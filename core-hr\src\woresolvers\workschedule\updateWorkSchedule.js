//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { UserInputError,ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../../../common/tablealias');
//Require constants
const { formName,systemLogs } = require('../../../common/appconstants');
//Require validation functions
const { validateAddUpdateWorkScheduleInputs,validateAndFormWSUpdateDetails,validateWSAndAttendance, validateWSAssociated } = require('../../../common/workScheduleValidation');

//Update the work schedule
module.exports.updateWorkSchedule = async (parent, args, context, info) => {
    console.log('Inside updateWorkSchedule() function.');
    let organizationDbConnection;
    let errResult;
    let validationError={};
    let wsAssociatedForms = '';
    try{
        let loginEmployeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        //Get the login employee - work schedule form access
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection,loginEmployeeId,formName.workSchedule,'','UI');
        //Check update rights exist or not                 
        if(Object.keys(checkRights).length>0 && checkRights.Role_Update === 1) {
            validationError = await validateAddUpdateWorkScheduleInputs(organizationDbConnection,args,0);
            //Check validation error exist or not
            if(Object.keys(validationError).length ===0){
                let workScheduleId = args.workScheduleId;

                //No need to validate if only workschedule name is updated
                //Validate and form the work schedule update details
                var workScheduleUpdateJson = await validateAndFormWSUpdateDetails(organizationDbConnection,args,loginEmployeeId,0);
                
                if(!args.onlyNameChanged){
                    //If the dashboard type is 'HRMSDASHBOARD'
                    if(args.dashboardType === 'HRMSDASHBOARD'){
                        /** 1. Validate the attendance exist or not for the non-shift roster employees.
                         * 2. Get the shift type associated with the work schedule and validate the
                         * shift is scheduled or not for that shift type.*/
                        await validateWSAndAttendance(organizationDbConnection,workScheduleId);
                    }
                }

                
                if(args.oldStatus == 'Active' && args.status == 'InActive'){
                    wsAssociatedForms = await validateWSAssociated(organizationDbConnection,workScheduleId,workScheduleUpdateJson.Title,args.dashboardType,'edit')
                }

                if(!wsAssociatedForms){
                    //Update the work schedule details
                    return(
                    organizationDbConnection(ehrTables.workSchedule)
                    .update(workScheduleUpdateJson)
                    .where('WorkSchedule_Id',workScheduleId)
                    .then(async(workScheduleUpdateResponse) => {
                        //If work schedule is updated
                        if(workScheduleUpdateResponse){
                            //Log message: Update Work Schedule  - 47
                            let systemLogParam = {
                                action: systemLogs.roleUpdate,
                                userIp: context.User_Ip,
                                employeeId: loginEmployeeId,
                                formName: formName.workSchedule,
                                trackingColumn: '',
                                organizationDbConnection: organizationDbConnection,
                                uniqueId: workScheduleId
                            };
                            //Call the function to add the system log for the work schedule update action
                            await commonLib.func.createSystemLogActivities(systemLogParam);
                            //Destroy DB connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            //Return success response
                            return {errorCode: '',message:'Work schedule updated successfully.'};
                        }else{
                            console.log('Work schedule is not updated for the work schedule id,',workScheduleId,'and the work schedule update response,',workScheduleUpdateResponse);
                            throw 'CWS0008';
                        }
                    })
                    .catch(function (workScheduleCatchError) {
                        console.log('Error in updateWorkSchedule() function work schedule .catch() block', workScheduleCatchError);
                        errResult = commonLib.func.getError(workScheduleCatchError, 'CWS0105');
                        //Destroy DB connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        throw new ApolloError(errResult.message,errResult.code);//return response
                    })
                    )
                }else{
                    throw 'CWS0010';
                }
            }else{
                throw 'IVE0000';
            }
        }
        else{
            console.log('Login employee id does not have update access to work schedule form.');
            throw ('_DB0102');
        }
    }catch(updateWorkScheduleMainCatchErr) {
        console.log('Error in the updateWorkSchedule() function main catch block. ',updateWorkScheduleMainCatchErr, ' and wsAssociatedForms: ',wsAssociatedForms);
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        //If validation error exist
        if (updateWorkScheduleMainCatchErr === 'IVE0000') {
            console.log('Validation error in the updateWorkSchedule() function',validationError);
            errResult = commonLib.func.getError('', 'IVE0000');
            throw new UserInputError(errResult.message, { validationError: validationError });//return response
        } else if (updateWorkScheduleMainCatchErr == 'CWS0010'){
            throw new ApolloError('Unable to update work schedule as it is associated in '+wsAssociatedForms+' forms.',updateWorkScheduleMainCatchErr);//return response
        }else{
            errResult = commonLib.func.getError(updateWorkScheduleMainCatchErr, 'CWS0009');
            throw new ApolloError(errResult.message,errResult.code);//return response
        }
    }
};