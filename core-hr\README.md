# Core-hr

# Development

```
Install serverless in your system,
npm install -g serverless

Go to the root path of the project and run the commands,
npm install .


Start the offline server by using the command,
serverless offline start

Start working on the code. To invoke the microservices locally,
sls invoke local --function functionName

To invoke the microservices locally by using the API url,
For example, http://localhost:3000/getsecret

If any other new microservices added, configure the microservices in function section of serverless.yml file refer the below link,
https://serverless.com/framework/docs/providers/aws/guide/functions/


If any new secure keys want to use in project, then keep the keys in secrets manager

```

# Production

```
If any other packages additionally used for the development, add the package names in  package.json file and the packages should be OS independent


```
