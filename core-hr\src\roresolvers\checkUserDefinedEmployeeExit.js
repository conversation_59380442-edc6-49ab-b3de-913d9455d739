//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { UserInputError,ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../../common/tablealias');
//Require constants
const { formName } = require('../../common/appconstants');

module.exports.checkUserDefinedEmployeeExit = async (parent, args, context, info) => {
    let validationError = {};
    let organizationDbConnection;
    try{
        console.log("Inside checkUserDefinedEmployeeExit function()",args)
        let userDefinedEmpId=args.userDefinedEmpId;
        let employeeId=args.employeeId;
        if(userDefinedEmpId && commonLib.commonValidation.alphaNumSpCDotHySlashValidation(userDefinedEmpId))
        {
            // get the organization database connection
            organizationDbConnection = knex(context.connection.OrganizationDb);
            let subQuery= organizationDbConnection(ehrTables.empJob)
            .pluck('User_Defined_EmpId')
            .where('User_Defined_EmpId',userDefinedEmpId);
            if(employeeId)
            {
                subQuery=subQuery.whereNot('Employee_Id',employeeId)
            }
            return(
                subQuery
                .then(data=>{
                    if(data.length>0)
                    {
                        return { errorCode:'',message:'Employee Id already exist.'};
                    }
                    else{
                        return { errorCode:'',message:'Employee Id do not exist.'};
                    }
                })
                .catch(e=>{
                    console.log('Error in the checkUserDefinedEmployeeExit() function .catch block.',e);
                    throw('CDG0129');
                })
            )
        }
        else{
            validationError['IVE0083'] = commonLib.func.getError('', 'IVE0083').message;
            throw('IVE0000')
        }
    }
    catch(e)
    {
        console.log('Error in the checkUserDefinedEmployeeExit() function catch block. ',e);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        if (e === 'IVE0000') {
            errResult = commonLib.func.getError('', 'IVE0000');
            // return response
            throw new UserInputError(errResult.message, { validationError: validationError });
        }else {
            errResult = commonLib.func.getError(e, 'CDG0129');
            // return response
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
}