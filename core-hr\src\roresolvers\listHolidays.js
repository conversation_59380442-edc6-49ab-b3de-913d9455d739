// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formName } = require('../../common/appconstants');

let organizationDbConnection;
module.exports.listHolidays = async (parent, args, context, info) => {
    try {
        console.log("Inside listHolidays function.")
        let employeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        
        // check their rights
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, employeeId, formName.holidays, '', 'UI');
        if (Object.keys(checkRights).length > 0 && checkRights.Role_View === 1) {
            return (
                await organizationDbConnection(ehrTables.holidayAssignment)
                .select("HA.Holiday_Id", "HA.Holiday_Name", "HA.Description", "HA.Added_On", "HA.Updated_On", organizationDbConnection.raw("CONCAT_WS(' ',EPI.Emp_First_Name,EPI.Emp_Middle_Name, EPI.Emp_Last_Name) as addedByName"), organizationDbConnection.raw("CONCAT_WS(' ',EPI2.Emp_First_Name,EPI2.Emp_Middle_Name, EPI2.Emp_Last_Name) as updatedByName"))
                .leftJoin(ehrTables.empPersonalInfo + " as EPI", "EPI.Employee_Id", "HA.Added_By")
                .leftJoin(ehrTables.empPersonalInfo + " as EPI2", "EPI2.Employee_Id", "HA.Updated_By")
                .from(ehrTables.holiday + " as HA")
                    .then((data) => {
                        //destroy the connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        return { errorCode: "", message: "Holiday details retrieved successfully.", listHolidays: data };
                    })
                    .catch((err) => {
                        console.log('Error in listHolidays .catch() block', err);
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        let errResult = commonLib.func.getError(err, 'CGH0112');
                        throw new ApolloError(errResult.message, errResult.code);
                    })
            )
        }
        else {
            throw '_DB0100';
        }

    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in listHolidays function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'CGH0004');
        throw new ApolloError(errResult.message, errResult.code);
    }
}
