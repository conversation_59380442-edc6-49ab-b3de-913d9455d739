// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');

let organizationDbConnection;
module.exports.listLanguages = async (parent, args, context, info) => {
    try {
        console.log("Inside listLanguages function.")
        organizationDbConnection = knex(context.connection.OrganizationDb);
        return (
             organizationDbConnection(ehrTables.languages)
             .orderBy('Language_Name', 'asc')
                .select("*")
                .then((data) => {
                    //destroy the connection
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    return { errorCode: "", message: "Languages retrieved successfully.", languages: data };
                })
                .catch((err) => {
                    console.log('Error in listLanguages .catch() block', err);
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    let errResult = commonLib.func.getError(err, 'CCH0101');
                    throw new ApolloError(errResult.message, errResult.code);
                })
        )
    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in listLanguages function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'CCH0001');
        throw new ApolloError(errResult.message, errResult.code);
    }
}
