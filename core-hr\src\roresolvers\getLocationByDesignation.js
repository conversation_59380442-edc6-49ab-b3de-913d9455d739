//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require common table alias
const { ehrTables } = require('../../common/tablealias');

module.exports.getLocationByDesignation = async (parent, args, context, info) => {
    console.log('Inside getLocationByDesignation function');
    let organizationDbConnection;
    try {
        const { formId, designationId } = args;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        
        // Check access rights
        const checkRights = await commonLib.func.checkEmployeeAccessRights(
            organizationDbConnection,
            context.Employee_Id,
            '',
            '',
            'UI',
            false,
            formId
        );

        if (Object.keys(checkRights).length <= 0 || checkRights.Role_View !== 1) {
            throw '_DB0100';
        }

        const result = await organizationDbConnection(ehrTables.designation + ' as D')
            .select('L.Location_Id')
            .innerJoin(ehrTables.SFWPOrganizationStructure + ' as OS', 'OS.Pos_Code', 'D.Designation_Code')
            .innerJoin(ehrTables.location + ' as L', 'L.Location_Code', 'OS.Lst_Work_Location')
            .where('D.Designation_Id', designationId).first();

        return {
            errorCode: '',
            message: 'Location details retrieved successfully',
            locationId: result ? result.Location_Id : 0
        };

    } catch (e) {
        console.log('Error in getLocationByDesignation function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'SET0006');
        throw new ApolloError(errResult.message, errResult.code);
    } finally {
        // Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
    }
};