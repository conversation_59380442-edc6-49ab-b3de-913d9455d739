// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');

let organizationDbConnection;
module.exports.listDocumentSubType = async (parent, args, context, info) => {
    try {
        console.log("Inside listDocumentSubType function.")
        organizationDbConnection = knex(context.connection.OrganizationDb);
        return (
             organizationDbConnection(ehrTables.documentSubType)
                .select("*").where(function() {
                    this.orWhere('Mandatory', 'Yes')
                    this.orWhere('Only_For_Email', 'No');
                })
                .modify(function (queryBuilder) {
                    if (args.isDefault!== undefined && args.isDefault!== null) {
                        queryBuilder.where('Is_Default', args.isDefault)
                    }
                })
                .then((data) => {
                    //destroy the connection
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    return { errorCode: "", message: "Document sub types retrieved successfully.", documentSubType: data };
                })
                .catch((err) => {
                    console.log('Error in listDocumentSubType .catch() block', err);
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    let errResult = commonLib.func.getError(err, 'CCH0107');
                    throw new ApolloError(errResult.message, errResult.code);
                })
        )
    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in listDocumentSubType function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'CCH0007');
        throw new ApolloError(errResult.message, errResult.code);
    }
}
