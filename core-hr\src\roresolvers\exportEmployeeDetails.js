// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formName } = require('../../common/appconstants');


let organizationDbConnection;
let exportData = ""
module.exports.exportEmployeeDetails = async (parent, args, context, info) => {
  try {
    console.log("Inside exportEmployeeDetails function.")
    let employeeId = context.Employee_Id;
    let sortby = args.sortBy ? args.sortBy : 'Employee_Id';
    organizationDbConnection = knex(context.connection.OrganizationDb);
    let adminRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, employeeId, formName.admin, '', 'UI');
    let employeeAdminRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, employeeId, formName.teamSummary, '', 'UI');
    if ((adminRights && adminRights.Role_Update === 1) && (employeeAdminRights && employeeAdminRights.Role_View === 1)) {
      //Employee Job Details
      if (args.typeOfEmployeeDetails === 'Employee Job Details') {
        var query = organizationDbConnection(ehrTables.empJob + ' as EJ')
          .select(organizationDbConnection.raw("CONCAT_WS(' ',EPI.Emp_First_Name,EPI.Emp_Middle_Name,EPI.Emp_Last_Name) as Employee_Name"), "EPI.Employee_Id", "EJ.Designation_Id", "EJ.Department_Id", "EJ.Work_Schedule", "EJ.External_EmpId", "EJ.Location_Id", "EJ.Date_Of_Join", "EJ.Emp_Email", "EJ.Date_Of_Join", "EJ.Emp_Status", "EJ.Confirmation_Date", "EJ.Probation_Date", "EJ.User_Defined_EmpId", "EJ.Invitation_Status", "EJ.Invited_Time", "EJ.EmpType_Id", "EJ.Manager_Id", "EJ.Pf_PolicyNo", "EJ.Service_Provider_Id", "EJ.Global_Resource_Id", "EJAH.Designation_Id_End_Date", "EJAH.Department_Id_End_Date", "EJAH.Location_Id_End_Date", "EJAH.Manager_Id_End_Date", "EJAH.EmpType_Id_End_Date", "EJAH.Work_Schedule_End_Date")
          .innerJoin(ehrTables.empPersonalInfo + " as EPI", "EPI.Employee_Id", "EJ.Employee_Id")
          .leftJoin(ehrTables.empJobAuditHistory + " as EJAH", "EJAH.Employee_Id", "EJ.Employee_Id")
          .orderBy(sortby, 'asc')
          .from(ehrTables.empJob + " as EJ")
      }
      let subQuery = commonWhereFunction(query, args)
      let data = await subQuery
      if (data) {
        data = processEmployeeData(data)
        exportData = JSON.stringify(data)
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        return { errorCode: "", message: "Export employee details retrieved successfully.", exportEmployeeDetails: exportData };
      } else {
        throw 'EDM0101'
      }
    }
    else {
      throw '_DB0109';
    }

  }
  catch (err) {
    //Destroy DB connection
    organizationDbConnection ? organizationDbConnection.destroy() : null;
    console.log('Error in exportEmployeeDetails function main catch block.', err);
    let errResult = commonLib.func.getError(err, 'EDM0001');
    throw new ApolloError(errResult.message, errResult.code);
  }
}

//common filter function
async function commonWhereFunction(query, args) {
  let subqQuery = query.where(function () {
    if (args.filterBy !== 'All') {
      switch (args.filterBy) {
        case 'Active Employees':
          this.where('EJ.Emp_Status', 'Active')
          break

        case 'Inactive Employees':
          this.where('EJ.Emp_Status', 'InActive')
          break

        case 'Portal Enabled Employees':
          this.where('EPI.Allow_User_Signin', '1')
          break

        case 'Portal Disabled Employees':
          this.where('EPI.Allow_User_Signin', '0')
          break
      }
    }
    if (args.location && args.location.length) {
      this.whereIn('EJ.Location_Id', args.location)
    }

    if (args.designation && args.designation.length) {
      this.whereIn('EJ.Designation_Id', args.designation)
    }

    if (args.department && args.department.length) {
      this.whereIn('EJ.Department_Id', args.department)
    }

  })
  return subqQuery
}

// Function to get the processed record
function processEmployeeData(employeeData) {
  // Create an object to store the last end dates for each property
  const lastEndDates = {};

  // Iterate over the employeeData array
  for (const employee of employeeData) {
    // Get the employee ID
    const employeeId = employee.Employee_Id;

    // Update the last end dates for each property
    if (employee.Designation_Id_End_Date) {
      const existingEndDate = lastEndDates[employeeId]?.Designation_Id_End_Date;
      const currentEndDate = employee.Designation_Id_End_Date;

      if (!existingEndDate || currentEndDate > existingEndDate) {
        lastEndDates[employeeId] = {
          ...lastEndDates[employeeId],
          Designation_Id_End_Date: currentEndDate
        };
      }
    }

    if (employee.Department_Id_End_Date) {
      const existingEndDate = lastEndDates[employeeId]?.Department_Id_End_Date;
      const currentEndDate = employee.Department_Id_End_Date;

      if (!existingEndDate || currentEndDate > existingEndDate) {
        lastEndDates[employeeId] = {
          ...lastEndDates[employeeId],
          Department_Id_End_Date: currentEndDate
        };
      }
    }

    if (employee.Location_Id_End_Date) {
      const existingEndDate = lastEndDates[employeeId]?.Location_Id_End_Date;
      const currentEndDate = employee.Location_Id_End_Date;

      if (!existingEndDate || currentEndDate > existingEndDate) {
        lastEndDates[employeeId] = {
          ...lastEndDates[employeeId],
          Location_Id_End_Date: currentEndDate
        };
      }
    }

    if (employee.Work_Schedule_End_Date) {
      const existingEndDate = lastEndDates[employeeId]?.Work_Schedule_End_Date;
      const currentEndDate = employee.Work_Schedule_End_Date;

      if (!existingEndDate || currentEndDate > existingEndDate) {
        lastEndDates[employeeId] = {
          ...lastEndDates[employeeId],
          Work_Schedule_End_Date: currentEndDate
        };
      }
    }

    if (employee.Manager_Id_End_Date) {
      const existingEndDate = lastEndDates[employeeId]?.Manager_Id_End_Date;
      const currentEndDate = employee.Manager_Id_End_Date;

      if (!existingEndDate || currentEndDate > existingEndDate) {
        lastEndDates[employeeId] = {
          ...lastEndDates[employeeId],
          Manager_Id_End_Date: currentEndDate
        };
      }
    }

    if (employee.EmpType_Id_End_Date) {
      const existingEndDate = lastEndDates[employeeId]?.EmpType_Id_End_Date;
      const currentEndDate = employee.EmpType_Id_End_Date;

      if (!existingEndDate || currentEndDate > existingEndDate) {
        lastEndDates[employeeId] = {
          ...lastEndDates[employeeId],
          EmpType_Id_End_Date: currentEndDate
        };
      }
    }

    // Check if the employee has any end date property
    const hasEndDateProperty = Object.keys(employee)
      .some(key => key.endsWith('_End_Date') && employee[key]);

    if (!hasEndDateProperty) {
      // If the employee does not have any end date property,
      // add an entry with null values to lastEndDates
      lastEndDates[employeeId] = {
        ...lastEndDates[employeeId],
        Designation_Id_End_Date: null,
        Department_Id_End_Date: null,
        Location_Id_End_Date: null,
        Work_Schedule_End_Date: null,
        Manager_Id_End_Date: null,
        EmpType_Id_End_Date: null
      };
    }
  }

  // Create an array to store the final output
  const output = [];

  // Iterate over the lastEndDates object and create the final output
  for (const employeeId in lastEndDates) {
    const lastEndDatesObject = lastEndDates[employeeId];

    // Find the corresponding employee object in the employeeData array
    const employeeObject = employeeData.find(
      employee => employee.Employee_Id === parseInt(employeeId)
    );

    // Create the final object by merging the employee object and last end dates object
    const finalObject = { ...employeeObject, ...lastEndDatesObject };

    // Push the final object to the output array
    output.push(finalObject);
  }

  return output;
}



