// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');

let organizationDbConnection;
module.exports.listDocumentType = async (parent, args, context, info) => {
    try {
        console.log("Inside listDocumentType function.")
        organizationDbConnection = knex(context.connection.OrganizationDb);
        return (
             organizationDbConnection(ehrTables.documentType)
                .select("*")
                .then((data) => {
                    //destroy the connection
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    return { errorCode: "", message: "Document types retrieved successfully.", documentType: data };
                })
                .catch((err) => {
                    console.log('Error in listDocumentType .catch() block', err);
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    let errResult = commonLib.func.getError(err, 'CCH0106');
                    throw new ApolloError(errResult.message, errResult.code);
                })
        )
    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in listDocumentType function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'CCH0006');
        throw new ApolloError(errResult.message, errResult.code);
    }
}
