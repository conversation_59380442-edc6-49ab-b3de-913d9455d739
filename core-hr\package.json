{"name": "<PERSON><PERSON>", "version": "1.0.0", "description": "core-hr node modules", "main": "rohandler.js", "dependencies": {"@cksiva09/hrapp-corelib": "git+https://cksiva09:<EMAIL>/cksiva09/hrapp-corelib.git", "@cksiva09/validationlib": "^1.4.0", "apollo-server": "^2.26.0", "apollo-server-lambda": "^2.15.0", "axios": "^1.1.2", "graphql": "^15.1.0", "knex": "2.3.0", "moment": "^2.29.1", "mysql": "^2.18.0", "path": "^0.12.7", "save": "^2.9.0", "serverless-domain-manager": "^7.1.2", "serverless-prune-plugin": "^2.0.2", "serverless-step-functions": "^3.17.0", "xlsx": "^0.18.5"}, "devDependencies": {"eslint": "^6.5.1", "serverless-offline": "^13.2.0"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "sls offline --stage dev --region ap-south-1 --reload<PERSON>andler", "local": "sls offline --stage local --region ap-south-1 --reload<PERSON>andler"}, "author": "<PERSON><PERSON><PERSON>", "license": "ISC"}