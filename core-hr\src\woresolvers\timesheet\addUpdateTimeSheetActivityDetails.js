const { UserInputError, ApolloError } = require('apollo-server-lambda');
const { formName, systemLogs, formIds } = require('../../../common/appconstants');
const { ehrTables } = require('../../../common/tablealias');
const { insertDataInTable,getTimesheetInstanceData,getTimeSheetEventId} = require('../../../common/commonfunctions');
const moment = require('moment');
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const { validateTimeSheetActivityInputs } = require('../../../common/inputValidations');
// require knex
const knex = require('knex');
module.exports.addUpdateempTimesheet = async (parent, args, context) => {
    let validationError={}
    try {
        const loginEmployee_Id = context.Employee_Id;
        let orgCode=context.Org_Code;
        const formId = args.selfService === 1 ? formIds.timeSheet : formIds.timeSheetMyTeam;
        let projectFormId=formIds.projectSettings;
        const organizationDbConnection = knex(context.connection.OrganizationDb);
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployee_Id, '', '', 'UI', false, formId);
        if(args.selfService === 0 && Object.keys(checkRights).length >= 0 && checkRights.Is_Manager !== 1 && checkRights.Employee_Role.toLowerCase() !== 'admin'){
            throw '_DB0114'
        }
        if(Object.keys(checkRights).length <= 0 || (args.requestId && checkRights.Role_Update === 0)||(!args.requestId && checkRights.Role_Add === 0)){
            
            if(!args.requestId && checkRights.Role_Add === 0){
                throw '_DB0101';
            }
            else{
                throw "_DB0102";
            }
            
          }
        const { timesheetId,requestId, employeeId, weekEndingDate, projectId, timesheetType, projectActivityId,dayValue, dayDetails,description,Approval_Status} = args;
        let empTimesheetData = {
            "Employee_Id": employeeId,
            "Week_Ending_Date": weekEndingDate
        };
        let timesheetHoursTrackingData={
            "Project_Id":projectId,
            "Timesheet_Type": timesheetType,
            "Project_Activity_Id": projectActivityId,
        }
        timesheetHoursTrackingData['Day' + dayValue] = args['day' + dayValue];
        const fieldValidations = {};
        if(description){
            fieldValidations.description = "IVE0415";
        }
        if(dayDetails && dayDetails.length){
            validationError = await validateTimeSheetActivityInputs(dayDetails,weekEndingDate,description,fieldValidations);
        }
        if (Object.keys(validationError).length !== 0) {
            throw 'IVE0000';
        }

        const systemLogParams = {
            userIp: context.User_Ip,
            employeeId: loginEmployee_Id,
            formName: formName.timeSheet,
            trackingColumn: 'Request_Id',
            organizationDbConnection: organizationDbConnection,
            uniqueId: requestId
        };
        let updateResult;
        let updateTimesheetHoursResult;
        return(
        await organizationDbConnection.transaction(async (trx) => {
            if(requestId===0){
                 let existingMainRecord=await organizationDbConnection(ehrTables.empTimesheet)
                .transacting(trx)
                .where("Week_Ending_Date",weekEndingDate).whereIn("Approval_Status", ["Applied", "Draft", 'Approved','Returned'])
                .where('Employee_Id',employeeId);
                if(existingMainRecord && existingMainRecord.length > 0){
                    throw 'CHR0125'
                }
                else{
                    empTimesheetData.Approval_Status = 'Draft' 
                }
            
                
            }
            else if(requestId!==0 && timesheetId===0){
                let existingRecordSameProjectAndActivity=
                await organizationDbConnection(ehrTables.timesheetHoursTracking)
                .transacting(trx)
                .where("Request_Id", requestId).andWhere('Project_Id',projectId).andWhere('Project_Activity_Id',projectActivityId).select('*');
                if(existingRecordSameProjectAndActivity && existingRecordSameProjectAndActivity.length > 0){
                    throw 'CHR0090'
                }
            }
           
            if(timesheetId!==0){
                let existingRecord=await organizationDbConnection(ehrTables.timesheetHoursTracking)
                    .transacting(trx)
                    .where("Timesheet_Id", timesheetId).select('*');
                    if(!existingRecord || existingRecord.length <= 0){
                        throw 'CHR0093'
                    }
            }
            if(timesheetId!==0 && requestId!==0 && timesheetSetting(organizationDbConnection,trx)){
            await checkForDuplicate(dayDetails,dayValue,organizationDbConnection,trx,timesheetId);
            }
            if (timesheetId) {
                timesheetHoursTrackingData.Updated_On = moment().utc().format('YYYY-MM-DD HH:mm:ss');
                timesheetHoursTrackingData.Updated_By = loginEmployee_Id;
            } else {
                timesheetHoursTrackingData.Added_Date = moment().utc().format('YYYY-MM-DD HH:mm:ss');
                timesheetHoursTrackingData.Added_By = loginEmployee_Id;
            }
            let empTimesheetQuery = requestId!==0
            ? await organizationDbConnection(ehrTables.empTimesheet)
                .transacting(trx)
                .where("Request_Id",requestId)
                .update(empTimesheetData)
            : await organizationDbConnection(ehrTables.empTimesheet)
                .transacting(trx)
                .insert(empTimesheetData);

                updateResult = await empTimesheetQuery;
                if (!updateResult) {
                    throw 'CHR0082';
                }
                requestId?timesheetHoursTrackingData.Request_Id=requestId:timesheetHoursTrackingData.Request_Id=updateResult[0];
            let timesheetHoursTrackingQuery = timesheetId
                ? await organizationDbConnection(ehrTables.timesheetHoursTracking)
                    .transacting(trx)
                    .where("Timesheet_Id", timesheetId)
                    .update(timesheetHoursTrackingData)
                : await organizationDbConnection(ehrTables.timesheetHoursTracking)
                    .transacting(trx)
                    .insert(timesheetHoursTrackingData);
                    updateTimesheetHoursResult = await timesheetHoursTrackingQuery;
                    if (!updateTimesheetHoursResult) throw 'CHR0092';
            const deleteActivityDetails = timesheetId !== 0 ? await commonLib.func.deteleDataWithTrx(organizationDbConnection, ehrTables.activityDetailsBytime, trx,"Timesheet_Id",timesheetId,dayValue,'Day') : null;
            if(dayDetails && dayDetails.length){
            await Promise.all([
                deleteActivityDetails,
                ...dayDetails.map(async (element) => {
                    let activityData = [{ "Timesheet_Id": timesheetId?timesheetId:updateTimesheetHoursResult[0], "Day": dayValue, "Notes": element.notes ,"Total_Hours":element.totalHours,"Start_Time":element.startTime,"End_Time":element.endTime, "Room_Id": element.roomId}];
                    return insertDataInTable(organizationDbConnection, trx, activityData, ehrTables.activityDetailsBytime);
                })
          
            ])
        }
        let instanceData =  await getTimesheetInstanceData(organizationDbConnection,args,trx);
        if(requestId!==0 && Object.keys(instanceData).length>0 && instanceData.Approval_Status.toLowerCase()==='applied'){
        const eventId= await getTimeSheetEventId(organizationDbConnection);
        let oldInstanceId=instanceData.Process_Instance_Id;
        delete instanceData.Process_Instance_Id;
        let initiateTimesheetWorkflow = await commonLib.func.initiateWorkflow(eventId, instanceData, orgCode,projectFormId,loginEmployee_Id);
        if (initiateTimesheetWorkflow.status == 200 && initiateTimesheetWorkflow.data && initiateTimesheetWorkflow.data.workflowProcessInstanceId) {
            await commonLib.func.deleteOldApprovalRecords(organizationDbConnection,oldInstanceId,trx);
            let updateParam = { Process_Instance_Id: initiateTimesheetWorkflow.data.workflowProcessInstanceId };
            await commonLib.leaveCommonFunction.updateTableWithTrxBasedOnCondition(organizationDbConnection, ehrTables.empTimesheet, updateParam,trx,'Request_Id','=',args.requestId,"","");
        }

        }

        })
            .then(async(result) => {
                await commonLib.func.createSystemLogActivities(systemLogParams);
                organizationDbConnection ? organizationDbConnection.destroy() : null;
    
            const successMessage = timesheetId ? "timesheet activity data details updated successfully." : "timesheet activity data details added successfully.";
            let optionsObject={}
            if(requestId===0 && updateResult && updateResult.length){
                optionsObject.requestId=updateResult[0]
            }
            if(timesheetId===0 && updateTimesheetHoursResult && updateTimesheetHoursResult.length){
                optionsObject.timesheetId=updateTimesheetHoursResult[0]
            }
            return { errorCode: "", message: successMessage,optionsObject};
            }).catch((error) => {
                                    //Destroy DB connection
                                  throw error;
            }));
        
    } catch (error) {
        if (error === 'IVE0000') {
            console.log('Validation error in the addUpdateempTimesheet  function', validationError);
            errResult = commonLib.func.getError('', 'IVE0000');
            throw new UserInputError(errResult.message, { validationError: validationError });//Return error response
        }
        else {
            let errorCode = error.code === 'ER_DUP_ENTRY'?'CHR0090': error
            errResult = commonLib.func.getError(errorCode, 'CHR0091');
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
};
async function checkForDuplicate(dayDetails, dayValue, organizationDbConnection, trx, timesheetId) {
    try {
        if (dayDetails && dayDetails.length) {
            const data = await organizationDbConnection(ehrTables.activityDetailsBytime)
                .transacting(trx)
                .where('Day', dayValue)
                .where('Timesheet_Id', timesheetId)
                .select('Start_Time', 'End_Time', 'Day', 'Details_Bytime_Id')
                .orderBy('Details_Bytime_Id', 'asc');
            if (data && data.length) {
                    for (const item of dayDetails) {

                    for (const element of data) {
                        if (item.detailsBytimeId === element.Details_Bytime_Id  || (!item.startTime || !item.endTime || (item.endTime==='00:00:00'&& item.startTime==='00:00:00'))) {
                            continue;
                        } else if (filterdate(element, item)) {
                            console.log("Duplicate record exists");
                            throw 'CHR0089';
                        }
                      
                    }
                }
                
            }
        }
    } catch (insertError) {
        console.log('Error in checkForDuplicate main catch block', insertError);
        throw 'CHR0089';
    }
}


// The filterdate function and other necessary functions remain unchanged
function timeToDateMoment(timeString) {
    const currentDate = new Date().toISOString().slice(0, 10);
    const dateTimeString = `${currentDate} ${timeString}`;
    return moment(dateTimeString).utc().format('YYYY-MM-DD HH:mm:ss');
}
function filterdate(data,item){
    const startTime = timeToDateMoment(data.Start_Time);
    const endTime = timeToDateMoment(data.End_Time);
    const filterStartTimeObj = timeToDateMoment(item.startTime);
    const filterEndTimeObj = timeToDateMoment(item.endTime);
    if((startTime === filterStartTimeObj || endTime === filterEndTimeObj)||(startTime <= filterStartTimeObj && endTime >= filterEndTimeObj)){
        return true;
    }
    else{
        return false;
    }
}
async function timesheetSetting(organizationDbConnection,trx){
    const data = await organizationDbConnection(ehrTables.projectSettings)
                .transacting(trx)
                .pluck('Present_Time_Slot').then((data)=>{
                   if(data[0].toLowerCase()==='yes'){
                    return true;
                   }
                   else{
                    return false;
                   }
                }).catch((err)=>{
                    throw err;
                })

}


