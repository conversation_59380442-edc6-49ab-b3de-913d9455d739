// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formIds, systemLogs, formName } = require('../../../../common/appconstants');
//require moment
const moment = require('moment');

//function to delete the room
let organizationDbConnection;
module.exports.deleteRoom = async (parent, args, context, info) => {
    try {
        console.log("Inside deleteRoom function.")
        let employeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);

        // check their rights
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, employeeId, null, 'Role_Delete', 'Back-end', null, formIds.room);
        if (!checkRights) {
            throw '_DB0103';
        }

        //Validate association
        let associatedWithActivity = await organizationDbConnection(ehrTables.activityDetailsBytime)
            .select('Room_Id')
            .where('Room_Id', args.Room_Id)
            .first();

        if (associatedWithActivity) {
            throw 'CHR0139'
        }

        let updateSystemLog = await organizationDbConnection.transaction(async (trx) => {
            //Archive deleting room
            let archiveRoom = await organizationDbConnection(ehrTables.room)
                .where('Room_Id', args.Room_Id)
                .transacting(trx)
                .first()

            if (archiveRoom) {
                //Delete room details and insert the archive
                await Promise.all([
                    organizationDbConnection(ehrTables.room)
                        .where('Room_Id', args.Room_Id)
                        .transacting(trx)
                        .del(),
                    organizationDbConnection(ehrTables.roomArchive)
                        .insert({
                            Room_Id: archiveRoom.Room_Id,
                            Room_No: archiveRoom.Room_No,
                            Description: archiveRoom.Description,
                            Deleted_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                            Deleted_By: employeeId
                        })
                        .transacting(trx)
                ])
                return true
            }
            return false
        })

        if (updateSystemLog) {
            // Add System Log
            let systemLogParams = {
                action: systemLogs.roleDelete,
                userIp: context.User_Ip,
                employeeId: employeeId,
                formName: formName.room,
                organizationDbConnection: organizationDbConnection,
                message: `Room Id ${args.Room_Id} was deleted successfully.`
            };

            //Call function to add the system log
            await commonLib.func.createSystemLogActivities(systemLogParams);
        }


        return { errorCode: "", message: "Room details deleted successfully." };

    }
    catch (e) {
        console.log('Error in deleteRoom function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'CHR00108');
        throw new ApolloError(errResult.message, errResult.code);
    }
    finally {
        //Destroy DB connection
        if (organizationDbConnection) {
            organizationDbConnection.destroy();
        }
    }
}
