const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const knex = require('knex');
const { ehrTables } = require('../../../common/tablealias');
const { ApolloError } = require('apollo-server-lambda');
const { formIds } = require('../../../common/appconstants');
const { getExistingTimesheetData } = require('../../../common/commonfunctions');
// this will be called from approval management when the approver returns the record with some comments
module.exports.timesheetApprovalReturn = async (parent, args, context, info) => {
  let organizationDbConnection;
  try {
    console.log("Inside timesheetApprovalReturn function.");
    let employeeId = context.Employee_Id;
    organizationDbConnection = knex(context.connection.OrganizationDb);
      //Update the data
      await organizationDbConnection.transaction(async (trx) => {
        let existingData=await getExistingTimesheetData(organizationDbConnection,trx,args);
          if(!existingData ||existingData.length<=0){
            throw 'CHR0120'
          }
          if( !existingData[0].Process_Instance_Id){
            throw 'CHR0127'
          }
        await commonLib.func.deleteOldApprovalRecords(organizationDbConnection, existingData[0].Process_Instance_Id, trx);
        let updateParam = { Process_Instance_Id: null,Approval_Status: "Returned",Returned_Comment: args.returnedComment };
        await commonLib.leaveCommonFunction.updateTableWithTrxBasedOnCondition(organizationDbConnection, ehrTables.empTimesheet, updateParam, trx, 'Request_Id', '=', args.requestId, "", "");
        

      }).then(async () => {
        let systemLogParam = {
          userIp: context.User_Ip,
          employeeId: employeeId,
          organizationDbConnection: organizationDbConnection,
          message: ` The timesheet request id ${args.requestId} is being returned.`,
        };
        await commonLib.func.createSystemLogActivities(systemLogParam);

        organizationDbConnection ? organizationDbConnection.destroy() : null;
        return { errorCode: "", message: `The timesheet is being returned successfully.` };
      }).catch((err) => {
        console.log("Error in timesheetApprovalReturn function .catch block.",err)
        throw 'CHR0124';
      })
    
  } catch (e) {
    //Destroy DB connection
    organizationDbConnection ? organizationDbConnection.destroy() : null;
    console.log("Error in timesheetApprovalReturn function main catch block.", e);

    let errResult = commonLib.func.getError(e, "CHR00102");
    throw new ApolloError(errResult.message, errResult.code);
  }
};
