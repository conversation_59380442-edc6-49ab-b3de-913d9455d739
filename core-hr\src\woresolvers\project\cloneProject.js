
//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../../../common/tablealias');
const { formName, formIds, systemLogs } = require('../../../common/appconstants');
const moment = require('moment');
const {getProjectCoverage, insertDataInTable}=require('../../../common/commonfunctions')

//function to clone the project
module.exports.cloneProject = async (parent, args, context) => {
    console.log('Inside cloneProject function');
    let organizationDbConnection;
    let oldProjectId = args.projectId
    try {
        let loginEmployeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, null, '', 'UI', false, formIds.projects);

        if (Object.keys(checkRights).length > 0 && checkRights.Role_Add === 1) {
            const result = await organizationDbConnection
                .transaction(async function (trx) {
                    let projectCoverage = await getProjectCoverage(organizationDbConnection);

                    // cloned project info
                    let getClonedProjectData = await insertIntoProjectDetails(organizationDbConnection, loginEmployeeId, oldProjectId, args)
                    const newProjectId = getClonedProjectData.Project_Id;

                    if(args.cloneOption && (args.cloneOption.toLowerCase() === 'project and activity' || args.cloneOption.toLowerCase() === 'course and activity')){
                      await cloneProjectActivities(organizationDbConnection,loginEmployeeId,trx, oldProjectId, newProjectId)
                    }

                    let getAccreditationCategoryAndTypeIdData = [];
                    getAccreditationCategoryAndTypeIdData = await commonLib.func.getDataFromTableAccordingToStatus(organizationDbConnection,ehrTables.projectAccreditationCategoryTypeMapping,'Project_Id',oldProjectId, ['Project_Id','Accreditation_Category_And_Type_Id'])
                    if(getAccreditationCategoryAndTypeIdData && getAccreditationCategoryAndTypeIdData.length){
                        const accreditationCategoryAndTypeIds = getAccreditationCategoryAndTypeIdData.map(obj => obj.Accreditation_Category_And_Type_Id);
                        let accreditationCategoryAndTypeIdData = getAccreditationCategoryAndTypeIdData.map(obj => ({
                            ...obj,
                            Project_Id: newProjectId,
                          }));
                        await insertDataInTable(organizationDbConnection, trx, accreditationCategoryAndTypeIdData, ehrTables.projectAccreditationCategoryTypeMapping);
                        getClonedProjectData.Accreditation_Id = accreditationCategoryAndTypeIds
                    }

                    if(projectCoverage === "Employee"){
                        let getEmployeeData = [];
                        getEmployeeData = await commonLib.func.getDataFromTableAccordingToStatus(organizationDbConnection,ehrTables.empProject,'Project_Id',oldProjectId,  ['Employee_Id','Project_Id'])
                        if(getEmployeeData && getEmployeeData.length){
                            const employeeIds = getEmployeeData.map(obj => obj.Employee_Id);
                            let employeeData = getEmployeeData.map(obj => ({
                                ...obj,
                                Project_Id: newProjectId,
                              }));
                            await insertDataInTable(organizationDbConnection, trx, employeeData, ehrTables.empProject);
                            getClonedProjectData.Employee_Id = employeeIds
                        }
                    }
                    if(projectCoverage === "CUSTOMGROUP"){
                        let getCustomGroupIds = [];
                        getCustomGroupIds = await commonLib.func.getDataFromTableAccordingToStatus(organizationDbConnection,ehrTables.customGroupAssociated,'Parent_Id',oldProjectId, 'Form_Id', formIds.projects, ['Parent_Id','Form_Id','Custom_Group_Id'])
                        if(getCustomGroupIds && getCustomGroupIds.length){
                            const customGroupIds = getCustomGroupIds.map(obj => obj.Custom_Group_Id);
                            let customGroupIdData = getCustomGroupIds.map(obj => ({
                                ...obj,
                                Parent_Id: newProjectId,
                                Form_Id: formIds.projects 
                            }));
                        await insertDataInTable(organizationDbConnection, trx, customGroupIdData, ehrTables.customGroupAssociated);
                        getClonedProjectData.Custom_Group_Id = customGroupIds
                        }
                    }
                    let oldProjectName = getClonedProjectData.Project_Name.split('Clone')[1];
                    let systemLogParam = { action: systemLogs.roleAdd, userIp: context.User_Ip, employeeId: loginEmployeeId, formName: formName.projects, trackingColumn: '', organizationDbConnection, uniqueId: oldProjectId, message: `${getClonedProjectData.Project_Name}-${newProjectId} was cloned from ${oldProjectName}-${oldProjectId}` };

                    // Call the function to add the system log
                    await commonLib.func.createSystemLogActivities(systemLogParam);
                    return { errorCode: "", message: "Project cloned successfully.", projectDetails:getClonedProjectData };
                });

            return result

        } else {
            console.log('No rights to clone Project details');
            throw '_DB0111';
        }
    } catch (mainCatchError) {
        console.log('Error in cloneProject function main block', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        let errResult = commonLib.func.getError(mainCatchError, 'CHR0087');
        // return response
        throw new ApolloError(errResult.message, errResult.code);
    }
}

async function insertIntoProjectDetails(organizationDbConnection,loginEmployeeId, oldProjectId, args) {
    try {
        let getProjectData = await commonLib.func.getDataFromTableAccordingToStatus(organizationDbConnection,ehrTables.projectDetails,'Project_Id',oldProjectId)
        getProjectData = getProjectData[0]
        let newProjectName = args.projectName;
        getProjectData.Project_Name = newProjectName;
        getProjectData.Project_Id = 0;
        getProjectData.Added_On = moment().utc().format('YYYY-MM-DD HH:mm:ss');
        getProjectData.Added_By = loginEmployeeId;
        getProjectData.Status = 'Open';
        let commonCheck = {
            "Project_Name": getProjectData.Project_Name,
        }

        // Check if the project name already exists
        let alreadyEmployeeExists = await checkProjectExists(organizationDbConnection, commonCheck)
        if (alreadyEmployeeExists) {
            //Throw project name already exists error
            throw 'CHR0118'
        }
        const newProjectId =  await commonLib.func.insertIntoTable(organizationDbConnection, ehrTables.projectDetails, getProjectData, 1)
        getProjectData.Project_Id = newProjectId[0];
        return getProjectData;

    } catch (err) {
        console.log('Error in insertIntoProjectDetails main() function', err);
        throw err
    }
}

async function cloneProjectActivities(organizationDbConnection,loginEmployeeId,trx, oldProjectId, newProjectId) {
    try {
        let getProjectActivityData = await commonLib.func.getDataFromTableAccordingToStatus(organizationDbConnection,ehrTables.projectActivities,'Project_Id',oldProjectId)
        if(getProjectActivityData && getProjectActivityData.length){
            let updatedArrayOfObjects = getProjectActivityData.map(obj => ({
                ...obj,
                Project_Activity_Id: 0,
                Project_Id: newProjectId,
                Added_Date: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                Added_By: loginEmployeeId
          }));
        await insertDataInTable(organizationDbConnection, trx, updatedArrayOfObjects, ehrTables.projectActivities);
        }
        return getProjectActivityData;

    } catch (err) {
        console.log('Error in cloneProjectActivities main() function', err);
        throw err
    }
}
async function checkProjectExists(organizationDbConnection, whereCondition) {
    try {
        return (
            organizationDbConnection(ehrTables.projectDetails)
                .select("*")
                .where(whereCondition)
                .andWhere("Status", "Open")
                .then((data) => {
                    if (data && data.length) {
                        return true;
                    } else {
                        return false
                    }
                })
        )
    } catch (err) {
        console.log('Error in checkProjectExists function', err);
        throw err
    }
}
