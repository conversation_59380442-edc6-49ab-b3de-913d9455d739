// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');

let organizationDbConnection;
module.exports.listInsuranceType = async (parent, args, context, info) => {
    try {
        console.log("Inside listInsuranceType function.")
        organizationDbConnection = knex(context.connection.OrganizationDb);
        if (args.type === 'insurance') {
            //Get the designation id
            let designationId = await getDesignationId(args.employeeId, organizationDbConnection, args.screenType);
            if (designationId) {
                return (
                    organizationDbConnection(ehrTables.insuranceType + " as IT")
                        .select("IT.InsuranceType_Id", "IT.Insurance_Name")
                        .innerJoin(ehrTables.insuranceTypeGrade + " as ITG", "ITG.InsuranceType_Id", "IT.InsuranceType_Id")
                        .innerJoin(ehrTables.insuranceGrade + " as IG", "IG.Insurance_Grade_Id", "ITG.Insurance_Grade_Id")
                        .innerJoin(ehrTables.designation + " as DES", "DES.Grade_Id", "IG.Grade_Id")
                        .where("IT.InsuranceType_Status", "Active")
                        .andWhere("DES.Designation_Id", designationId)
                        .orderBy("IT.Insurance_Name", "ASC")
                        .then((data) => {
                            if (data) {
                                data = JSON.stringify(data)
                                //destroy the connection
                                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                return { errorCode: "", message: "Insurance type retrieved successfully.", insuranceType: data };
                            } else {
                                throw 'CCH0023'
                            }
                        })
                        .catch((err)=>{
                            console.log('Error while retrieving insurance type', err)
                            //Destroy DB connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            console.log('Error in listInsuranceType function main catch block.', e);
                            let errResult = commonLib.func.getError(e, 'CCH0023');
                            throw new ApolloError(errResult.message, errResult.code);
                        })
                )
            } else {
                throw 'CCH0022'
            }
        } else {
            return (
                organizationDbConnection(ehrTables.fixedHealthInsurance + " as FHI")
                    .select("FHI.Insurance_Type_Id as InsuranceType_Id", "FHT.Title as Insurance_Name")
                    .innerJoin(ehrTables.fixedHealthInsuranceType + " as FHT", "FHT.Insurance_Type_Id", "FHI.Insurance_Type_Id")
                    .where("FHI.Employee_Id", args.employeeId)
                    .where("FHI.Insurance_Status", "Active")
                    .orWhere("FHI.Coverage", 0)
                    .where("FHI.Insurance_Status", "Active")
                    .then((data) => {
                        if (data) {
                            data = JSON.stringify(data)
                            //destroy the connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            return { errorCode: "", message: "Fixed health insurance type retrieved successfully.", insuranceType: data };
                        }else{
                            throw 'CCH0023'
                        }
                    })
                    .catch((err)=>{
                        console.log('Error while retrieving fixed insurance', err)
                        //Destroy DB connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        console.log('Error in listInsuranceType function main catch block.', e);
                        let errResult = commonLib.func.getError(e, 'CCH0023');
                        throw new ApolloError(errResult.message, errResult.code);
                    })
            )
        }
    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in listInsuranceType function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'CCH0023');
        throw new ApolloError(errResult.message, errResult.code);
    }
}

async function getDesignationId(employeeId, organizationDbConnection, screenType) {
    try {
        if(screenType && screenType.toLowerCase() === 'onboard'){

            return (
                organizationDbConnection(ehrTables.candidateJob)
                    .select("Designation_Id")
                    .where("Candidate_Id", employeeId)
                    .then((data) => {
                        if (data && data.length) {
                            return data[0].Designation_Id;
                        } else {
                            return false
                        }
                    })
            )
        }else{
            return (
                organizationDbConnection(ehrTables.empJob)
                    .select("Designation_Id")
                    .where("Employee_Id", employeeId)
                    .then((data) => {
                        if (data && data.length) {
                            return data[0].Designation_Id;
                        } else {
                            return false
                        }
                    })
            )
        }
    } catch (err) {
        console.log('Error in getDesignationId()', err)
        return false
    }
}
