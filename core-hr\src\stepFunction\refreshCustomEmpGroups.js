
// Require knex to get dbconnection
var knex = require('knex');
//Require moment
const moment = require('moment-timezone');
//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../../common/tablealias');
const { updateOrgLeaveBalance } = commonLib.leaveCommonFunction;
const { formIds } = require('../../common/appconstants')

// variable declarations
var errResult, organizationDbConnection = '', appmanagerDbConnection = '';


module.exports.refreshCustomEmpGroups = async (args, context) => {
    try {
        console.log("Inside refreshCustomEmpGroups", args);
        let databaseConnection = await commonLib.stepFunctions.getConnection(process.env.stageName, process.env.dbPrefix, process.env.dbSecretName, process.env.region, '', 1);
        // check whether data exist or not
        if (Object.keys(databaseConnection).length) {
            // form app manager database connection
            appmanagerDbConnection = knex(databaseConnection.AppManagerDb);
            let orgCode = args.orgCode;
            let orgRegionDetails = await commonLib.func.getOrgRegionDetails(appmanagerDbConnection, orgCode);
            appmanagerDbConnection ? appmanagerDbConnection.destroy() : null;
            if (orgRegionDetails && Object.keys(orgRegionDetails).length > 0) {
                let additionalHeaders = await commonLib.stepFunctions.formAdditionalHeaders(orgRegionDetails);
                //Get database connection
                let connection = await commonLib.stepFunctions.getConnection(process.env.stageName, process.env.dbPrefix, process.env.dbSecretName, process.env.region, orgCode, 0, additionalHeaders);
                if (Object.keys(connection).length > 0) {
                    organizationDbConnection = knex(connection.OrganizationDb);
                    let customEmployeeGroup = await getAllCustomEmployeeGroup(organizationDbConnection)
                    let employeeIds = args.employeeId
                    let orgDetails = await commonLib.func.getOrgDetails(args.orgCode, organizationDbConnection, 0);
                    if (args.refreshFromNonEmpAndLeaveForm) {
                        //Trigger the custom group refresh
                        await refreshCustomGroup(args, organizationDbConnection, customEmployeeGroup, orgDetails);
                    } else if (args.isDOJUpdated) {
                        //If DOJ Changed delete and Insert the emp eligible leave
                        await deleteAndInsertEmpEligibleLeaves(organizationDbConnection, employeeIds, customEmployeeGroup, 'Date of Join Changed', orgCode, orgDetails, args);
                        //Gender Change
                    } else if (args.leaveEnforcementConfigValue && args.leaveEnforcementConfigValue.toLowerCase() === 'genderchange') {
                        //Update Org Leave Balance
                        await commonLib.leaveCommonFunction.updateOrgLeaveBalance(organizationDbConnection, orgCode, employeeIds, '')
                        let customGroupRecords = await getEmpEligibleCustomGroupLeaveType(organizationDbConnection, employeeIds);
                        let utcCurrentDateTime = moment.utc().format("YYYY-MM-DD HH:mm:ss");
                        for (let i = 0; i < customGroupRecords.length; i++) {
                            //Call updateLeaveForCustomGroup to update the custom group coverage leave type balance
                            await commonLib.refreshCommonFunction.updateLeaveForCustomGroup([customGroupRecords[i].Employee_Id], customGroupRecords[i].Group_Id, orgDetails, utcCurrentDateTime, organizationDbConnection, null, true)
                        }
                        // If Dependent details changed
                    } else if (args.leaveEnforcementConfigValue && args.leaveEnforcementConfigValue.toLowerCase() === 'maternityleaveslab') {
                        // Call maternityLeave leave
                        await commonLib.leaveCommonFunction.updateOrgLeaveBalance(organizationDbConnection, orgCode, employeeIds, 'maternityleaveslab');
                        //If Job details or probation date changed
                    } else if (args.isJobDetailsUpdated || args.isProbationDateUpdated) {
                        if (args.isJobDetailsUpdated && args.isProbationDateUpdated) {
                            await refreshCustomGroup(args, organizationDbConnection, customEmployeeGroup, orgDetails);
                        }

                        if (args.isProbationDateUpdated) {
                            // Update Probation Leave Balance for Custom Group and Organization
                            await Promise.all([
                                updateProbationLeaveBalanceForCustomGroup(organizationDbConnection, employeeIds, orgDetails),
                                commonLib.leaveCommonFunction.updateOrgLeaveBalance(organizationDbConnection, orgCode, employeeIds, '')
                            ]);
                        }
                        else {
                            // - trigger the custom group coverage balance update function
                            await refreshCustomGroup(args, organizationDbConnection, customEmployeeGroup, orgDetails);
                        }
                    } else {
                        //Update Org and Custom Group Leave Balance
                        await commonLib.leaveCommonFunction.updateOrgLeaveBalance(organizationDbConnection, orgCode, employeeIds, '')
                        // - trigger the custom group coverage balance update function
                        await refreshCustomGroup(args, organizationDbConnection, customEmployeeGroup, orgDetails);
                        //if the partner id is entomo call the entomoSync
                        if (args.partnerid?.toLowerCase() === 'entomo') {
                            await syncEntomoEmployee(args.orgCode, args.partnerid, employeeIds);
                        }
                    }
                }
                else {
                    throw ("Error while getting connection for org db.")
                }
            }
            else {
                throw ('Error while getting org region details.')
            }
        }
        else {
            throw ("Error while getting database connection.")
        }
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        return { errorCode: '', message: 'Custom Employee Group refreshed and updated successfully' }
    } catch (error) {
        console.log('Error in refreshCustomEmpGroups() fn main catch block', error);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        appmanagerDbConnection ? appmanagerDbConnection.destroy() : null;
        // access denied error
        if (error === '_DB0102') {
            errResult = commonLib.func.getError('', '_DB0102');
        } else if (error instanceof Object) { // is to check error object is json or not
            errResult = error;
        } else {
            errResult = commonLib.func.getError('', 'ERE0120');
        }
        // throw error response to UI
        throw new ApolloError(errResult.code, errResult.message);
    }
}
/**
 * Synchronizes employee data with Entomo for the specified organization and partner.
 *
 * This function iterates over a list of employee IDs and triggers the Entomo sync process
 * for each employee by invoking a step function. The step function is triggered with specific
 * input parameters including organization code, partner ID, employee ID, function name ('entomoSync'),
 * and action ('add').
 *
 * @param {string} orgCode - The organization code for which the sync is to be performed.
 * @param {string} partnerId - The partner ID used in the sync process.
 * @param {Array<string>} employeeIds - An array of employee IDs to be synchronized with Entomo.
 */
async function syncEntomoEmployee(orgCode, partnerId, employeeIds) {
    console.log('Inside syncEntomoEmployee')
    try {
        for (let i = 0; i < employeeIds.length; i++) {
            let inputData = {
                'orgCode': orgCode, 'partnerId': partnerId, 'inputParams': {
                    entityId: employeeIds[i],
                    entityType: 'Employee',
                    functionName: 'entomoSync',
                    action: 'add'
                }
            }
            //Trigger Entomo Sync
            await commonLib.stepFunctions.triggerStepFunction(process.env.commonStepFunction, 'commonStepFunction', '', inputData);
        }
    }
    catch (err) {
        console.log('Error in syncEntomoEmployee fn main catch block', err);
        return false
    }
}
async function updateProbationLeaveBalanceForCustomGroup(organizationDbConnection, employeeIds, orgDetails) {
    try {
        console.log("Inside updateProbationLeaveBalanceForCustomGroup function.", employeeIds, orgDetails);

        let customGroupDetails = await organizationDbConnection(ehrTables.customGroupEmployees + " as CEG")
            .select("CEG.Group_Id", "CEG.Employee_Id", "LT.LeaveType_Id", "LT.Leave_Name",'LT.Replenishment_Limit as leaveTypeReplenishmentLimit')
            .innerJoin(ehrTables.customGroupAssociated + " as CGAF", "CEG.Group_Id", "CGAF.Custom_Group_Id")
            .innerJoin(ehrTables.leaveType + " as LT", "CGAF.Parent_Id", "LT.LeaveType_Id")
            .whereIn("CEG.Employee_Id", employeeIds)
            .whereIn('CEG.Type', ['Default', 'AdditionalInclusion'])
            .where("LT.Coverage", "CUSTOMGROUP")
            .where("LT.Prorate_Leave_Balance_From", "After Probation")
            .where("CGAF.Form_Id", formIds.leaveTypes)
            .where("LT.Leave_Status", "Active");
        console.log("customGroupDetails", customGroupDetails);
        let currentUtcDateTime = moment().format('YYYY-MM-DD HH:mm:ss');
        for (let i = 0; i < customGroupDetails.length; i++) {
            //Call this function to update the custom group leave type eligible days for the probation employees
            await commonLib.refreshCommonFunction.updateLeaveForCustomGroup([customGroupDetails[i].Employee_Id], customGroupDetails[i].Group_Id, orgDetails, currentUtcDateTime, organizationDbConnection, null, true)
        }
    } catch (err) {
        console.log('Error in updateProbationLeaveBalanceForCustomGroup main catch', err)
        throw err
    }
}

//Function to get the applicable custom group coverage leave type for the employee
async function getEmpEligibleCustomGroupLeaveType(organizationDbConnection, employeeIds) {
    try {
        console.log("Inside getEmpEligibleCustomGroupLeaveType function.", employeeIds);
        return (
            organizationDbConnection(ehrTables.customGroupEmployees + " as CEG")
                .select("CEG.Group_Id", "CEG.Employee_Id", "LT.LeaveType_Id", "LT.Leave_Name",'LT.Replenishment_Limit as leaveTypeReplenishmentLimit')
                .innerJoin(ehrTables.customGroupAssociated + " as CGAF", "CEG.Group_Id", "CGAF.Custom_Group_Id")
                .innerJoin(ehrTables.leaveType + " as LT", "CGAF.Parent_Id", "LT.LeaveType_Id")
                .whereIn("CEG.Employee_Id", employeeIds)
                .whereIn('CEG.Type', ['Default', 'AdditionalInclusion'])
                .where("LT.Coverage", "CUSTOMGROUP")
                .where("CGAF.Form_Id", formIds.leaveTypes)
                .where("LT.Leave_Status", "Active")
                .then(data => {
                    console.log("data", data)
                    return data;
                })
                .catch(e => {
                    console.log("Error in getEmpEligibleCustomGroupLeaveType function .catch block.", e);
                    throw e;
                })
        )
    } catch (err) {
        console.log('Error in getEmpEligibleCustomGroupLeaveType function main catch block.', err)
        throw err
    }
}

async function deleteAndInsertEmpEligibleLeaves(organizationDbConnection, employeeIds, customGroupIds, reason, orgCode, orgDetails, args) {
    try {
        console.log("Inside deleteAndInsertEmpEligibleLeaves function.", employeeIds, customGroupIds, reason, orgCode, args);

        let utcCurrentDateTime = moment.utc().format("YYYY-MM-DD HH:mm:ss");

        //Get applicable custom group leave type details for the employee
        let customGroupRecords = await getEmpEligibleCustomGroupLeaveType(organizationDbConnection, employeeIds);
        console.log("customGroupRecords", customGroupRecords);

        let backupEmpEligibleData = await organizationDbConnection(ehrTables.empEligibleLeave).whereIn('Employee_Id', employeeIds)

        if (backupEmpEligibleData && backupEmpEligibleData.length) {
            //Form the data
            backupEmpEligibleData = backupEmpEligibleData.map((el) => {
                let eligibleLeave = el
                eligibleLeave.Audit_Reason = reason
                eligibleLeave.Added_On = utcCurrentDateTime
                delete eligibleLeave.Eligible_Leave_Id

                return eligibleLeave
            })

            //Insert the backupEligibleData into audit
            await organizationDbConnection(ehrTables.auditEmpEligibleLeave).insert(backupEmpEligibleData)

            //Delete the data from empEligibleLeave
            await organizationDbConnection(ehrTables.empEligibleLeave).whereIn('Employee_Id', employeeIds).del()
        }

        //Update Org Leave Balance
        await commonLib.leaveCommonFunction.updateOrgLeaveBalance(organizationDbConnection, orgCode, employeeIds, '')

        for (let i = 0; i < customGroupRecords.length; i++) {
            //Call this function to insert the custom group coverage leave type balance
            await commonLib.refreshCommonFunction.updateLeaveForCustomGroup([customGroupRecords[i].Employee_Id], customGroupRecords[i].Group_Id, orgDetails, utcCurrentDateTime, organizationDbConnection, null, true)
        }

        return true;

    } catch (err) {
        console.log('Error in deleteAndInsertEmpELigibleLeaves function main catch block', err);
        throw err
    }
}

async function insertStatusInCustomGroupRefreshStatus(organizationDbConnection, data) {
    try {
        return (
            organizationDbConnection(ehrTables.customGroupRefreshStatus)
                .insert(data)
                .then(data => {
                    console.log("Custom group refresh status successfully inserted.")
                    return true;
                })
                .catch(e => {
                    console.log('Error in insertStatusInCustomGroupRefreshStatus function .catch block', e);
                    return false;
                })
        )
    }
    catch (e) {
        console.log('Error in insertStatusInCustomGroupRefreshStatus function main catch block', e);
        return false;
    }
}

async function refreshCustomGroup(args, organizationDbConnection, groupIdArray, orgDetails) {
    try {
        console.log("Inside async refreshCustomGroup function.", args, groupIdArray)
        let addedOn = moment.utc().format("YYYY-MM-DD HH:mm:ss")
        // check groupIdArray length
        if (groupIdArray.length > 0) {
            let insertStatusArray = [];
            // Iterate the groupId and run filter query and update the employees in table
            for (let group in groupIdArray) {
                // get groupdetails
                var groupId = groupIdArray[group].Group_Id;
                let status = 'InProgress';
                let errorMessage = null;
                addedOn = moment.utc().format("YYYY-MM-DD[T]HH:mm:ss");
                // check Filter_Vue field is empty or not
                if (groupIdArray[group].Filter_Vue) {
                    // call function formKnexQuery() to form filter query
                    var filterQuery = await commonLib.func.formKnexQuery(groupIdArray[group].Filter_Vue, organizationDbConnection, 0, args.isCustomGroupRefresh, args.employeeId);
                    //Call this function to refresh the custom employee group and insert/update the leave eligible days for the newly added employes in the group
                    var runAndUpdateQuery = filterQuery.knexQuery ? await commonLib.refreshCommonFunction.runFilterQuery(args, groupId, filterQuery.knexQuery, orgDetails, addedOn, organizationDbConnection) : "Custom group rule is empty";
                    if (runAndUpdateQuery) {
                        status = 'Success';
                    }
                    else {
                        status = 'Failed';
                        errorMessage = runAndUpdateQuery;
                    }
                }
                else {
                    status = 'Success';
                }
                let insertStatus = {
                    Group_Id: groupId,
                    Error_Message: errorMessage,
                    Status: status,
                    Added_On: addedOn,
                    Added_By: args.logInEmpId
                }
                for (let i = 0; i < args.employeeId.length; i++) {
                    insertStatus['Employee_Id'] = args.employeeId[i];
                    insertStatusArray.push(insertStatus);
                }
            }
            await insertStatusInCustomGroupRefreshStatus(organizationDbConnection, insertStatusArray);
            console.log('Custom Employee Group refreshed and updated successfully');
            // return response
            return 'Custom Employee Group refreshed and updated successfully';
        } else {
            console.log('There is no custom group exists');
            return 'There is no custom group exists';
        }
    }
    catch (e) {
        console.log('Error in refreshCustomGroup', e);
        throw e;
    }
}

async function getAllCustomEmployeeGroup(organizationDbConnection) {
    try {
        return (
            organizationDbConnection(ehrTables.cusEmpGroup)
                .select('*')
                .then(data => {
                    return data;
                })
                .catch(e => {
                    console.log("Error in getAllCustomEmployeeGroup function .catch block", e);
                    throw e;
                })
        )
    }
    catch (e) {
        console.log("Error in getAllCustomEmployeeGroup function main catch block", e);
        throw e;
    }
}
