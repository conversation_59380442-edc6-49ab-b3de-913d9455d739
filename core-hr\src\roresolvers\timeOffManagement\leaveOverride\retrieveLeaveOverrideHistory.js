//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { UserInputError,ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../../../../common/tablealias');

//Validate the inputs
function validateInputs(args){
    try{
        let validationError = {};
        if(!args.employeeId){
            validationError['IVE0083'] = commonLib.func.getError('', 'IVE0083').message;
        }
        if(!args.leaveTypeId){
            validationError['IVE0442'] = commonLib.func.getError('', 'IVE0442').message;
        }
        return validationError;
    }catch(error){
        console.log('Error in validateInputs() function main catch block.',error);
        throw error;
    }
}
//Resolver function to list the employee eligible leave - leave override history details
module.exports.retrieveLeaveOverrideHistory = async (parent, args, context, info) => {
    console.log('Inside retrieveLeaveOverrideHistory function');
    let organizationDbConnection;
    let validationError;
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);
        validationError = validateInputs(args);
        if(Object.keys(validationError).length === 0){
            let employeeId = args.employeeId;
            let leaveTypeId = args.leaveTypeId;
            return (organizationDbConnection
            .transaction(function (trx) {
                //Get the leave override form history details
                return( organizationDbConnection(ehrTables.auditEmpEligibleLeave+' as AEEL')
                .select('AEEL.Eligible_Days as currentYearTotalEligibleDays','LT.Leave_Type as leaveType','AEEL.No_Of_Days as totalCODays','AEEL.Last_CO_Balance as lastCOBalance','AEEL.Added_On as updatedOn',
                'AEEL.Leaves_Taken as leavesTaken','LT.Carry_Over as carryOver', organizationDbConnection.raw("(CONCAT_WS(' ',EPI.Emp_First_Name,EPI.Emp_Middle_Name, EPI.Emp_Last_Name)) as employeeName"),
                organizationDbConnection.raw("(CASE WHEN EJ.User_Defined_EmpId IS NOT NULL THEN EJ.User_Defined_EmpId ELSE EPI.Employee_Id END) as userDefinedEmpId"),
                organizationDbConnection.raw("CONCAT_WS(' ',EPI1.Emp_First_Name,EPI1.Emp_Middle_Name, EPI1.Emp_Last_Name) as updatedBy"))
                .innerJoin(ehrTables.leaveType+' as LT','AEEL.LeaveType_Id','LT.LeaveType_Id')
                .innerJoin(ehrTables.empPersonalInfo+' as EPI','AEEL.Employee_Id','EPI.Employee_Id')
                .innerJoin(ehrTables.empJob+' as EJ','EPI.Employee_Id', 'EJ.Employee_Id')
                .leftJoin(ehrTables.empPersonalInfo + ' as EPI1', 'AEEL.Added_By','EPI1.Employee_Id')
                .where('AEEL.Employee_Id',employeeId)
                .where('AEEL.LeaveType_Id',leaveTypeId)
                .where('AEEL.Audit_Reason','leave-override')
                .orderBy('AEEL.Audit_Eligible_Leave_Id', 'desc')
                .transacting(trx)
                .then(historyResult=>{
                    return { errorCode: '', message: 'Leave override history details has been retrieved successfully.', historyDetails: historyResult ? historyResult : [] };//Return the response
                }))     
            })
            .then(function (result) {
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                return result;
            })
            .catch(function (catchError) {
                console.log('Error in retrieveLeaveOverrideHistory .catch() block', catchError);
                let errResult = commonLib.func.getError(catchError, 'CTL0102');
                //Destroy DB connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                throw new ApolloError(errResult.message, errResult.code);
            })
            )
        }else{
            console.log("Invalid input",args);
            throw 'IVE0000';
        }
    }catch(error){
        console.log('Error in the retrieveLeaveOverrideHistory() function in the main catch block.',error);
        let errResult;
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        if(error==='IVE0000'){
            console.log('Validation error in the retrieveLeaveOverrideHistory() function.',validationError);
            errResult = commonLib.func.getError('',error);
            throw new UserInputError(errResult.message,{validationError: validationError});
        }else{
            errResult = commonLib.func.getError(error, 'CTL0002');
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
}