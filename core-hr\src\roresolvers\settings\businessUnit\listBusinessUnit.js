// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formName } = require('../../../../common/appconstants');

const resolvers = {
    Query: {
        listBusinessUnit: async (parent, args, context, info) => {
            let organizationDbConnection;
            try {
                console.log("Inside listBusinessUnit function.")
                let employeeId = context.Employee_Id;
                organizationDbConnection = knex(context.connection.OrganizationDb);

                //Check the login employee is admin/super admin or employee admin and has the view access for the form
                let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, employeeId, formName.businessUnit, '', 'UI');
                if (Object.keys(checkRights).length > 0 && checkRights.Role_View === 1) {
                    return (
                        organizationDbConnection(ehrTables.businessUnit + ' as B')
                            .select('B.Business_Unit_Id as businessUnitId', 'B.Business_Unit as businessUnit', 'B.Level as level',
                                'B.Business_Unit_Status as status', 'B.Description as description', 'B.Added_On as addedOn', 'B.Updated_On as updatedOn',
                                organizationDbConnection.raw("CONCAT_WS(' ',EPI.Emp_First_Name,EPI.Emp_Middle_Name, EPI.Emp_Last_Name) as addedByName"),
                                organizationDbConnection.raw("CONCAT_WS(' ',EPI2.Emp_First_Name,EPI2.Emp_Middle_Name, EPI2.Emp_Last_Name) as updatedByName"),
                                'B.Business_Unit_Code as businessUnitCode', 'B.Business_Unit_Parent_Id as businessUnitParentId',
                                'BU.Business_Unit as businessUnitParentName'
                            )
                            .leftJoin(ehrTables.empPersonalInfo + " as EPI", "B.Added_By", "EPI.Employee_Id")
                            .leftJoin(ehrTables.empPersonalInfo + " as EPI2", "B.Updated_By", "EPI2.Employee_Id")
                            .leftJoin(ehrTables.businessUnit + " as BU", "BU.Business_Unit_Id", "B.Business_Unit_Parent_Id")
                            .orderBy('B.Business_Unit_Code', 'asc')
                            .then((result) => {
                                //Destroy DB connection
                                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                return { errorCode: "", message: "Business unit list retrieved successfully.", settings: (result && result.length > 0) ? result : [] };
                            })
                            .catch((err) => {
                                console.log('Error in listBusinessUnit .catch() block', err);
                                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                let errResult = commonLib.func.getError(err, 'SBU0002');
                                throw new ApolloError(errResult.message, errResult.code);
                            })
                    )
                }
                else {
                    throw '_DB0100';
                }
            }
            catch (e) {
                //Destroy DB connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                console.log('Error in listBusinessUnit function main catch block.', e);
                let errResult = commonLib.func.getError(e, 'SBU0104');
                throw new ApolloError(errResult.message, errResult.code);
            }
        },
        listBusinessUnitInDropdown: async (parent, args, context, info) => {
            let organizationDbConnection;
            try {
                console.log("Inside listBusinessUnitInDropdown function.")
                let employeeId = context.Employee_Id;
                organizationDbConnection = knex(context.connection.OrganizationDb);
                let businessUnitQuery = organizationDbConnection(ehrTables.businessUnit + ' as B')
                    .select('B.Business_Unit_Id as businessUnitId',
                        organizationDbConnection.raw(
                            "CASE WHEN B.Business_Unit_Code IS NOT NULL THEN CONCAT(B.Business_Unit_Code, ' - ', B.Business_Unit) ELSE B.Business_Unit END AS businessUnit"
                        )
                    );

                let action = args.action ? args.action : 'all';
                if (action === 'active') {
                    businessUnitQuery = businessUnitQuery.where('Business_Unit_Status', 'Active');
                }
                return (
                    businessUnitQuery
                        .then((result) => {
                            if (result) {
                                //Destroy DB connection
                                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                return { errorCode: "", message: "Business unit list retrieved successfully.", settings: result };
                            } else {
                                console.log('Business unit does not exist');
                                throw 'SBU0105'
                            }
                        })
                        .catch((err) => {
                            console.log('Error in listBusinessUnit .catch() block', err);
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            let errResult = commonLib.func.getError(err, 'SBU0002');
                            throw new ApolloError(errResult.message, errResult.code);
                        })
                )
            }
            catch (e) {
                //Destroy DB connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                console.log('Error in listBusinessUnitInDropdown function main catch block.', e);
                let errResult = commonLib.func.getError(e, 'SBU0104');
                throw new ApolloError(errResult.message, errResult.code);
            }
        }
    }
}
exports.resolvers = resolvers;
