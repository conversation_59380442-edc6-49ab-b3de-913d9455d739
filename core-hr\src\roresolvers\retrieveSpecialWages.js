// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formName,formIds } = require('../../common/appconstants');
module.exports.retrieveSpecialWages = async (parent, args, context, info) => {
    let organizationDbConnection;
    try {
        console.log("Inside retrieveSpecialWages function.")
        let employeeId = context.Employee_Id;
        let accessFormName = formName.specialWages;
        let formId= formIds.specialWages;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        // check their rights
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, employeeId, accessFormName, '', 'UI');
        if (Object.keys(checkRights).length > 0 && checkRights.Role_View === 1) {
            return (
                organizationDbConnection(ehrTables.specialWages + " as SW")
                    .select('SW.*', 'CGA.Custom_Group_Id', 'CEG.Group_Name',organizationDbConnection.raw("CONCAT_WS(' ',EPI.Emp_First_Name, EPI.Emp_Middle_Name, EPI.Emp_Last_Name) as Updated_By"), organizationDbConnection.raw("CONCAT_WS(' ',EPI2.Emp_First_Name,EPI2.Emp_Middle_Name, EPI2.Emp_Last_Name) as Added_By"))
                    .leftJoin(ehrTables.empPersonalInfo + " as EPI", "EPI.Employee_Id", " SW.Updated_By")
                    .leftJoin(ehrTables.empPersonalInfo + " as EPI2", "EPI2.Employee_Id", "SW.Added_By")
                    .leftJoin(ehrTables.customGroupAssociated + " as CGA", function() {
        this.on("SW.Configuration_Id", "=", "CGA.Parent_Id")
            .andOn("CGA.Form_Id", "=", formId);
    })
                    .leftJoin(ehrTables.cusEmpGroup + " as CEG", "CEG.Group_Id", "CGA.Custom_Group_Id")
                    .then((data) => {
                        if (data) {
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            return { errorCode: "", message: "Special wages configuration retrieved successfully.", SpecialWagesConfiguration: data };
                        } else {
                            console.log('Special wages data not found')
                            throw 'SSW0101'
                        }
                    })
                    .catch((err) => {
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        console.log('Error in retrieveSpecialWages .catch() block', err);
                        let errResult = commonLib.func.getError(err, 'SSW0101');
                        throw new ApolloError(errResult.message, errResult.code);
                    })
            )
        }
        else {
            throw '_DB0100';
        }
    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in retrieveSpecialWages function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'SSW0001');
        throw new ApolloError(errResult.message, errResult.code);
    }
}