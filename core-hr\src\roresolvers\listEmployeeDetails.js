// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formName } = require('../../common/appconstants');


let organizationDbConnection;
module.exports.listEmployeeDetails = async (parent, args, context, info) => {
    try {
        console.log("Inside listEmployeeDetails function.")
        let employeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        // check their rights
        let adminRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, employeeId, formName.admin, '', 'UI');
        let employeeAdminRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, employeeId, formName.employees, '', 'UI');
        if ((adminRights && adminRights.Role_View === 1) || (employeeAdminRights && employeeAdminRights.Role_View === 1 && employeeAdminRights.Employee_Role.toLowerCase() === 'admin')) {
            return (
                organizationDbConnection(ehrTables.empJob)
                    .select("EPI.Emp_First_Name", "EPI.Emp_Middle_Name", "EPI.Emp_Last_Name", "EPI.Employee_Id", "EJ.Emp_Email", "EJ.Emp_Status", "EJ.User_Defined_EmpId", "EJ.Manager_Id", organizationDbConnection.raw("CONCAT_WS(' ',CD.Mobile_No_Country_Code ,CD.Mobile_No) as Mobile_No"))
                    .leftJoin(ehrTables.empPersonalInfo + " as EPI", "EPI.Employee_Id", "EJ.Employee_Id")
                    .leftJoin(ehrTables.contactDetails + " as CD", "CD.Employee_Id", "EJ.Employee_Id")
                    .from(ehrTables.empJob + " as EJ")
                    .then((data) => {
                        //destroy the connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        return { errorCode: "", message: "Employee details retrieved successfully.", listEmployeeDetails: data };
                    })
                    .catch((err) => {
                        console.log('Error in listEmployeeDetails .catch() block', err);
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        let errResult = commonLib.func.getError(err, 'EDM0103');
                        throw new ApolloError(errResult.message, errResult.code);
                    })
            )
        }
        else {
            throw '_DB0109';
        }

    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in listEmployeeDetails function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'EDM0003');
        throw new ApolloError(errResult.message, errResult.code);
    }
}
