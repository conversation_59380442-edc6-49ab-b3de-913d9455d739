// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../common/tablealias');
//Require apollo server to return error message
const { UserInputError, ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formName, formIds, systemLogs } = require('../../../common/appconstants');
//Require validation function
const { numberValidation } = require('../../../common/commonvalidation');

module.exports.deleteActivityAssociatedWithProject = async (parent, args, context, info) => {
    let organizationDbConnection;
    let errResult;
    let validationError = {};

    try {
        console.log("Inside deleteActivityAssociatedWithProject function.");
        let employeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);

        // check their rights
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, employeeId, null, '', 'UI', false, formIds.projects);

        if (Object.keys(checkRights).length > 0 && checkRights.Role_Delete === 1) {
            let projectActivityId = args.projectActivityId;

            if (args.projectActivityId) {
                if (args.projectActivityId < 1 || !(numberValidation(args.projectActivityId))) {
                    validationError['IVE0420'] = commonLib.func.getError('', 'IVE0420').message1;
                }
            } else {
                validationError['IVE0420'] = commonLib.func.getError('', 'IVE0420').message;
            }

            // Check validation error exist or not
            if (Object.keys(validationError).length === 0) {
                const timesheetResult = await organizationDbConnection(ehrTables.timesheetHoursTracking)
                    .select("*")
                    .where('Project_Activity_Id', args.projectActivityId);

                if (timesheetResult && timesheetResult.length === 0) {
                    const assignmentResult = await organizationDbConnection(ehrTables.assignment)
                        .select("*")
                        .where('Activity_Id', args.projectActivityId);

                    if (assignmentResult && assignmentResult.length === 0) {
                        const deleteResult = await organizationDbConnection(ehrTables.projectActivities)
                            .delete()
                            .where('Project_Activity_Id', args.projectActivityId);

                        if (deleteResult) {
                            let systemLogParams = {
                                userIp: context.User_Ip,
                                employeeId: employeeId,
                                organizationDbConnection: organizationDbConnection,
                                message: `Project activity id ${args.projectActivityId} was deleted by ${employeeId}`
                            };

                            // Call function to add the system log
                            await commonLib.func.createSystemLogActivities(systemLogParams);
                            // Destroy the connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            return { errorCode: "", message: "Project activity deleted successfully." };
                        } else {
                            console.log('The project activity from project activities table cannot be deleted');
                            throw 'CHR0108';
                        }
                    } else {
                        console.log("Unable to delete the project activity as this activity is associated with assignments.");
                        throw ('CHR0114');
                    }
                } else {
                    console.log("Unable to delete the project activity as this activity is associated with employee timesheet.");
                    throw ('CHR0115');
                }
            } else {
                throw 'IVE0000';
            }
        } else {
            console.log("The employee does not have delete access.");
            throw '_DB0103';
        }
    } catch (e) {
        console.log('Error in deleteActivityAssociatedWithProject function main catch block.', e);
        // Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;

        // If validation error exist
        if (e === 'IVE0000') {
            console.log('Validation error in the deleteActivityAssociatedWithProject() function', validationError);
            errResult = commonLib.func.getError('', 'IVE0000');
            throw new UserInputError(errResult.message, { validationError: validationError }); // Return error response
        } else {
            errResult = commonLib.func.getError(e, 'CHR0076');
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
};
