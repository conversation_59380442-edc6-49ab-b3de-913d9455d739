// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib
// require knex
const knex = require('knex')
// require common table alias
const { ehrTables } = require('../../../common/tablealias')
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda')
const moment = require('moment')
const { formIds } = require('../../../common/appconstants')

module.exports.retrieveAirTicketSettlementSummary = async (parent, args, context, info) => {
  console.log('Inside retrieveAirTicketSettlementSummary function')
  let organizationDbConnection
  try {
    organizationDbConnection = knex(context.connection.OrganizationDb)
    const loginEmployeeId = context.Employee_Id

    // Check employee access rights
    const checkRights = await commonLib.func.checkEmployeeAccessRights(
      organizationDbConnection,
      loginEmployeeId,
      null,
      '',
      'UI',
      false,
      args.formId
    )

    let isAdmin = 0;
    let serviceProviderAdmin = false;
    let employeeIdsArray=[]
    // Verify access rights
    if (Object.keys(checkRights).length > 0 && checkRights.Role_View === 1) {
       if (checkRights.Employee_Role.toLowerCase() === 'admin' && !args.employeeId) {
            serviceProviderAdmin = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, '', 'Role_Update', '', false, formIds.serviceProviderAdmin);
            if (serviceProviderAdmin) {
                let serviceProviderEmployeeIds
                serviceProviderEmployeeIds = await commonLib.func.getServiceProviderEmpIdsForFieldForce(organizationDbConnection, loginEmployeeId, context.Org_Code)
                //If serviceProviderEmployeeIds is not false use the serviceProviderEmployeeIds
                if (serviceProviderEmployeeIds) {
                    employeeIdsArray = employeeIdsArray.concat(serviceProviderEmployeeIds);
                }
            }else {
              isAdmin = 1;
          }
          
      }
      else if(checkRights.Employee_Role.toLowerCase() !== 'admin' && !args.employeeId){
        console.log("Employee is not admin");
        throw '_DB0109'
      }
    } else {
      console.log('Employee does not have view/admin access rights')
        throw '_DB0100'
      }

      return await organizationDbConnection(ehrTables.airTicketSettlementSummary + ' as ATSS')
        .select('ATSS.*',
          organizationDbConnection.raw(
            '(CASE WHEN EJ.User_Defined_EmpId IS NOT NULL THEN EJ.User_Defined_EmpId ELSE ATSS.Employee_Id END) as User_Defined_EmpId'
          ),
          organizationDbConnection.raw('CONCAT_WS(" ", EPI3.Emp_First_Name, EPI3.Emp_Middle_Name, EPI3.Emp_Last_Name) as Employee_Name'),
        )
        .leftJoin(ehrTables.empPersonalInfo + ' as EPI3','EPI3.Employee_Id', 'ATSS.Employee_Id')
        .leftJoin(ehrTables.empJob + ' as EJ', 'EJ.Employee_Id', 'ATSS.Employee_Id')
        .modify((queryBuilder) => {
          if (args.employeeId) {
            queryBuilder.where('ATSS.Employee_Id', args.employeeId)
          }
          if(!isAdmin && !args.employeeId){
            queryBuilder.whereIn('ATSS.Employee_Id', employeeIdsArray)
          }
          if (args.month && args.year) {
            let formattedMonth = String(args.month).padStart(2, '0'); // Ensure two-digit month
            let monthStartDate = moment(`${args.year}-${formattedMonth}-01`).format('YYYY-MM-DD');
            let monthEndDate = moment(`${args.year}-${formattedMonth}-01`).endOf('month').format('YYYY-MM-DD');
            if (monthStartDate && monthEndDate) {
              queryBuilder.whereBetween('ATSS.Availed_Date', [monthStartDate, monthEndDate]);
            }
          }
        })
        .then(async (data) => {
          organizationDbConnection?.destroy()
          return {
            errorCode: '',
            message: 'Settlement summary retrieved successfully.',
            settlementSummary: data.length?data:[]
          }
        })
        .catch((catchError) => {
          let errResult = commonLib.func.getError(catchError, 'ATT0007')
          //Destroy DB connection
          organizationDbConnection ? organizationDbConnection.destroy() : null
          throw new ApolloError(errResult.message, errResult.code)
        })
  } catch (e) {
    organizationDbConnection?.destroy()
    console.log('Error in retrieveAirTicketSettlementSummary:', e)
    const errResult = commonLib.func.getError(e, 'ATT0007')
    throw new ApolloError(errResult.message, errResult.code)
  }
}
