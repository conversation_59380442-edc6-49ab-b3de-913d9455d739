//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { UserInputError,ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../../../common/tablealias');
//Require constants
const { formName,systemLogs, formIds } = require('../../../common/appconstants');
//Require validation functions
const { validateAddUpdateWorkScheduleInputs,validateAndFormWSUpdateDetails } = require('../../../common/workScheduleValidation');

//Add the work schedule
module.exports.addWorkSchedule = async (parent, args, context, info) => {
    console.log('Inside addWorkSchedule() function.');
    let organizationDbConnection;
    let errResult;
    let validationError={};
    try{
        let loginEmployeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        //Get the login employee - work schedule form access
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection,loginEmployeeId,formName.workSchedule,'','UI');
        //Check add rights exist or not                 
        if(Object.keys(checkRights).length>0 && checkRights.Role_Add === 1) {
            validationError = await validateAddUpdateWorkScheduleInputs(organizationDbConnection,args,1);
            //Check validation error exist or not
            if(Object.keys(validationError).length ===0){
                //Validate and form the work schedule add details
                let workScheduleAddJson = await validateAndFormWSUpdateDetails(organizationDbConnection,args,loginEmployeeId,1);
                //Insert the work schedule details
                return(
                organizationDbConnection(ehrTables.workSchedule)
                .insert(workScheduleAddJson)
                .then(async(workScheduleIdArray) => {
                    let workScheduleId = workScheduleIdArray[0];
                    let lateAttendance = await checkLateAttendance(organizationDbConnection)
                    if(!lateAttendance){
                        throw 'CWS0108'
                    }
                    //Log message: Add Work Schedule  - 47
                    let systemLogParam = {
                        action: systemLogs.roleAdd,
                        userIp: context.User_Ip,
                        employeeId: loginEmployeeId,
                        formId: formIds.workSchedule,
                        organizationDbConnection: organizationDbConnection,
                        uniqueId: workScheduleId,
                        message: `Work schedule ${args?.workScheduleCode || ''}${args.workSchedule} business hours ${args.businessHoursStartTime} start time to ${args.businessHoursEndTime} end time, ${args.breakScheduleStartTime && args.breakScheduleEndTime ? `, break schedule ${args.breakScheduleStartTime} start time to ${args.breakScheduleEndTime} end time` : ''} has been added successfully.`,
                        isEmployeeTimeZone: 0,
                        changedData: args
                    };
                    //Call the function to add the system log
                    await commonLib.func.createSystemLogActivities(systemLogParam);
                    //Destroy DB connection
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    //Return success response
                    return {errorCode: '',message:'Work schedule added successfully.',workScheduleId:workScheduleId, lateAttendance:lateAttendance};
                })
                .catch(function (catchError) {
                    console.log('Error in addWorkSchedule() function .catch() block', catchError);
                    errResult = commonLib.func.getError(catchError, 'CWS0102');
                    throw new ApolloError(errResult.message,errResult.code);//Return error response
                })
                )
            }else{
                throw 'IVE0000';
            }
        }
        else{
            console.log('Login employee id does not have add access to work schedule form.');
            throw ('_DB0101');
        }
    }catch(addWorkScheduleMainCatchErr) {
        console.log('Error in the addWorkSchedule() function main catch block. ',addWorkScheduleMainCatchErr);
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        //If validation error exist
        if (addWorkScheduleMainCatchErr === 'IVE0000') {
            console.log('Validation error in the addWorkSchedule() function',validationError);
            errResult = commonLib.func.getError('', 'IVE0000');
            throw new UserInputError(errResult.message, { validationError: validationError });//Return error response
        } else{
            errResult = commonLib.func.getError(addWorkScheduleMainCatchErr, 'CWS0004');
            throw new ApolloError(errResult.message,errResult.code);//Return error response
        }
    }
};


async function checkLateAttendance(organizationDbConnection){
    try{
        return(
            organizationDbConnection(ehrTables.attendanceSettings)
            .select('*')
            .where('Configuration_Type', 'Late Attendance')
            .andWhere('Attendance_Settings_Status', 'Active')
            .then((data)=>{
                if(data && data.length){
                    return 'active'
                }else{
                    return 'inactive'
                }
            })
            .catch((err)=>{
                console.log('Error while checking for late attendance', err)
                return false
            })
        )
    }catch(err){
        console.log('Error while checking for late attendance', err)
        return false
    }
}