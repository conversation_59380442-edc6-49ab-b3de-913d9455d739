//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../../../common/tablealias');
const { formName } = require('../../../common/appconstants');

//function to delete holidays

module.exports.deleteHolidays = async (parent, args, context, info) => {
  console.log('Inside deleteHolidays function');
  let organizationDbConnection;
  let loginEmployeeId = context.Employee_Id;
  try {
    organizationDbConnection = knex(context.connection.OrganizationDb);
    //Form Access check for deleting holidays
    let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, formName.holidays, '', 'UI');
    if (Object.keys(checkRights).length > 0 && checkRights.Role_Delete === 1) {
      let failedHolidayIds = [];
      return organizationDbConnection(ehrTables.holidayAssignment)
        .select('Holiday_Id')
        .whereIn('Holiday_Id', args.holidayIds)
        .from(ehrTables.holidayAssignment)
        .then(async data => {
          if (data && data.length >= args.holidayIds.length) {
            throw 'CGH0133';
          } else {
            if (data.length) {
              const values = data.map(object => Object.values(object)).reduce((acc, val) => acc.concat(val), []);
              failedHolidayIds = failedHolidayIds.concat(values);
            }
            return organizationDbConnection(ehrTables.holidayCustomGroup)
              .select('Holiday_Id')
              .whereIn('Holiday_Id', args.holidayIds)
              .then(data => {
                if (data && data.length >= args.holidayIds.length) {
                  throw 'CGH0133';
                } else {
                  const values = data.map(object => Object.values(object)).reduce((acc, val) => acc.concat(val), []);
                  failedHolidayIds = failedHolidayIds.concat(values);
                }
                if (failedHolidayIds.length >= args.holidayIds.length) {
                  throw 'CGH0133';
                } else {
                  const deletableHolidayIds = args.holidayIds.filter(x => !failedHolidayIds.includes(x));
                  return organizationDbConnection(ehrTables.holiday)
                    .del()
                    .whereIn('Holiday_Id', deletableHolidayIds)
                    .then(async data => {
                      if (data) {
                        await updateDashboardSetupStatus(organizationDbConnection)
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        if (failedHolidayIds.length) {
                          return { errorCode: '', message: 'Holiday(s) have been partially deleted due to the existence of holidays which are associated with custom group or location holidays' };
                        } else {
                          return { errorCode: '', message: 'Holiday(s) have been deleted' };
                        }
                      } else {
                        throw 'CGH0132';
                      }
                    });
                }
              });
          }
        })
        .catch(catchError => {
          console.log('Error in deleteHolidays .catch() block', catchError);
          let errResult = commonLib.func.getError(catchError, 'CGH0132');
          //Destroy DB connection
          organizationDbConnection ? organizationDbConnection.destroy() : null;
          //Return error response
          throw new ApolloError(errResult.message, errResult.code);
        });
    } else {
      console.log('No rights to delete holidays');
      throw '_DB0103';
    }
  } catch (mainCatchError) {
    console.log('Error in deleteHolidays function main block', mainCatchError);
    organizationDbConnection ? organizationDbConnection.destroy() : null;
    let errResult = commonLib.func.getError(mainCatchError, 'CGH0017');
    // return response
    throw new ApolloError(errResult.message, errResult.code);
  }
};

async function updateDashboardSetupStatus(organizationDbConnection){
  try{
    return(
      organizationDbConnection(ehrTables.holiday)
      .select('Holiday_Id')
      .then(async(data)=>{
        if(data && !data.length){
          //Update the datasetup_dashboard status
          await updateDataSetupDashboard(organizationDbConnection, null, formIds.holidayTypes, 'Open')
        }
        return true
      })
      .catch((err)=>{
        console.log('Error in updateDashboardSetupStatus .catch', err)
        throw err
      })
    )
  }catch(err){
    console.log('Error in updateDashboardSetupStatus main catch', err)
    throw err
  }
}
