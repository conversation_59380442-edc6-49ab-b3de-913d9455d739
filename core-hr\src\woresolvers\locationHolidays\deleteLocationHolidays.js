//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../../../common/tablealias');
const { formName } = require('../../../common/appconstants');
const { getAttendanceExist, getCompensatoryOffExist, getLeaveExist, getPaySlipExist } = require('../../../common/commonfunctions');

//function to delete location holidays

module.exports.deleteLocationHolidays = async (parent, args, context, info) => {
    console.log('Inside deleteLocationHolidays function');
    let organizationDbConnection;
    let loginEmployeeId = context.Employee_Id;
    let orgCode = context.Org_Code
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);
        //Form Access check for deleting location holidays
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, formName.holidays, '', 'UI');
        if (Object.keys(checkRights).length > 0 && checkRights.Role_Delete === 1) {
            return (
                organizationDbConnection(ehrTables.holidayAssignment)
                    .select("HA.Holiday_Assign_Id", "HA.Holiday_Date", "HA.Location_Id", "EJ.Employee_Id")
                    .whereIn("HA.Holiday_Assign_Id", args.holidayIds)
                    .leftJoin(ehrTables.empJob + " as EJ", "EJ.Location_Id", "HA.Location_Id")
                    .from(ehrTables.holidayAssignment + " as HA")
                    .then(async (data) => {
                
                        let toBeChangedData = JSON.parse(JSON.stringify(data))
                        let holidayData = []

                        //Getting the employeeIds into single object
                        toBeChangedData.forEach(i => {
                            let f=0;
                            holidayData.forEach(j => {
                                if (i.Holiday_Assign_Id == j.Holiday_Assign_Id){
                                    if(!j.Employee_Id.includes(i.Employee_Id)){
                                        j.Employee_Id.push(i.Employee_Id)
                                    }
                                    f = 1
                                }
                            })
                            if(f == 0){
                                holidayData.push(i)
                                holidayData[holidayData.length-1]['Employee_Id'] = [holidayData[holidayData.length-1]['Employee_Id']]
                            }
                        });

                        //validation to check if date is in payslip
                        let paySlipResult = await getPaySlipExist(holidayData, organizationDbConnection, orgCode)
                        if(!paySlipResult){
                            throw 'CGH0116'
                        }
                        if(paySlipResult.length){
                            console.log('Payslip contains date')
                            throw 'CGH0117'
                        }

                        //validation to check if date is in emp_attendance table
                        let attendanceResult = await getAttendanceExist(holidayData, organizationDbConnection);
                        if(!attendanceResult){
                            throw 'CGH0105'
                        }
                        if (attendanceResult.length) {
                            console.log('Attendance contains date')
                            //get the values from the array of object
                            let attendanceDates = attendanceResult.map(function (item) {
                                return item.Attendance_Date
                            })
                            //Compare the holidayData with result, remove the holidayData record if the result contains the same holiday date
                            holidayData = holidayData.filter((data) => {
                                return !attendanceDates.includes(data.Holiday_Date)
                            })
                        }
                        if (holidayData.length) {
                            //validation to check if date is in compensatory table
                            let compensatoryResult = await getCompensatoryOffExist(holidayData, organizationDbConnection)
                            if(!compensatoryResult){
                                throw 'CGH0106'
                            }
                            if (compensatoryResult.length) {
                                console.log('Compensatory off contains date')
                                //get the values from the array of object
                                let compensatoryDates = compensatoryResult.map(function (item) {
                                    return item.Compensatory_Date
                                })
                                //Compare the holidayData with result, remove the holidayData record if the result contains the same holiday date
                                holidayData = holidayData.filter((data) => {
                                    return !compensatoryDates.includes(data.Holiday_Date)
                                })
                            }
                        }
                        if (holidayData.length) {
                            //validation to check if date is in leave table
                            let employeeLeaveResult = await getLeaveExist(holidayData, organizationDbConnection)
                            if(!employeeLeaveResult){
                                console.log('Employee Leave contains date')
                                throw 'CGH0107'
                            }
                            if (employeeLeaveResult.length) {
                                //Compare the holidayData with result, remove the holidayData record if the result contains the same holiday date
                                holidayData = holidayData.filter((data) => {
                                    return !employeeLeaveResult.includes(data.Holiday_Date)
                                })
                            }
                        }

                        //After all the four validation, if it is not in anyone, We will delete it
                        if (holidayData.length) {
                            let result = await deleteSelectedLocationHolidays(holidayData, organizationDbConnection)
                            if (result) {
                                //Destroy DB connection
                                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                if (args.holidayIds.length === holidayData.length) {
                                    return { errorCode: "", message: "Holidays records have been deleted" }
                                } else {
                                    return { errorCode: "", message: "Holiday records are partially deleted due to the existence of user records for attendance or leave or compensatory off or shorttime off for the same date as holiday." }
                                }
                            } else {
                                throw 'CGH0130'
                            }
                            //if every holiday id fails the validation
                        } else {
                            throw 'CGH0103'
                        }
                    })
                    .catch(catchError => {
                        console.log('Error in deleteLocationHolidays .catch() block', catchError);
                        let errResult = commonLib.func.getError(catchError, 'CGH0130');
                        //Destroy DB connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        //Return error response
                        throw new ApolloError(errResult.message, errResult.code);
                    })
            );
        } else {
            console.log('No rights to delete location holidays');
            throw '_DB0103';
        }
    }
    catch (mainCatchError) {
        console.log('Error in deleteLocationHolidays function main block', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        errResult = commonLib.func.getError(mainCatchError, 'CGH0015');
        // return response
        throw new ApolloError(errResult.message, errResult.code);
    }
}

async function deleteSelectedLocationHolidays(holidayData, organizationDbConnection) {
    //get the holiday_Assign_Id from holidaydata
    let holidayAssignIds = holidayData.map((data) => {
        return data.Holiday_Assign_Id
    })
    try {
        return (
            organizationDbConnection(ehrTables.holidayAssignment)
                .whereIn('Holiday_Assign_Id', holidayAssignIds)
                .del()
                .then((data) => {
                    if (data) {
                        return true
                    }else{
                        return false
                    }
                })
                .catch((e) => {
                    console.log('Error while deleting the location holidays', e)
                    return false
                })
        )
    }
    catch (e) {
        console.log("Error occur while deleting the location holidays", e);
        return false
    }
}