//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require table alias
const { ehrTables } = require('../../common/tablealias');
//Require constants
const { formName, formIds } = require('../../common/appconstants');
const { getOrganizationCoveragePreApprovalSettings } = require('../../common/commonfunctions');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');

module.exports.getPreApprovalEmployeeList = async (parent, args, context, info) => {
    let organizationDbConnection;
    let validationError = {};
    try {
        console.log("Inside getPreApprovalEmployeeList function()")

        let logInEmpId = context.Employee_Id;

        // get the organization database connection
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let isAdmin = 0;
        // check their rights
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, logInEmpId, formName.preApprovalRequests, '', 'UI', false, args.formId);
        if (Object.keys(checkRights).length === 0) {
            throw ('_DB0100');
        }

        else {
            var employeeIdsArray = [];
            if (checkRights.Role_View === 1) {
                // check loggedIn employee is  admin or not
                if (args.formId === formIds.preApprovalSelfService) {
                    employeeIdsArray.push(logInEmpId);
                }
                else if (checkRights.Employee_Role.toLowerCase() === 'admin') {
                    let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, logInEmpId, formName.serviceProviderAdmin, '', 'UI');
                    if (checkRights.Role_Update === 1) {
                        let getServiceProviderEmployees = await commonLib.func.getServiceProviderEmployees(organizationDbConnection, logInEmpId)
                        getServiceProviderEmployees = getServiceProviderEmployees.map(row => row.Employee_Id);
                        employeeIdsArray = getServiceProviderEmployees
                    } else {
                        isAdmin = 1;
                    }
                } else {
                    if (checkRights.Is_Manager === 1) {
                        let reportees = await commonLib.func.getEmployeeIdsOrManagerHierarchyBasedOnRole(organizationDbConnection, 1, logInEmpId, 0, isAdmin);
                        employeeIdsArray = reportees.employeeIdsArray;

                    } else {
                        employeeIdsArray = [logInEmpId];
                    }
                }

                let employeeList = await Promise.all([
                    getEmployeeListBasedOnPreApprovalType(organizationDbConnection, 'Work from home', employeeIdsArray),
                    getEmployeeListBasedOnPreApprovalType(organizationDbConnection, 'Work during week off', employeeIdsArray),
                    getEmployeeListBasedOnPreApprovalType(organizationDbConnection, 'Work during holiday', employeeIdsArray),
                    getEmployeeListBasedOnPreApprovalType(organizationDbConnection, 'On Duty', employeeIdsArray),
                    getEmployeeListBasedOnPreApprovalType(organizationDbConnection, 'Overtime Work', employeeIdsArray),
                ]);

                let responseData = {};

                if (employeeList.length > 0) {
                    responseData = {
                        'workFromHome': employeeList[0],
                        'workDuringWeekOff': employeeList[1],
                        'workDuringHoliday': employeeList[2],
                        'onDuty': employeeList[3],
                        'overtimeWork': employeeList[4]
                    }
                }

                //Destroy DB connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;

                //Return success response
                return { errorCode: '', message: 'Employees details retrieved successfully.', employeeDetails: responseData };
            }
            else {
                throw '_DB0100';
            }
        }
    }
    catch (e) {
        console.log('Error in the getPreApprovalEmployeeList() function catch block. ', e);
        organizationDbConnection ? organizationDbConnection.destroy() : null;

        errResult = commonLib.func.getError(e, 'CHR0060');
        // return response
        throw new ApolloError(errResult.message, errResult.code);
    }
}

async function getEmployeeListBasedOnPreApprovalType(organizationDbConnection, preApprovalType, employeeIdsArray) {
    try {
        let orgCoverage = await getOrganizationCoveragePreApprovalSettings(organizationDbConnection, preApprovalType);

        return (
            organizationDbConnection(ehrTables.empPersonalInfo + 'as EPI')
                .select(
                    "EPI.Employee_Id as employeeId",
                    "EJ.User_Defined_EmpId as userDefinedEmpId",
                    organizationDbConnection.raw("CONCAT_WS(' ',EPI.Emp_First_Name,EPI.Emp_Middle_Name, EPI.Emp_Last_Name) as employeeName"),
                )
                .from(ehrTables.empPersonalInfo + ' as EPI')
                .leftJoin(ehrTables.empJob + ' as EJ', 'EJ.Employee_Id', 'EPI.Employee_Id')
                .where(function () {
                    if (employeeIdsArray && employeeIdsArray.length) {
                        this.whereIn('EPI.Employee_Id', employeeIdsArray)
                    }
                })
                .where('EJ.Emp_Status', "Active")
                .modify(function (queryBuilder) {
                    if (Object.keys(orgCoverage).length === 0) {
                        queryBuilder.innerJoin(
                            ehrTables.customGroupEmployees + " as CGE",
                            "CGE.Employee_Id",
                            "EPI.Employee_Id"
                        ).innerJoin(
                            ehrTables.preApprovalSettings + " as PAS",
                            "CGE.Group_Id",
                            "PAS.Custom_Group_Id"
                        ).where("PAS.Pre_Approval_Type", preApprovalType)
                            .where("PAS.Status", "Active")
                            .whereIn("CGE.Type", ['Default', 'AdditionalInclusion']);
                    }
                })
                .groupBy('EPI.Employee_Id')
                .then(data => {
                    //Destroy DB connection
                    return data;
                })
                .catch(e => {
                    console.log('Error in the getEmployeeListBasedOnPreApprovalType() function .catch block. ', e);
                    return [];
                })
        )
    } catch (error) {
        console.log('Error in the getEmployeeListBasedOnPreApprovalType() function catch block. ', error);
        return [];
    }
}