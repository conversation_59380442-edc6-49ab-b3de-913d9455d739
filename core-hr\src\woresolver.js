const listWorkScheduleDetails = require('./roresolvers/workschedule/listWorkScheduleDetails');
const addWorkSchedule = require('./woresolvers/workschedule/addWorkSchedule');
const updateWorkSchedule = require('./woresolvers/workschedule/updateWorkSchedule');
const deleteWorkScheduleAndWeekOff = require('./woresolvers/workschedule/deleteWorkScheduleAndWeekOff');
const addWeekOff = require('./woresolvers/workschedule/addWeekOff');
const updateLeaveSettings = require('./woresolvers/updateLeaveSettings');
const deleteCustomGroupHolidays = require('./woresolvers/deleteCustomGroupHolidays');
const addCustomGroupHolidays = require('./woresolvers/addCustomGroupHolidays');
const updateCustomGroupHoliday = require('./woresolvers/updateCustomGroupHoliday');
const deleteProjectDetails = require('./woresolvers/project/deleteProjectDetails');
const addUpdateProjectDetails = require('./woresolvers/project/addUpdateProjectDetails');
const deleteDesignationDetails = require('./woresolvers/designation/deleteDesignationDetails');
const addUpdateDesignationDetails = require('./woresolvers/designation/addUpdateDesignationDetails');
const updateHolidaySettings = require('./woresolvers/updateHolidaySettings');
const updateRosterManagmentSetting = require('./woresolvers/updateRosterManagmentSetting');
const updateFormLevelCoverage = require('./woresolvers/updateFormLevelCoverage');
const bulkImportCustomGroupHolidays = require('./woresolvers/bulkImportCustomGroupHolidays');
const bulkImportLocationHolidays = require('./woresolvers/bulkImportLocationHolidays');
const addLocationHolidays = require('./woresolvers/locationHolidays/addLocationHolidays');
const updateLocationHoliday = require('./woresolvers/locationHolidays/updateLocationHoliday');
const deleteLocationHolidays = require('./woresolvers/locationHolidays/deleteLocationHolidays');
const addUpdateHoliday = require('./woresolvers/holidays/addUpdateHoliday');
const deleteHolidays = require('./woresolvers/holidays/deleteHolidays');
const triggerBulkInviteEmployees = require('./woresolvers/employees/triggerBulkInviteEmployees');
const updateUserAccountDetails = require('./woresolvers/employees/updateUserAccountDetails');
const updateUserAccounts = require('./woresolvers/employees/updateUserAccounts');
const importEmployeeData = require('./woresolvers/employees/importEmployeeData');
const updateShortTimeOffSettings = require('./woresolvers/settings/updateShortTimeOffSettings');
const updateOnDutySettings = require('./woresolvers/settings/updateOnDutySettings');
const addUpdatePreApprovalRequests = require('./woresolvers/addUpdatePreApprovalRequests');
const addUpdateCompOffRules = require('./woresolvers/addUpdateCompOff');
const addUpdateSpecialWages = require('./woresolvers/addUpdateSpecialWages');
const addUpdateOvertimeConfiguration = require('./woresolvers/addUpdateOvertimeConfiguration');
const addUpdatePreApprovalSettings = require('./woresolvers/addUpdatePreApprovalSettings');
const addUpdateBusinessUnit = require('./woresolvers/settings/businessUnit/addUpdateBusinessUnit');
const sendCommonEmail = require('./woresolvers/sendCommonEmail');
const addUpdateLopRecoverySettings = require('./woresolvers/settings/lopRecovery/addUpdateLopRecoverySettings');
const addUpdateProjectActivities = require('./woresolvers/project/addUpdateProjectActivities');
const deleteProjectActivity = require('./woresolvers/project/deleteProjectActivity');
const addUpdateActivitiesToProject = require('./woresolvers/project/addUpdateActivitiesToProject');
const deleteActivityAssociatedWithProject = require('./woresolvers/project/deleteActivityAssociatedWithProject');
const importProjectActivities = require('./woresolvers/project/importProjectActivities');
const addUpdateempTimesheet = require('./woresolvers/timesheet/addUpdateTimeSheetActivityDetails');
const deleteTimesheetActivity = require('./woresolvers/timesheet/deleteTimesheetActivity');
const timesheetApprovalWithdraw = require('./woresolvers/timesheet/timesheetApprovalWithdraw');
const cloneProject = require('./woresolvers/project/cloneProject');
const importAndMapProjectWithActivities = require('./woresolvers/project/importAndMapProjectWithActivities');
const timesheetSubmitForApproval = require('./woresolvers/timesheet/timesheetSubmitForApproval');
const timesheetApprovalReturn = require('./woresolvers/timesheet/timesheetApprovalReturn');
const addTimeSheetPrevWeek = require('./woresolvers/timesheet/addTimeSheetPrevWeek');
const addUpdateorganizationGroup = require('./woresolvers/organizationGroup/addUpdateOrganizationGroup');
const updateOrganizationStatus = require('./woresolvers/organizationGroup/updateOrganizationStatus');
const overrideEmployeeLeaves = require('./woresolvers/timeOffManagement/leaveOverride/overrideEmployeeLeaves');
const addUpdateLocation = require('./woresolvers/addUpdateLocation');
const addUpdateRooms = require('./woresolvers/project/room/addUpdateRoom');
const deleteRoom = require('./woresolvers/project/room/deleteRoom');
const deleteLocation = require('./woresolvers/deleteLocation');
const addUpdateCustomField = require('./woresolvers/customFields/addUpdateCustomField');
const addUpdateCustomEmailTemplate = require('./woresolvers/customEmailTemplates/addUpdateCustomEmailTemplate');
const addUpdateAirTicketSetting=require('./woresolvers/airTicketing/addUpdateAirTicketSettings');
const addUpdateEmployeeIdPrefixSettings=require('./woresolvers/employeeIdPrefix/addUpdateEmployeeIdPrefixSettings');
const updateEmployeeIdPrefixConfig=require('./woresolvers/employeeIdPrefix/updateEmployeeIdPrefixConfig');
const addUpdateGeneralSettings=require('./woresolvers/settings/addUpdateGeneralSettings');
const addUpdateEmployeeCustomTableHeader=require('./woresolvers/addUpdateEmployeeCustomTableHeader');


// Define resolver
const resolvers = {
    Query: Object.assign({},
        listWorkScheduleDetails
    ),
    Mutation: Object.assign({},
        addWorkSchedule,
        updateWorkSchedule,
        deleteWorkScheduleAndWeekOff,
        addWeekOff,
        updateLeaveSettings,
        deleteCustomGroupHolidays,
        addCustomGroupHolidays,
        updateCustomGroupHoliday,
        deleteProjectDetails,
        addUpdateProjectDetails,
        deleteDesignationDetails,
        addUpdateDesignationDetails,
        updateHolidaySettings,
        updateRosterManagmentSetting,
        updateFormLevelCoverage,
        bulkImportCustomGroupHolidays,
        bulkImportLocationHolidays,
        addLocationHolidays,
        updateLocationHoliday,
        deleteLocationHolidays,
        addUpdateHoliday,
        deleteHolidays,
        triggerBulkInviteEmployees,
        updateUserAccountDetails,
        updateUserAccounts,
        importEmployeeData,
        updateShortTimeOffSettings,
        updateOnDutySettings,
        addUpdatePreApprovalSettings,
        addUpdateBusinessUnit,
        addUpdatePreApprovalRequests,
        addUpdateCompOffRules,
        addUpdateSpecialWages,
        addUpdateOvertimeConfiguration,
        sendCommonEmail,
        addUpdateLopRecoverySettings,
        addUpdateProjectActivities,
        deleteProjectActivity,
        addUpdateActivitiesToProject,
        deleteActivityAssociatedWithProject,
        importProjectActivities,
        addUpdateempTimesheet,
        deleteTimesheetActivity,
        timesheetApprovalWithdraw,
        cloneProject,
        importAndMapProjectWithActivities,
        timesheetSubmitForApproval,
        timesheetApprovalReturn,
        addTimeSheetPrevWeek,
        addUpdateorganizationGroup,
        updateOrganizationStatus,
        overrideEmployeeLeaves,
        addUpdateLocation,
        addUpdateRooms,
        deleteRoom,
        deleteLocation,
        addUpdateCustomEmailTemplate,
        addUpdateCustomField,
        addUpdateAirTicketSetting,
        addUpdateEmployeeIdPrefixSettings,
        updateEmployeeIdPrefixConfig,
        addUpdateGeneralSettings,
        addUpdateEmployeeCustomTableHeader
    )
}
exports.resolvers = resolvers;