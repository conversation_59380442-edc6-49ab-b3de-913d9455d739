const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const knex = require('knex');
const { ApolloError } = require('apollo-server-lambda');
const { ehrTables } = require('../../../common/tablealias');

module.exports.listEmployeeIdPrefixSettings = async (_, args, context) => {
    let organizationDbConnection;
    try {

        let loginEmployeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);

        // Skip access check if formId is not 358
        let checkRights = {};
        let skipAccessCheck = false;

        if (args.formId !== 358) {
            // Skip access check
            skipAccessCheck = true;
            checkRights = { Role_View: 1, Employee_Role: 'admin' };
        } else {
            // Check access rights
            checkRights = await commonLib.func.checkEmployeeAccessRights(
                organizationDbConnection,
                loginEmployeeId,
                null,
                '',
                'UI',
                false,
                args.formId
            );
        }
        // Check if user has view rights and is an admin, or if access check is skipped
        if (skipAccessCheck || (Object.keys(checkRights).length > 0 && checkRights.Role_View === 1 &&
            checkRights.Employee_Role && checkRights.Employee_Role.toLowerCase() === 'admin')) {
            return (
                organizationDbConnection(ehrTables.empPrefixSettings + " as EPS")
                    .select(
                        "EPS.Emp_Prefix_Setting_Id as empPrefixSettingId",
                        "EPS.Service_Provider_Id as serviceProviderId",
                        "EPS.Prefix as prefix",
                        "EPS.Suffix as suffix",
                        "EPS.No_Of_Digits as noOfDigits",
                        "EPS.Next_Number as nextNumber",
                        "EPS.Status as status",
                        "EPS.Added_On as addedOn",
                        "EPS.Updated_On as updatedOn",
                        organizationDbConnection.raw("CONCAT_WS(' ',EPI1.Emp_First_Name,EPI1.Emp_Middle_Name,EPI1.Emp_Last_Name) as addedByName"),
                        organizationDbConnection.raw("CONCAT_WS(' ',EPI2.Emp_First_Name,EPI2.Emp_Middle_Name,EPI2.Emp_Last_Name) as updatedByName"),
                        "SP.Service_Provider_Name as serviceProviderName"
                    )
                    .leftJoin(ehrTables.empPersonalInfo + " as EPI1", "EPI1.Employee_Id", "EPS.Added_By")
                    .leftJoin(ehrTables.empPersonalInfo + " as EPI2", "EPI2.Employee_Id", "EPS.Updated_By")
                    .leftJoin(ehrTables.serviceProvider + " as SP", "SP.Service_Provider_Id", "EPS.Service_Provider_Id")
                    .modify(function(queryBuilder) {
                        // Filter by service provider ID if provided
                        if (args.serviceProviderId === 0) {
                            // If serviceProviderId is 0, filter for null service provider IDs (organization-wide)
                            queryBuilder.whereNull("EPS.Service_Provider_Id");
                        } else if (args.serviceProviderId) {
                            // If serviceProviderId is provided and not 0, filter for that specific service provider
                            queryBuilder.where("EPS.Service_Provider_Id", args.serviceProviderId);
                        }
                        // Filter by status if provided
                        if (args.status) {
                            queryBuilder.where("EPS.Status", args.status);
                        }
                    })
                    .orderBy("EPS.Added_On", "desc")
                    .then(async (data) => {
                        const processedData = data.map(item => {
                            if (!item.serviceProviderId) {
                                item.serviceProviderName = 'Organization-wide';
                            } else if (!item.serviceProviderName) {
                                item.serviceProviderName = 'Unknown';
                            }
                            return item;
                        });
                        const configPromise = organizationDbConnection(ehrTables.empPrefixConfig)
                            .select(
                                "Emp_Prefix_Config_Id as empPrefixConfigId",
                                "Is_Enabled as isEnabled",
                                "Config_Level as configLevel",
                                "Added_On as addedOn",
                                "Updated_On as updatedOn"
                            )
                            .leftJoin(ehrTables.empPersonalInfo + " as EPI1", "EPI1.Employee_Id", ehrTables.empPrefixConfig + ".Added_By")
                            .leftJoin(ehrTables.empPersonalInfo + " as EPI2", "EPI2.Employee_Id", ehrTables.empPrefixConfig + ".Updated_By")
                            .select(
                                organizationDbConnection.raw("CONCAT_WS(' ',EPI1.Emp_First_Name,EPI1.Emp_Middle_Name,EPI1.Emp_Last_Name) as addedByName"),
                                organizationDbConnection.raw("CONCAT_WS(' ',EPI2.Emp_First_Name,EPI2.Emp_Middle_Name,EPI2.Emp_Last_Name) as updatedByName")
                            )
                            .first();

                        const config = await configPromise;

                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        return {
                            errorCode: "",
                            message: "Employee ID prefix settings retrieved successfully.",
                            employeeIdPrefixSettings: processedData,
                            config: config || {
                                empPrefixConfigId: 0,
                                isEnabled: false,
                                configLevel: 'Organization',
                                addedOn: null,
                                addedByName: null,
                                updatedOn: null,
                                updatedByName: null
                            }
                        };
                    })
                    .catch(catchError => {

                        let errResult = commonLib.func.getError(catchError, 'EMP0003');
                        // Destroy DB connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        // Return error response
                        throw new ApolloError(errResult.message, errResult.code);
                    })
            );
        } else {
            if (args.formId !== 358) {
                throw ('_DB0100');
            }
            if (!checkRights || !checkRights.Employee_Role || checkRights.Employee_Role.toLowerCase() !== 'admin') {
                throw ('_DB0109');
            } else {
                throw ('_DB0100');
            }
        }
    } catch (error) {

        let errResult = commonLib.func.getError(error, 'EMP0004');
        // Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        // Return error response
        throw new ApolloError(errResult.message, errResult.code);
    }
};
