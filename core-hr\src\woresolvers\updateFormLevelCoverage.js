// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
let moment = require('moment-timezone');
// require common constant files

module.exports.updateFormLevelCoverage = async (parent, args, context, info) => {
    let organizationDbConnection;
    let validationError = {};
    try {
        console.log("Inside updateFormLevelCoverage function.")
        let loginEmployeeId = context.Employee_Id;
        let accessFormName = args.formName;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let activeRecordNotExist=await checkActiveRecords(organizationDbConnection,accessFormName);
        let formLevelCoverageUpdatedData = {
            "Coverage": args.Coverage,
            'Updated_On': moment().utc().format('YYYY-MM-DD HH:mm:ss'),
            'Updated_By': loginEmployeeId,

        }
        // check their rights
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, accessFormName, '', 'UI');
        if (Object.keys(checkRights).length > 0 && checkRights.Role_Update === 1) {
            if(activeRecordNotExist){
            return (
                organizationDbConnection(ehrTables.formLevelCoverage + " as FLC")
                    .update(formLevelCoverageUpdatedData)
                    .where('Coverage_Id',args.Coverage_Id).then((data) => {
                        if (data) {
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            return { errorCode: "", message: "Form level coverage updated successfully." };
                        }
                        else {
                            throw 'FLC0102';
                        }
                        //destroy the connection

                    }).catch((catchError) => {
                        console.log('Error while updaing form level coverage settings', catchError);
                        //Destroy DB connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;

                        let errResult = commonLib.func.getError(catchError, 'FLC0102');
                        throw new ApolloError(errResult.message, errResult.code);
                    })
            )
                }
                else{
                    throw('FLC0104'); 
                }
        }
        else {
            throw '_DB0102';
        }

    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in updateFormLevelCoverage function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'FLC0001');
        throw new ApolloError(errResult.message, errResult.code);
    }
}
async function checkActiveRecords(organizationDbConnection, formName) {
let tableName, status;
   try { 
    switch (formName) {
        case 'Comp Off':
            tableName = ehrTables.compoffConfiguration+ " as CC";
            status = "CC.Status";
            break;
        case 'Special Wages':
            tableName = ehrTables.specialWages + " as SW";
            status = "SW.Status";
            break;
        case 'LOP Recovery':
                tableName = ehrTables.lopRecoverySettings + " as LRS";
                status = "LRS.Configuration_Status";
                break;
        case 'Over Time':
            tableName = ehrTables.overtimeConfiguration + " as OT";
            status = "OT.Status";
            break;
        default:
            throw 'FLC0103';
    }
    const data = await organizationDbConnection(tableName)
            .select('*')
            .where(status, "Active");

        if (data && data.length > 0) {
            throw 'FLC0104';
        } 
            console.log("active record does not exist");
            return true;
    } catch (catchErr) {
        console.log("Error in active records for the form catch block", catchErr);
        throw 'FLC0104';
    }
}