const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const knex = require('knex');
const { ehrTables } = require('../../../common/tablealias');
const { ApolloError } = require('apollo-server-lambda');
const { formName, systemLogs, formIds } = require('../../../common/appconstants');
const { getTimesheetInstanceData, getTimeSheetEventId } = require('../../../common/commonfunctions');
const moment = require('moment');


module.exports.deleteTimesheetActivity = async (parent, args, context, info) => {
    let organizationDbConnection;

    try {
        // Extracting information from the context
        let loginEmployeeId = context.Employee_Id;
        let orgCode = context.Org_Code;

        // Creating a connection to the organization database using Knex
        organizationDbConnection = knex(context.connection.OrganizationDb);

        // Checking employee access rights
        const formId = args.selfService === 1 ? formIds.timeSheet : formIds.timeSheetMyTeam;
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, '', '', 'UI', false, formId);

        const { timesheetId, requestId, detailsBytimeId, parentDelete } = args;
        let projectFormId = formIds.projectSettings

        if (Object.keys(checkRights).length > 0 && ((checkRights.Role_Delete === 1 && args.selfService === 1)||(args.selfService===0 && (checkRights.Is_Manager === 1 || checkRights.Employee_Role.toLowerCase() === 'admin')))) {
            // Using try-catch block to handle errors
            try {
                // Main ransaction block
                await organizationDbConnection.transaction(async (trx) => {
                    // Fetching timesheet data for deletion
                    let timesheetRequestIdData = await organizationDbConnection(ehrTables.empTimesheet)
                        .transacting(trx)
                        .select('Approval_Status', 'Approver_Id', 'Employee_Id', 'Week_Ending_Date', 'Process_Instance_Id')
                        .where("Request_Id", requestId)

                    if (!timesheetRequestIdData || timesheetRequestIdData.length === 0) {
                        console.log('The record cannot be deleted');
                        throw 'CHR0093';
                    } else {
                        let timesheetData = await organizationDbConnection(ehrTables.empTimesheet)
                            .transacting(trx)
                            .select('Approval_Status', 'Approver_Id', 'Employee_Id', 'Week_Ending_Date', 'Process_Instance_Id')
                            .where("Request_Id", requestId)
                            .whereIn("Approval_Status", ["Applied", "Draft", 'Approved','Returned'])
                        if (!timesheetData || timesheetData.length === 0) {
                            console.log('The record is in rejected status and cannot be deleted');
                            throw 'CHR0094'
                        }
                        // If requestId is provided or no records left, delete timesheet data
                        let checkIsThisLastTimesheetRecord=await checkIfExistNoRecord(organizationDbConnection, trx,requestId);
                        // Archive timesheet data
                        if (parentDelete) {
                            if (Object.keys(timesheetData[0]).length > 0 && timesheetData[0].Approval_Status.toLowerCase() === 'applied') {
                                let processInstanceId = timesheetData[0].Process_Instance_Id;
                                await commonLib.func.deleteOldApprovalRecords(organizationDbConnection, processInstanceId, trx);
                                let updateParam = { Process_Instance_Id: null };
                                await commonLib.leaveCommonFunction.updateTableWithTrxBasedOnCondition(organizationDbConnection, ehrTables.empTimesheet, updateParam, trx, 'Request_Id', '=', args.requestId, "", "");
                            }
                            let empTimeSheetArchiveData = await commonLib.leaveCommonFunction.insertInTableWithTrx(organizationDbConnection, ehrTables.empTimesheetArchive, timesheetData, trx);
                            if (!empTimeSheetArchiveData) {
                                throw 'CHR0095';
                            }
                        }
                        // Archive timesheet hour tracking data
                        if (parentDelete || timesheetId) {
                            let hourTrackingData = await organizationDbConnection(ehrTables.timesheetHoursTracking)
                                .transacting(trx)
                                .select('Day1', 'Day2', 'Day3', 'Day4', 'Day5', 'Day6', 'Day7', 'Description', 'Project_Activity_Id', 'Project_Id', 'Request_Id', 'Timesheet_Id', 'Timesheet_Type', 'Added_By', 'Added_Date', 'Updated_By', 'Updated_On')
                                .modify(function (queryBuilder) {
                                    if (!parentDelete) {
                                        queryBuilder.where("Timesheet_Id", timesheetId);
                                    }
                                    else {
                                        queryBuilder.where("Request_Id", requestId);
                                    }
                                });
                            let hourTracking = hourTrackingData.map(element => {
                                return ({
                                    ...element,
                                    Deleted_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                                    Deleted_By: loginEmployeeId
                                })
                            })
                            if (hourTrackingData && hourTrackingData.length) {
                                await commonLib.leaveCommonFunction.insertInTableWithTrx(organizationDbConnection, ehrTables.timesheetHoursTrackingArchive, hourTracking, trx);
                            }
                        }

                        // Archive activity details by time
                        let data = await organizationDbConnection(ehrTables.timesheetHoursTracking + " as THT")
                            .transacting(trx)
                            .select('ADT.Details_Bytime_Id', 'ADT.Timesheet_Id', 'ADT.Day', 'ADT.Total_Hours', 'ADT.Notes', 'ADT.End_Time', 'ADT.Start_Time', 'ADT.Room_Id')
                            .innerJoin(ehrTables.activityDetailsBytime + " as ADT", "ADT.Timesheet_Id", "THT.Timesheet_Id")
                            .modify(function (queryBuilder) {
                                if (parentDelete || timesheetId) {
                                    if (parentDelete) {
                                        queryBuilder.where("THT.Request_Id", requestId);
                                    }
                                    else {
                                        queryBuilder.where("ADT.Timesheet_Id", timesheetId);
                                    }

                                } else if (detailsBytimeId) {
                                    queryBuilder.where("ADT.Details_Bytime_Id", detailsBytimeId);
                                }
                            });

                        if (data && data.length > 0) {
                            let activityTracking = data.map(element => {
                                return ({
                                    ...element,
                                    Deleted_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                                    Deleted_By: loginEmployeeId
                                })
                            })
                            await commonLib.leaveCommonFunction.insertInTableWithTrx(organizationDbConnection, ehrTables.activityDetailsBytimeArchive, activityTracking, trx);

                            // Delete activity details by time
                            await organizationDbConnection(ehrTables.activityDetailsBytime)
                                .innerJoin(ehrTables.timesheetHoursTracking + " as THT", "THT.Timesheet_Id", "activity_details_bytime.Timesheet_Id")
                                .del()
                                .transacting(trx)
                                .modify(function (queryBuilder) {
                                    if (parentDelete || timesheetId) {
                                        if (parentDelete) {
                                            queryBuilder.where("THT.Request_Id", requestId);
                                        } else {
                                            queryBuilder.where("THT.Timesheet_Id", timesheetId);
                                        }
                                    } else if (detailsBytimeId) {
                                        queryBuilder.where("Details_Bytime_Id", detailsBytimeId);
                                    }
                                });
                        }

                        // If timesheetId is provided, delete timesheet hour tracking data
                        if (timesheetId && !parentDelete) {
                            await organizationDbConnection(ehrTables.timesheetHoursTracking)
                                .del()
                                .transacting(trx)
                                .where("Timesheet_Id", timesheetId);
                            if (Object.keys(timesheetData[0]).length > 0 && timesheetData[0].Approval_Status.toLowerCase() === 'applied') {
                                let instanceData = await getTimesheetInstanceData(organizationDbConnection, args,trx);
                                if (Object.keys(instanceData).length > 0 && instanceData.Approval_Status.toLowerCase() === 'applied') {
                                    let eventId = await getTimeSheetEventId(organizationDbConnection);
                                    let oldInstanceId = instanceData.Process_Instance_Id;
                                    delete instanceData.Process_Instance_Id;
                                    let initiateTimesheetWorkflow = await commonLib.func.initiateWorkflow(eventId, instanceData, orgCode, projectFormId,loginEmployeeId);
                                    if (initiateTimesheetWorkflow.status == 200 && initiateTimesheetWorkflow.data && initiateTimesheetWorkflow.data.workflowProcessInstanceId) {
                                        await commonLib.func.deleteOldApprovalRecords(organizationDbConnection, oldInstanceId, trx);
                                        let updateParam = { Process_Instance_Id: initiateTimesheetWorkflow.data.workflowProcessInstanceId };
                                        await commonLib.leaveCommonFunction.updateTableWithTrxBasedOnCondition(organizationDbConnection, ehrTables.empTimesheet, updateParam, trx, 'Request_Id', '=', args.requestId, "", "");
                                    }
                                }

                            }

                        }
                        if (parentDelete || checkIsThisLastTimesheetRecord) {
                            if (parentDelete) {
                                await organizationDbConnection(ehrTables.timesheetHoursTracking)
                                    .del()
                                    .transacting(trx)
                                    .where("Request_Id", requestId);
                            }

                            await organizationDbConnection(ehrTables.empTimesheet)
                                .del()
                                .transacting(trx)
                                .where("Request_Id", requestId);
                        }



                        return true;
                    }
                })
                    .then(async (result) => {
                        if (result) {
                            let systemLogParam = {
                                action: systemLogs.roleDelete,
                                userIp: context.User_Ip,
                                employeeId: loginEmployeeId,
                                organizationDbConnection: organizationDbConnection,
                                message: ` The timesheet with timesheet id ${args.timesheetId} data is being deleted.`,
                            };
                            // Return the result for further processing if needed
                            await commonLib.func.createSystemLogActivities(systemLogParam);
                            organizationDbConnection ? organizationDbConnection.destroy() : null;

                            const successMessage = "timesheet activity data details deleted successfully.";
                            return { errorCode: "", message: successMessage };
                        }
                        else {
                            throw 'CHR0090'
                        }
                    }).catch((error) => {
                        //Destroy DB connection
                        throw error;
                    })
            } catch (catchError) {
                console.log('Error while deleting the employee timesheet details', catchError);
                throw catchError;
            }
        } else {
            if (Object.keys(checkRights).length > 0 && args.selfService === 0) {
                console.log("The employee does not have admin or manager access.");
                throw '_DB0114';
            }
            else {
                console.log("The employee does not have delete access.");
                throw '_DB0103';
            }
        }
    } catch (e) {
        console.log('Error in deleteTimesheetActivity function main block', e);

        // Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;

        // Handle and throw the ApolloError
        let errResult = commonLib.func.getError(e, 'CHR0096');
        throw new ApolloError(errResult.message, errResult.code);
    }
};
async function checkIfExistNoRecord(organizationDbConnection, trx,requestId) {
    let data = await organizationDbConnection(ehrTables.timesheetHoursTracking + " as THT")
        .transacting(trx).select('THT.Request_Id')
        .where("Request_Id", requestId);
    return data && data.length === 1 ? true : false
}