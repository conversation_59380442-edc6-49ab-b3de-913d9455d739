// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;

// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../common/tablealias');
//Require apollo server to return error message
const { UserInputError, ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formName, formIds, systemLogs } = require('../../common/appconstants');
//Require validation function
const { validateCommonRuleInput } = require('../../common/inputValidations');
const moment = require('moment');
const { getPreApprovalFormId } = require('../../common/commonfunctions');

module.exports.addUpdatePreApprovalSettings = async (parent, args, context, info) => {
    let organizationDbConnection;
    let errResult;
    let validationError = {};
    try {
        console.log("Inside addUpdatePreApprovalSettings function")
        let loginEmployeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        const { preApprovalConfigurationId, preApprovalType, coverage,maxDaysAllowedPerRequest, customGroupId, period, noOfPreApprovalRequest, restrictSandwich, restrictSandwichFor, advanceNotificationDays, workflowId, status, documentUpload, maxDaysForDocumentUpload, typeOfDay } = args;

        // check their rights
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, formName.preApprovalSettings, '', 'UI', false, formIds.preApprovalSetting);
        if (Object.keys(checkRights).length > 0 && ((preApprovalConfigurationId === 0 && checkRights.Role_Add === 1) || (preApprovalConfigurationId > 0 && checkRights.Role_Update === 1))) {
            // validationError = await validatePreApprovalSettingsInputs(args);
            const fieldValidations = {};
            if (maxDaysForDocumentUpload) {
                fieldValidations.maxDaysForDocumentUpload = "IVE0385";
                validationError = await validateCommonRuleInput(args, fieldValidations);
            }
            //Check validation error exist or not
            if (Object.keys(validationError).length === 0) {

                //Should not allow the user to inactivate the settings if any outstanding approval is there
                let outstandingPreApproval = 0;
                if (preApprovalConfigurationId) {
                    let settingsDetails = await organizationDbConnection(ehrTables.preApprovalSettings)
                        .select("*")
                        .where("Pre_Approval_Configuration_Id", preApprovalConfigurationId)
                        .then(async (settings) => {
                            return settings.length > 0 ? settings[0] : {};
                        }).catch((catchError) => {
                            console.log('Error while getting the pre-approval settings details', catchError);
                            throw ('CHR0041');
                        });


                    outstandingPreApproval = await organizationDbConnection(ehrTables.preApprovalRequests + " as PR")
                        .count("PR.Pre_Approval_Id as preApprovalCount")
                        .where("PR.Pre_Approval_Type", settingsDetails.Pre_Approval_Type)
                        .whereIn("PR.Status", ['Applied', 'Cancel Applied'])
                        .modify(function (queryBuilder) {
                            if (settingsDetails.Coverage.toLowerCase() === "custom group") {
                                queryBuilder.innerJoin(
                                    ehrTables.customGroupEmployees + " as CGE",
                                    "CGE.Employee_Id",
                                    "PR.Employee_Id"
                                );
                                queryBuilder.where("CGE.Group_Id", settingsDetails.Custom_Group_Id);
                                queryBuilder.whereIn("CGE.Type", ['AdditionalInclusion', 'Default']);
                            }
                        })
                        .then(async (preApprovalCount) => {
                            return preApprovalCount[0].preApprovalCount > 0 ? true : false;
                        }).catch((catchError) => {
                            console.log('Error while getting the outstanding pre-approval request details', catchError);
                            throw ('CHR0041');
                        })
                }

                if (outstandingPreApproval) {
                    throw ('CHR0042');
                }
                /** We should not allow the user to add an Organization level coverage if the Organization / custom group level coverge is already added 
                and the status is active. We should not allow the user to add an custom group level coverage if the Organization / same custom group level 
                coverge is already added and the status is active. */
                let settingExists = await organizationDbConnection(ehrTables.preApprovalSettings + " as PAS")
                    .select("PAS.Coverage", "PAS.Custom_Group_Id")
                    .where("PAS.Pre_Approval_Type", preApprovalType)
                    .whereIn("PAS.Status", ['Active'])
                    .where(function () {
                        if (preApprovalConfigurationId) {
                            this.whereNot('PAS.Pre_Approval_Configuration_Id', preApprovalConfigurationId)
                        }
                    })
                    .then(async (coverageDetails) => {
                        let errorCode;
                        coverageDetails.map((data) => {
                            if (coverage.toLowerCase() === "organization") {
                                errorCode = data.Coverage.toLowerCase() === "organization" ? 'CHR0045' : 'CHR0047';
                            } else if (coverage.toLowerCase() === "custom group") {
                                console.log(coverage, data.Coverage, customGroupId, data.Custom_Group_Id);
                                if (data.Coverage.toLowerCase() === "organization") {
                                    errorCode = 'CHR0046';
                                } else if (data.Coverage.toLowerCase() === "custom group" && data.Custom_Group_Id == customGroupId) {
                                    errorCode = 'CHR0048';
                                }
                            }
                        })

                        return errorCode;
                    }).catch((catchError) => {
                        console.log('Error while validating the coverage details', catchError);
                        throw ('CHR0049');
                    })
                if (settingExists) {
                    throw (settingExists);
                }

                let currentDateTime = moment().utc().format('YYYY-MM-DD HH:mm:ss');

                let preApprovalSettingsData = {
                    "Pre_Approval_Configuration_Id": preApprovalConfigurationId,
                    "Pre_Approval_Type": preApprovalType,
                    "Coverage": coverage,
                    "Custom_Group_Id": coverage.toLowerCase() === 'custom group' ? customGroupId : 0,
                    "Period": period,
                    "No_Of_Pre_Approval_Request": noOfPreApprovalRequest,
                    "Restrict_Sandwich": restrictSandwich,
                    "Restrict_Sandwich_For": restrictSandwichFor,
                    "Advance_Notification_Days": advanceNotificationDays,
                    "Workflow_Id": workflowId,
                    "Status": status,
                    "Document_Upload": documentUpload,
                    "Max_Days_For_Document_Upload": maxDaysForDocumentUpload,
                    "Type_Of_Day": typeOfDay,
                    "Max_Days_Allowed_Per_Request": maxDaysAllowedPerRequest
                }

                //If the preApprovalConfigurationId is not zero then update the record
                if (preApprovalConfigurationId) {
                    //If it is a update action then update the Updated_on and Updated_By
                    preApprovalSettingsData.Updated_On = currentDateTime;
                    preApprovalSettingsData.Updated_By = loginEmployeeId;

                    return (
                        organizationDbConnection
                            .transaction(function (trx) {
                                return (
                                    organizationDbConnection(ehrTables.preApprovalSettings)
                                        .update(preApprovalSettingsData)
                                        .where("Pre_Approval_Configuration_Id", preApprovalConfigurationId)
                                        .transacting(trx)
                                        .then(async (updateResult) => {

                                            if (coverage.toLowerCase() === 'custom group' && customGroupId) {
                                                let approvalFormId = getPreApprovalFormId(preApprovalType);
                                                let customGroupData = [{ "Parent_Id": preApprovalConfigurationId, "Form_Id": approvalFormId, "Custom_Group_Id": customGroupId }];
                                                /**Delete the custom group association */
                                                organizationDbConnection(ehrTables.customGroupAssociated)
                                                    .del()
                                                    .where('Parent_Id', preApprovalConfigurationId)
                                                    .where('Form_Id', approvalFormId)
                                                    .then().catch((catchError) => {
                                                        console.log('Error while deleting the custom group associtaion', catchError);
                                                    })

                                                console.log('insert....', preApprovalConfigurationId, approvalFormId);
                                                /** Insert the custom group association */
                                                await commonLib.func.insertIntoTable(organizationDbConnection, ehrTables.customGroupAssociated, customGroupData);
                                            }

                                            // Log message: Add projects Project_Id - 1
                                            let systemLogParams = {
                                                action: systemLogs.roleUpdate,
                                                userIp: context.User_Ip,
                                                employeeId: loginEmployeeId,
                                                formName: formName.preApprovalSettings,
                                                trackingColumn: 'Pre_Approval_Configuration_Id',
                                                organizationDbConnection: organizationDbConnection,
                                                uniqueId: preApprovalConfigurationId
                                            };
                                            //Call function to add the system log
                                            await commonLib.func.createSystemLogActivities(systemLogParams);
                                            return true;
                                        })
                                )
                            }).then((data) => {
                                //destroy the connection
                                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                return { errorCode: "", message: "Pre-approval settings updated successfully." };

                            }).catch((catchError) => {
                                console.log('Error while updaing the pre-approval settings details', catchError);
                                //Destroy DB connection
                                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                let errorCode = catchError.code === 'ER_DUP_ENTRY' ? 'CHR0040' : 'CHR0039';
                                errResult = commonLib.func.getError(errorCode, 'CHR0039');
                                throw new ApolloError(errResult.message, errResult.code);
                            })
                    )


                } else {
                    //If it is a add action then update the Added_on and Added_By
                    preApprovalSettingsData.Added_On = currentDateTime;
                    preApprovalSettingsData.Added_By = loginEmployeeId;

                    return (
                        organizationDbConnection
                            .transaction(function (trx) {
                                return (
                                    organizationDbConnection(ehrTables.preApprovalSettings)
                                        .insert(preApprovalSettingsData)
                                        .transacting(trx)
                                        .then(async (insertData) => {
                                            let preApprovalConfigurationId = insertData[0];
                                            if (coverage.toLowerCase() === 'custom group' && customGroupId) {
                                                let approvalFormId = getPreApprovalFormId(preApprovalType);
                                                /** Insert the custom group association */
                                                let customGroupData = [{ "Parent_Id": preApprovalConfigurationId, "Form_Id": approvalFormId, "Custom_Group_Id": customGroupId }];
                                                await commonLib.func.insertIntoTable(organizationDbConnection, ehrTables.customGroupAssociated, customGroupData);
                                            }
                                            // Log message: Add projects Project_Id - 1
                                            let systemLogParams = {
                                                action: systemLogs.roleAdd,
                                                userIp: context.User_Ip,
                                                employeeId: loginEmployeeId,
                                                formName: formName.preApprovalSettings,
                                                trackingColumn: 'Project_Name',
                                                organizationDbConnection: organizationDbConnection,
                                                uniqueId: preApprovalConfigurationId
                                            };
                                            //Call function to add the system log
                                            await commonLib.func.createSystemLogActivities(systemLogParams);
                                            return true;
                                        })
                                )
                            }).then((data) => {
                                //destroy the connection
                                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                return { errorCode: "", message: "Pre-approval details added successfully." };

                            }).catch((catchError) => {
                                console.log('Error while adding the pre-approval settings', catchError);
                                //Destroy DB connection
                                organizationDbConnection ? organizationDbConnection.destroy() : null;

                                let errorCode = catchError.code === 'ER_DUP_ENTRY' ? 'CHR0040' : 'CHR0038';
                                errResult = commonLib.func.getError(errorCode, 'CHR0038');
                                throw new ApolloError(errResult.message, errResult.code);
                            })
                    )
                }
            } else {
                throw 'IVE0000';
            }
        }
        else {
            if (preApprovalConfigurationId) {
                console.log("The employee does not have edit access.");
                throw '_DB0102';
            } else {
                console.log("The employee does not have add access.");
                throw '_DB0101';
            }
        }
    }
    catch (e) {
        console.log('Error in addUpdatePreApprovalSettings function main catch block.', e);
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        //If validation error exist
        if (e === 'IVE0000') {
            console.log('Validation error in the addUpdatePreApprovalSettings function', validationError);
            errResult = commonLib.func.getError('', 'IVE0000');
            throw new UserInputError(errResult.message, { validationError: validationError });//Return error response
        } else {
            errResult = commonLib.func.getError(e, 'CHR0039');
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
}