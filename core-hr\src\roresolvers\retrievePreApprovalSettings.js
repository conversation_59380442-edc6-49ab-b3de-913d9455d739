// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;

// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formName } = require('../../common/appconstants');
const { getPreApprovalsTakenCount, getStartAndEndDate, getOrganizationCoveragePreApprovalSettings, getCustomGroupCoveragePreApprovalSettings } = require('../../common/commonfunctions');

let organizationDbConnection;
module.exports.retrievePreApprovalSettings = async (parent, args, context, info) => {
    try {
        console.log("Inside retrievePreApprovalSettings function.")
        let loginEmployeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        const { employeeId, preApprovalType, startDate, endDate, preApprovalId } = args;

        // get the access rights of the login employee for the accreditation form
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, formName.preApprovalRequests, '', 'UI', false, args.formId);
        if (Object.keys(checkRights).length > 0 && checkRights.Role_View === 1) {

            let preApprovalSettings = {};
            /** Get the organization level Pre-approval settings */
            preApprovalSettings = await getOrganizationCoveragePreApprovalSettings(organizationDbConnection, preApprovalType);

            if (Object.keys(preApprovalSettings).length === 0) {
                /** Get the Custom group level Pre-approval settings */
                preApprovalSettings = await getCustomGroupCoveragePreApprovalSettings(organizationDbConnection, preApprovalType, employeeId)
            }

            if (Object.keys(preApprovalSettings).length > 0) {
                /** Get the count of pre-approval request applied for the employee */
                if (startDate) {
                    const { start, end } = await getStartAndEndDate(new Date(startDate), preApprovalSettings.period);
                    let preApprovalsTaken = await getPreApprovalsTakenCount(organizationDbConnection, employeeId, preApprovalType, start, end, preApprovalId, preApprovalSettings.typeOfDay);
                    preApprovalSettings.preApprovalsTaken = preApprovalsTaken;
                }

                //destroy the connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                return { errorCode: "", message: "Pre-approval settings details retrieved successfully.", preApprovalSettings: preApprovalSettings };
            } else {
                throw ('CHR0051');
            }
        }
        else {
            console.log('Employee do not have view access rights');
            throw '_DB0100';
        }

    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in retrievePreApprovalSettings function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'CHR0043');
        throw new ApolloError(errResult.message, errResult.code);
    }
}


