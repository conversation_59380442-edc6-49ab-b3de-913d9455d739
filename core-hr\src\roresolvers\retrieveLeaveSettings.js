// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formName,formIds } = require('../../common/appconstants');


let organizationDbConnection;
module.exports.retrieveLeaveSettings = async (parent, args, context, info) => {
    try {
        console.log("Inside retrieveLeaveSettings function.")
        let employeeId = context.Employee_Id;
        let accessFormName = args.formName ? args.formName : formName.leaveSettings;
        let accessFormId = args.formId ? args.formId : formIds.leaveSettings;
        ;
        let leaveSettingsRetrieved = {};
        organizationDbConnection = knex(context.connection.OrganizationDb);

        const checkRights = await commonLib.func.checkEmployeeAccessRights(
            organizationDbConnection,
            employeeId,
            null,
            '',
            'UI',
            false,
            accessFormId
          );
        if (Object.keys(checkRights).length > 0 && checkRights.Role_View === 1) {
            return (
                organizationDbConnection(ehrTables.leaveSettings + " as LS")
                    .select('*', organizationDbConnection.raw("CONCAT_WS(' ',EPI.Emp_First_Name,EPI.Emp_Middle_Name, EPI.Emp_Last_Name) as updatedByName"))
                    .leftJoin(ehrTables.empPersonalInfo + " as EPI", "EPI.Employee_Id", "LS.Updated_By")
                    .from(ehrTables.leaveSettings + " as LS")
                    .then((data) => {
                        if(data.length){
                            leaveSettingsRetrieved.Allow_Upline_Managers_Approval = data[0].Allow_Upline_Managers_Approval == 1 ? 'Yes' : 'No';
                            leaveSettingsRetrieved.Enable_CAMU_Scheduler = data[0].Enable_CAMU_Scheduler ? data[0].Enable_CAMU_Scheduler : 'No';
                            leaveSettingsRetrieved.Enable_Workflow = data[0].Enable_Workflow ? data[0].Enable_Workflow : 'No';
                            leaveSettingsRetrieved.Enforce_Comment_For_Approval = data[0].Enforce_Comment_For_Approval ? data[0].Enforce_Comment_For_Approval : 'No';
                            leaveSettingsRetrieved.Enforce_Comment_For_Leave = data[0].Enforce_Comment_For_Leave ? data[0].Enforce_Comment_For_Leave : 'No';
                            leaveSettingsRetrieved.Enforce_Alternate_Person_For_Leave = data[0].Enforce_Alternate_Person_For_Leave ? data[0].Enforce_Alternate_Person_For_Leave : 'No';
                            leaveSettingsRetrieved.Coverage_For_Alternate_Person = data[0].Coverage_For_Alternate_Person ? data[0].Coverage_For_Alternate_Person : 'Department';
                            leaveSettingsRetrieved.Updated_On = data[0].Updated_On;
                            leaveSettingsRetrieved.updatedByName = data[0].updatedByName;
                            //destroy the connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            return { errorCode: "", message: "Leave settings retrieved successfully.", leaveSettings: leaveSettingsRetrieved };
                        }else{
                            console.log('No leave settings data found')
                            throw 'EM0264'
                        }
                    })
                    .catch((err)=>{
                        console.log('Error in retrieveLeaveSettings .catch() block', err);
                        throw 'EM0264'
                    })
            )
        }
        else {
            throw '_DB0100';
        }

    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in retrieveLeaveSettings function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'EM00116');
        throw new ApolloError(errResult.message, errResult.code);
    }
}
