// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formName } = require('../../../common/appconstants');


let organizationDbConnection;
module.exports.retrieveOnDutySettings = async (parent, args, context, info) => {
    try {
        console.log("Inside retrieveOnDutySettings function.")
        let employeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        // check their rights
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, employeeId, formName.onDutySettings, '', 'UI');
        if (Object.keys(checkRights).length > 0 && checkRights.Role_View === 1) {
            return (
                organizationDbConnection(ehrTables.onDutySettings + " as ODS")
                    .select('*', organizationDbConnection.raw("CONCAT_WS(' ',EPI.Emp_First_Name,EPI.Emp_Middle_Name, EPI.Emp_Last_Name) as updatedByName"))
                    .leftJoin(ehrTables.empPersonalInfo + " as EPI", "EPI.Employee_Id", "ODS.Updated_By")
                    .then((data) => {
                        if(data){                            
                            //destroy the connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            return { errorCode: "", message: "On Duty settings retrieved successfully.", onDutySettings: data[0] };
                        }else{
                            console.log('Error while retrieving on duty settings')
                            throw 'CHS0102'
                        }
                    })
                    .catch((err)=>{
                        console.log('Error in retrieveOnDutySettings .catch() block', err);
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        let errResult = commonLib.func.getError(err, 'CHS0102'); 
                        throw new ApolloError(errResult.message, errResult.code);
                    })
            )
        }
        else {
            throw '_DB0100';
        }

    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in retrieveOnDutySettings function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'CHS0002');
        throw new ApolloError(errResult.message, errResult.code);
    }
}
