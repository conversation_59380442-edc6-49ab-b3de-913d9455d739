// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../common/tablealias');
//Require apollo server to return error message
const { UserInputError, ApolloError } = require('apollo-server-lambda');
// require common constant files
const {formIds } = require('../../../common/appconstants');
//Require validation function
const { validateCommonRuleInput } = require('../../../common/inputValidations');
const moment = require('moment');

let organizationDbConnection;

module.exports.addUpdateActivitiesToProject = async (parent, args, context, info) => {
    console.log('Inside addUpdateActivitiesToProject function');
    let validationError = {};

    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);
        const projectActivityId = args.projectActivityId;
        const fieldValidations = {};

        if (args.description != null) {
            fieldValidations.description = "IVE0415";
        }

        if (fieldValidations) {
            validationError = await validateCommonRuleInput(args, fieldValidations);
        }

        let loginEmployeeId = context.Employee_Id;
        let checkRights = await commonLib.func.checkEmployeeAccessRights(
            organizationDbConnection,
            loginEmployeeId,
            null,
            '',
            'UI',
            false,
            formIds.projects
        );

        if (
            Object.keys(checkRights).length > 0 &&
            ((projectActivityId === 0 && checkRights.Role_Add === 1) ||
                (projectActivityId > 0 && checkRights.Role_Update === 1))
        ) {
            if (Object.keys(validationError).length) {
                throw 'IVE0000';
            }
            let activityData, operationData;
            activityData = await organizationDbConnection(ehrTables.projectActivities)
                .select('*')
                .where('Project_Id', args.projectId)
                .where('Activity_Id', args.activityId)
                .modify(function (queryBuilder) {
                    if (projectActivityId) {
                        queryBuilder.whereNot('Project_Activity_Id', args.projectActivityId);
                    }
                });
            if (activityData && !activityData.length) {
                if (projectActivityId) {
                    operationData = await organizationDbConnection(ehrTables.projectActivities)
                        .update({
                            Project_Activity_Id: args.projectActivityId,
                            Project_Id: args.projectId,
                            Activity_Id: args.activityId,
                            Is_Billable: args.isBillable,
                            Activity_From: args.activityFrom,
                            Activity_To: args.activityTo,
                            Modified_Date: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                            Updated_By: loginEmployeeId,
                        })
                        .where('Project_Activity_Id', args.projectActivityId);
                } else {
                    operationData = await organizationDbConnection(ehrTables.projectActivities)
                        .insert({
                            Project_Id: args.projectId,
                            Activity_Id: args.activityId,
                            Is_Billable: args.isBillable,
                            Activity_From: args.activityFrom,
                            Activity_To: args.activityTo,
                            Added_Date: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                            Added_By: loginEmployeeId,
                        });
                }

                if (operationData) {
                    let systemLogParam = {
                        userIp: context.User_Ip,
                        employeeId: loginEmployeeId,
                        organizationDbConnection: organizationDbConnection,
                        message: `Activity Id ${args.activityId} was ${
                            projectActivityId ? 'updated' : 'added'
                        } by ${loginEmployeeId}`,
                    };

                    await commonLib.func.createSystemLogActivities(systemLogParam);
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    return {
                        errorCode: '',
                        message: `Project activity has been ${
                            projectActivityId ? 'updated' : 'added'
                        } successfully.`,
                    };
                } else {
                    throw projectActivityId ? 'CHR0103' : 'CHR0105';
                }
            } else {
                throw 'CHR0112';
            }
        } else {
            if (projectActivityId) {
                console.log("The employee does not have edit access.");
                throw '_DB0102';
            } else {
                console.log("The employee does not have add access.");
                throw '_DB0101';
            }
        }
    } catch (mainCatchError) {
        console.log('Error in addUpdateActivitiesToProject function main catch block.', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;

        if (mainCatchError === 'IVE0000') {
            let errResult = commonLib.func.getError('', 'IVE0000');
            console.log('Validation error in addUpdateActivitiesToProject function - ', validationError);
            // return response
            throw new UserInputError(errResult.message, { validationError: validationError });
        } else {
            let errResult = commonLib.func.getError(mainCatchError, 'CHR0077');
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
};
