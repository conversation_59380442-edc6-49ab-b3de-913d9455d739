// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;

// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../common/tablealias');
//Require apollo server to return error message
const { UserInputError, ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formName, formIds, systemLogs } = require('../../../common/appconstants');
//Require validation function
const {validateProjectsInputs} = require('../../../common/inputValidations');
const {getProjectCoverage, deleteProjectAdditionalDetails, insertDataInTable}=require('../../../common/commonfunctions')

module.exports.addUpdateProjectDetails = async (parent, args, context, info) => {
    let organizationDbConnection;
    let errResult;
    let validationError = {};
    let newProjectId = null;
    try {
        console.log("Inside addUpdateProjectDetails function...")
        let loginEmployeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        const { projectId, projectName, clientName, managerId, locationId, status, description, employeeId, customGroupId, accreditationId } = args;
        
        // check their rights
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, formName.projects, '', 'UI');
        if (Object.keys(checkRights).length > 0 && ((projectId === 0 && checkRights.Role_Add === 1) || (projectId > 0 && checkRights.Role_Update === 1)) ) {
            let projectCoverage = await getProjectCoverage(organizationDbConnection);
            validationError = await validateProjectsInputs(args, projectCoverage);
            //Check validation error exist or not
            if (Object.keys(validationError).length === 0) {
            
                let queryProject = organizationDbConnection(ehrTables.projectDetails)
                .select('Project_Name')
                .where('Project_Name', projectName);
                if(projectId){
                    queryProject = queryProject.whereNot('Project_Id',projectId)
                }
                
                let projectNameExist = await queryProject
                    .then((result) => {
                        return result;
                    }).catch((e) => {
                        console.log('Error in checking the project name .catch block', e);
                        throw(commonLib.func.getError(e, 'CHR0025'));
                    })
                
                if(projectNameExist.length === 0){
                    //Get the login employee current date and time based on login employee location
                    let currentDateTime = await commonLib.func.getEmployeeTimeZone(loginEmployeeId, organizationDbConnection, 1);

                    let projectData = {"Project_Id": projectId,
                    "Project_Name": projectName,
                    "Client_Name":clientName,
                    "Manager_Id":managerId,
                    "Location_Id":locationId,
                    "Client_Name":clientName,
                    "Status":status,
                    "Description":description}

                    if(projectId){
                        //If it is a update action then update the Updated_on and Updated_By
                        projectData.Updated_On = currentDateTime;
                        projectData.Updated_By = loginEmployeeId;

                        return(
                            organizationDbConnection
                            .transaction(function (trx) {
                                return(
                                    organizationDbConnection(ehrTables.projectDetails)
                                    .update(projectData)
                                    .where("Project_Id",projectId)
                                    .transacting(trx)
                                    .then(async(projectUpdateData) => {
                                        await deleteProjectAdditionalDetails(organizationDbConnection, trx, projectId);
                                        await addProjectAdditionalDetails(organizationDbConnection, trx, projectId, projectCoverage, accreditationId, employeeId, customGroupId);

                                        // Log message: Add projects Project_Id - 1
                                        let systemLogParams = {
                                            action: systemLogs.roleUpdate,
                                            userIp: context.User_Ip,
                                            employeeId: loginEmployeeId,
                                            formName: formName.projects,
                                            trackingColumn: 'Project_Name',
                                            organizationDbConnection: organizationDbConnection,
                                            uniqueId: projectName
                                        };
                                        //Call function to add the system log
                                        await commonLib.func.createSystemLogActivities(systemLogParams);
                                        return true;
                                    })
                                )
                            }).then((data) => {
                                //destroy the connection
                                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                return { errorCode: "", message: "Project details updated successfully.", projectId: projectId}; 
                                
                            }).catch((e) => {
                                console.log('Error while updaing the project details transaction .catch block', e);
                                //Destroy DB connection
                                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                errResult = commonLib.func.getError(e, 'CHR0017');
                                throw new ApolloError(errResult.message, errResult.code);
                            })
                        )


                    } else {
                        //If it is a add action then update the Added_on and Added_By
                        projectData.Added_On = currentDateTime;
                        projectData.Added_By = loginEmployeeId;

                        return(
                            organizationDbConnection
                            .transaction(function (trx) {
                                return(
                                    organizationDbConnection(ehrTables.projectDetails)
                                    .insert(projectData)
                                    .transacting(trx)
                                    .then(async(projectInsertData) => {
                                        let projectInsertId = projectInsertData[0];
                                        newProjectId = projectInsertId;
                                        await addProjectAdditionalDetails(organizationDbConnection, trx, projectInsertId, projectCoverage, accreditationId, employeeId, customGroupId);
                                        
                                        // Log message: Add projects Project_Id - 1
                                        let systemLogParams = {
                                            action: systemLogs.roleAdd,
                                            userIp: context.User_Ip,
                                            employeeId: loginEmployeeId,
                                            formName: formName.projects,
                                            trackingColumn: 'Project_Name',
                                            organizationDbConnection: organizationDbConnection,
                                            uniqueId: projectName
                                        };
                                        //Call function to add the system log
                                        await commonLib.func.createSystemLogActivities(systemLogParams);
                                        return true;
                                    })
                                )
                            }).then((data) => {
                                //destroy the connection
                                console.log("My data:", data)
                                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                return { errorCode: "", message: "Project details added successfully.", projectId: newProjectId}; 
                                
                            }).catch((e) => {
                                console.log('Error while adding the project details transaction .catch block', e);
                                //Destroy DB connection
                                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                errResult = commonLib.func.getError(e, 'CHR0014');
                                throw new ApolloError(errResult.message, errResult.code);
                            })
                        )
                    }
                    
                } else {
                    console.log("Project name already exists")
                    validationError['IVE0279'] = commonLib.func.getError('', 'IVE0279').message1;
                    throw('IVE0000');
                }
            } else {
                throw 'IVE0000';
            } 
        }
        else {
            if(projectId){
                console.log("The employee does not have edit access.");
                throw '_DB0102';
            } else {
                console.log("The employee does not have add access.");
                throw '_DB0101';
            }
        }
        
        

    }
    catch (e) {
        console.log('Error in addUpdateProjectDetails function main catch block.', e);
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        //If validation error exist
        if (e === 'IVE0000') {
            console.log('Validation error in the addUpdateProjectDetails function', validationError);
            errResult = commonLib.func.getError('', 'IVE0000');
            throw new UserInputError(errResult.message, { validationError: validationError });//Return error response
        } else {
            errResult = commonLib.func.getError(e, 'CHR0018');
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
}


async function addProjectAdditionalDetails(organizationDbConnection, trx, projectId, projectCoverage, accreditationId, employeeId, customGroupId){
    try{
        if(accreditationId && accreditationId.length > 0){
            let projectAccreditationData = [];
            accreditationId.map((accreditation)=>{
                projectAccreditationData.push({"Project_Id":projectId, "Accreditation_Category_And_Type_Id":accreditation});
            });
            
            await insertDataInTable(organizationDbConnection, trx, projectAccreditationData, ehrTables.projectAccreditationCategoryTypeMapping);

        }
        if(projectCoverage === "Employee" && employeeId && employeeId.length > 0){
            let employeeProjectData = [];
            employeeId.map((employeeId)=>{
                employeeProjectData.push({"Project_Id":projectId, "Employee_Id":employeeId});
            });
            
            await insertDataInTable(organizationDbConnection, trx, employeeProjectData, ehrTables.empProject);
        }
        if(projectCoverage === "CUSTOMGROUP" && customGroupId){

            let customGroupProjectData = [{"Parent_Id":projectId, "Form_Id":formIds.projects, "Custom_Group_Id":customGroupId}];
           
            await insertDataInTable(organizationDbConnection, trx, customGroupProjectData, ehrTables.customGroupAssociated);
        }
    } catch(addError){
        console.log('Error in addProjectAdditionalDetails main catch block', addError);
        throw('CHR0015');
    }
}
