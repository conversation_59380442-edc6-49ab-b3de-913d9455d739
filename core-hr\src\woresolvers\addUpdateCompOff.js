// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../common/tablealias');
//Require apollo server to return error message
const { UserInputError, ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formName, systemLogs, formIds } = require('../../common/appconstants');
//Require validation function
const { validateCommonRuleInput } = require('../../common/inputValidations');
const moment = require('moment');

module.exports.addUpdateCompOffRules = async (parent, args, context, info) => {
    let organizationDbConnection;
    let errResult;
    let validationError = {};
    let formId = formIds.compOff;
    let errorMessage = "";
    try {
        let loginEmployeeId = context.Employee_Id;

        organizationDbConnection = knex(context.connection.OrganizationDb);
        const Comp_Off_Coverage = await getCoverage(organizationDbConnection, formId);
        const { Configuration_Id, CustomGroup_Id, Comp_Off_Expiry_Type, Comp_Off_Expiry_Days, Comp_Off_Encashment,Comp_Off_Balance_Approval, Encashment_Mode, Comp_Off_Threshold, Allow_Half_Day_Comp_Off_Credit, Fixed_Regular_Hours, Salary_Type, Work_Day_Type, Status, Workflow_Approval, Comp_Off_Applicability_For_Overtime_Hours, Minimum_OT_Hours_For_Full_Day_Comp_Off, Minimum_OT_Hours_For_Half_Day_Comp_Off, Minimum_Hours_For_Half_Day_Comp_Off } = args;
        // check their rights
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, formName.compOff, '', 'UI');
        if (Object.keys(checkRights).length > 0 && ((Configuration_Id === 0 && checkRights.Role_Add === 1) || (Configuration_Id > 0 && checkRights.Role_Update === 1))) {
        
            const fieldValidations = {
            };

            if(Minimum_Hours_For_Half_Day_Comp_Off){
                fieldValidations.Minimum_Hours_For_Half_Day_Comp_Off = "IVE0432"
            }

            if (Comp_Off_Expiry_Days) {
                fieldValidations.Comp_Off_Expiry_Days = "IVE0370";
            }

            if (Fixed_Regular_Hours) {
                fieldValidations.Fixed_Regular_Hours = "IVE0371";
            }

            if (Minimum_OT_Hours_For_Full_Day_Comp_Off) {
                fieldValidations.Minimum_OT_Hours_For_Full_Day_Comp_Off = "IVE0372";
            }

            if (Minimum_OT_Hours_For_Half_Day_Comp_Off) {
                fieldValidations.Minimum_OT_Hours_For_Half_Day_Comp_Off = "IVE0373";
            }
            validationError = await validateCommonRuleInput(args, fieldValidations);
            if (Minimum_OT_Hours_For_Full_Day_Comp_Off || Minimum_OT_Hours_For_Half_Day_Comp_Off) {
                if (Comp_Off_Applicability_For_Overtime_Hours && (Comp_Off_Applicability_For_Overtime_Hours === "Both Full Day & Half Day" && (Minimum_OT_Hours_For_Full_Day_Comp_Off <= Minimum_OT_Hours_For_Half_Day_Comp_Off))) {
                    validationError['IVE0374'] = commonLib.func.getError('', 'IVE0374').message;
                }
            }
            if (Comp_Off_Balance_Approval) {
                if(!(['Automatic', 'Manual'].includes(Comp_Off_Balance_Approval))){
                   throw 'SCR0107'
                }
            }
            if (Object.keys(validationError).length > 0) {
                throw ('IVE0000');
            }

            
                return (
                    organizationDbConnection(ehrTables.compoffConfiguration + " as CC")
                        .select('CC.Configuration_Id', 'CC.Salary_Type', 'CC.Work_Day_Type', 'FLC.Coverage')
                        .leftJoin(ehrTables.formLevelCoverage + " as FLC", "FLC.Form_Id", formId)
                        .where("CC.Salary_Type", Salary_Type)
                        .where("CC.Work_Day_Type", Work_Day_Type)
                        .where("CC.Status", "Active")
                        .modify(function (queryBuilder) {


                            // Conditionally add the 'Configuration_Id' filter
                            if (Configuration_Id !== 0) {
                                queryBuilder.whereNot('CC.Configuration_Id', Configuration_Id);
                            }
                            if ((CustomGroup_Id && Comp_Off_Coverage.toLowerCase() === "custom group") || (CustomGroup_Id && Configuration_Id)) {
                                queryBuilder.leftJoin(ehrTables.customGroupAssociated + " as CGA", "CC.Configuration_Id", "CGA.Parent_Id");
                                queryBuilder.where("CGA.Custom_Group_Id", CustomGroup_Id);
                                queryBuilder.where("CGA.Form_Id", formId);
                                queryBuilder.select('CGA.Custom_Group_Id');
                            }
                        })
                        .then(async (customGroupSettings) => {
                            if ((Comp_Off_Coverage === "Organization" && CustomGroup_Id) || (Comp_Off_Coverage.toLowerCase() === "custom group" && !CustomGroup_Id)) {
                                throw 'SCR0106'
                            }

                            else if 
                            (customGroupSettings.length > 0) {
                                console.log("duplicate record exist")
                                throw 'SCR0104';
                            }
                            
                            else {
                                let currentDateTime = moment().utc().format('YYYY-MM-DD HH:mm:ss');
                                let compOffData = {
                                    "Comp_Off_Expiry_Type": Comp_Off_Expiry_Type,
                                    "Comp_Off_Expiry_Days": Comp_Off_Expiry_Days,
                                    "Comp_Off_Encashment": Comp_Off_Encashment,
                                    "Comp_Off_Balance_Approval": Comp_Off_Balance_Approval,
                                    "Encashment_Mode": Encashment_Mode,
                                    "Comp_Off_Threshold": Comp_Off_Threshold,
                                    "Allow_Half_Day_Comp_Off_Credit": Allow_Half_Day_Comp_Off_Credit,
                                    "Fixed_Regular_Hours": Fixed_Regular_Hours,
                                    'Minimum_Hours_For_Half_Day_Comp_Off': Minimum_Hours_For_Half_Day_Comp_Off,
                                    "Salary_Type": Salary_Type,
                                    "Work_Day_Type": Work_Day_Type,
                                    "Status": Status,
                                    "Workflow_Approval": Workflow_Approval,
                                    "Comp_Off_Applicability_For_Overtime_Hours": Comp_Off_Applicability_For_Overtime_Hours,
                                    "Minimum_OT_Hours_For_Full_Day_Comp_Off": Minimum_OT_Hours_For_Full_Day_Comp_Off,
                                    "Minimum_OT_Hours_For_Half_Day_Comp_Off": Minimum_OT_Hours_For_Half_Day_Comp_Off,
                                }

                                if (Configuration_Id) {
                                    let existingCompOffData = await organizationDbConnection(ehrTables.compoffConfiguration)
                                                        .select('*')
                                                        .where('Configuration_Id', Configuration_Id)
                                                        .first();
                                    if(existingCompOffData && Object.keys(existingCompOffData).length > 0){
                                        //Remove the unwanted fields
                                        delete existingCompOffData.Updated_On;
                                        delete existingCompOffData.Updated_By;
                                        //Add the added on and added by fields
                                        existingCompOffData.Added_On = currentDateTime;
                                        existingCompOffData.Added_By = loginEmployeeId;
                                    }
                                    compOffData.Updated_On = currentDateTime;
                                    compOffData.Updated_By = loginEmployeeId;

                                    let instanceData = compOffData;
                                    instanceData.Added_On = compOffData.Added_On;
                                    instanceData.Added_By = compOffData.Added_By;
                                    return (
                                        organizationDbConnection
                                            .transaction(function (trx) {
                                                return (
                                                    organizationDbConnection(ehrTables.compoffConfiguration + " as CC")
                                                        .where("CC.Configuration_Id", Configuration_Id)
                                                        .update(compOffData)
                                                        .transacting(trx)
                                                        .then(async (updateResult) => {
                                                            if (updateResult) {
                                                                if(existingCompOffData && Object.keys(existingCompOffData).length > 0){
                                                                    //Insert the history record
                                                                    await organizationDbConnection(ehrTables.compoffConfigurationHistory)
                                                                    .insert(existingCompOffData)
                                                                    .transacting(trx);
                                                                }
                                                                if (CustomGroup_Id) {
                                                                    let customGroupData = [{ "Parent_Id": Configuration_Id, "Form_Id": 250, "Custom_Group_Id": CustomGroup_Id }];
                                                                    /**Delete the custom group association */
                                                                    await organizationDbConnection(ehrTables.customGroupAssociated)
                                                                        .del()
                                                                        .where('Form_Id', 250)
                                                                        .where('Parent_Id', Configuration_Id)

                                                                        .then().catch((catchError) => {
                                                                            console.log('Error while deleting the custom group associtaion', catchError);
                                                                            organizationDbConnection ? organizationDbConnection.destroy() : null;

                                                                            errResult = commonLib.func.getError(catchError, 'SCR0103');
                                                                            throw new ApolloError(errResult.message, errResult.code);
                                                                        })
                                                                    /** Insert the custom group association */
                                                                    await commonLib.func.insertIntoTable(organizationDbConnection, ehrTables.customGroupAssociated, customGroupData);
                                                                }
                                                            }
                                                            else {
                                                                throw 'SCR0102'
                                                            }
                                                            // Log message: Add projects Project_Id - 1
                                                            let systemLogParams = {
                                                                action: systemLogs.roleUpdate,
                                                                userIp: context.User_Ip,
                                                                employeeId: loginEmployeeId,
                                                                formName: formName.compOff,
                                                                trackingColumn: 'Configuration_Id',
                                                                organizationDbConnection: organizationDbConnection,
                                                                uniqueId: Configuration_Id
                                                            };
                                                            //Call function to add the system log
                                                            await commonLib.func.createSystemLogActivities(systemLogParams);
                                                            return true;
                                                        })
                                                )
                                            }).then((data) => {
                                                if (data) {
                                                    //destroy the connection
                                                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                                                    return { errorCode: "", message: "Comp off rules details updated successfully." };
                                                }
                                                else {
                                                    throw 'SCR0102';
                                                }

                                            }).catch((catchError) => {
                                                console.log('Error while updaing the comp off rules details', catchError);
                                                //Destroy DB connection
                                                organizationDbConnection ? organizationDbConnection.destroy() : null;

                                                errResult = commonLib.func.getError(catchError, 'SCR0102');
                                                throw new ApolloError(errResult.message, errResult.code);
                                            })
                                    )
                                } else {
                                    //If it is a add action then update the Added_on and Added_By
                                    compOffData.Added_On = currentDateTime;
                                    compOffData.Added_By = loginEmployeeId;
                                    return (
                                        organizationDbConnection(ehrTables.compoffConfiguration)
                                            .insert(compOffData)
                                            .leftJoin(ehrTables.customGroupAssociated + " as CGA", function () {
                                                this.on("CC.Configuration_Id", "=", "CGA.Parent_Id")
                                                    .andOn("CGA.Form_Id", "=", formId);
                                            })
                                            .then(async (insertData) => {
                                                if (insertData && insertData.length) {
                                                    if (CustomGroup_Id) {
                                                        /** Insert the custom group association */
                                                        let customGroupData = [{ "Parent_Id": insertData[0], "Form_Id": 250, "Custom_Group_Id": CustomGroup_Id }];
                                                        await commonLib.func.insertIntoTable(organizationDbConnection, ehrTables.customGroupAssociated, customGroupData);
                                                    }
                                                }
                                                else {
                                                    throw 'SCR0102'
                                                }
                                                let systemLogParams = {
                                                    action: systemLogs.roleAdd,
                                                    userIp: context.User_Ip,
                                                    employeeId: loginEmployeeId,
                                                    formName: formName.compOff,
                                                    trackingColumn: 'Configuration_Id',
                                                    organizationDbConnection: organizationDbConnection,
                                                    uniqueId: Configuration_Id
                                                };
                                                await commonLib.func.createSystemLogActivities(systemLogParams);
                                                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                                return { errorCode: "", message: "Comp off rules details added successfully." };
                                                //Call function to add the system log

                                            })
                                    )
                                }
                            }
                        })
                        .catch((err) => {
                            //Destroy DB connection
                            errResult = commonLib.func.getError(err, 'SCR0103');
                            if(errResult.code === 'SCR0106'){
                                let errorMessage = `Comp Off configuration is set to ${Comp_Off_Coverage === 'Organization' ? 'Custom Group' : 'Organization'}, conflicting with the platform's ${Comp_Off_Coverage} level setting. Please adjust the platform to ${Comp_Off_Coverage} or establish a new configuration.`; 
                                errResult.message= errorMessage;
                            }
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            // throw err;
                            throw new ApolloError(errResult.message, errResult.code);
                        }))

            

        }

        else {
            if (Configuration_Id) {
                console.log("The employee does not have edit access.");
                throw '_DB0102';
            } else {
                console.log("The employee does not have add access.");
                throw '_DB0101';
            }
        }
    }
    catch (e) {
        console.log('Error in addUpdateCompOffRules  function main catch block.', e);
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        //If validation error exist
        if (e === 'IVE0000') {
            console.log('Validation error in the addUpdateCompOffRules  function', validationError);
            errResult = commonLib.func.getError('', 'IVE0000');
            throw new UserInputError(errResult.message, { validationError: validationError });//Return error response
        }
        else {
            errResult = commonLib.func.getError(e, 'SCR0002');
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
}
async function getCoverage(organizationDbConnection, formId) {
    try {
        return (
            organizationDbConnection(ehrTables.formLevelCoverage + " as FLC")
                .select('FLC.Coverage')
                .where("FLC.Form_Id", formId)
                .then(async (coverage) => {
                    if (coverage && coverage.length > 0) {
                        return coverage[0].Coverage;
                    } else {
                        console.log("Coverage does not exist for this form");
                        throw ('SCR0105');
                    }
                })
                .catch((catchErr) => {
                    console.log("Error in getCoverage .catch block", catchErr);
                    throw ('SCR0105');
                })
        )
    } catch (mainErr) {
        console.log('Error in getCoverage catch block', mainErr);
        throw ('SCR0105');
    }
}