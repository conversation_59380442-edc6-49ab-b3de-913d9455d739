// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const {formIds } = require('../../common/appconstants');
const moment = require('moment');

const formatMinMaxDate = (date) => moment(date).format('YYYY-MM-DD');
const retrieveActivityData = async (organizationDbConnection, projectActivityId) => {
    return organizationDbConnection(ehrTables.timesheetHoursTracking + " as TSHT")
    .select('ET.Week_Ending_Date')
    .leftJoin(ehrTables.empTimesheet + " as ET", "ET.Request_Id", "TSHT.Request_Id")
    .where('TSHT.Project_Activity_Id', projectActivityId);
};

const processActivityData = (data) => {
    let resultArray = [];
    if (data && data.length) {
        // const dates = data.map(obj => moment(obj.Week_Ending_Date));
        const dates = data
            .filter(obj => obj.Week_Ending_Date !== null) // Filter out objects with null Week_Ending_Date
            .map(obj => moment(obj.Week_Ending_Date));
        let maxDate = moment.max(dates).clone();
        let minDate = moment.min(dates).subtract(6, 'days');
        resultArray = [{ minDate: formatMinMaxDate(minDate), maxDate: formatMinMaxDate(maxDate) }];
    }
    return resultArray;
};

module.exports.retrieveMinMaxDateForActivity = async (parent, args, context, info) => {
    console.log('Inside retrieveMinMaxDateForActivity function');
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let loginEmployeeId = context.Employee_Id;
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, null, '', 'UI', false, formIds.projects);
        if (Object.keys(checkRights).length > 0 && checkRights.Role_View === 1) {
            const data = await retrieveActivityData(organizationDbConnection, args.projectActivityId);
            const resultArray = processActivityData(data);

            // Destroy DB connection
            organizationDbConnection && organizationDbConnection.destroy();
            return {
                errorCode: '',
                message: 'Activity details have been retrieved successfully.',
                activityData: resultArray ? resultArray : data
            };
        } else {
            console.log('Employee does not have view access rights');
            throw '_DB0100';
        }
    } catch (e) {
        // Destroy DB connection
        organizationDbConnection && organizationDbConnection.destroy();
        console.log('Error in retrieveMinMaxDateForActivity function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'CHR0084');
        throw new ApolloError(errResult.message, errResult.code);
    }
};
