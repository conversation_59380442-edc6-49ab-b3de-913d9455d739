//Require moment
const moment = require('moment-timezone');
const knex = require('knex');
// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//get tablealias
const { appManagerTables, ehrTables } = require("../../common/tablealias")
const { defaultValues } = require('../../common/appconstants');

let currentUtcDateTime;
module.exports.processSystemProcess = async (event, context) => {
    let appmanagerDbConnection;
    let organizationDbConnection;
    let inputParams = { Complete_Status: "InProgress" };
    let completeStatus = 'Failed';
    let inputStatus = event.input.status;
    try {
        currentUtcDateTime=moment.utc().format("YYYY-MM-DD");
        if (inputStatus && inputStatus.toLowerCase() === 'failed') {
            inputStatus = 'Failed'
        }
        else if (inputStatus && inputStatus.toLowerCase() === 'inprogress') {
            inputStatus = 'InProgress'
        }
        else {
            inputStatus = 'Open'
        }
        let masterTable = appManagerTables.systemProcessManager;
        let databaseConnection = await commonLib.stepFunctions.getConnection(process.env.stageName, process.env.dbPrefix, process.env.dbSecretName, process.env.region, '', 1);
        // check whether data exist or not
        if (Object.keys(databaseConnection).length) {
            appmanagerDbConnection = knex(databaseConnection.AppManagerDb);
            let openInstances = await commonLib.func.getDataFromTableAccordingToStatus(appmanagerDbConnection, masterTable, 'Complete_Status', inputStatus);
            if (openInstances && openInstances.length > 0) {
                //number of instances should be processed in one call
                let instanceToBeProcessed = (openInstances.length > defaultValues.activeInstanceToBeProcessed) ? (defaultValues.activeInstanceToBeProcessed) : (openInstances.length);
                for (let i = 0; i < instanceToBeProcessed; i++) {
                    let orgCode = openInstances[i]['Org_Code'];
                    inputParams = { Complete_Status: "InProgress" };
                    await commonLib.func.updateTableBasedOnCondition(appmanagerDbConnection, masterTable, inputParams, "Org_Code", orgCode)
                    let orgRegionDetails = await commonLib.func.getOrgRegionDetails(appmanagerDbConnection, orgCode);
                    let partnerId = await commonLib.func.getPartnerIntegration(appmanagerDbConnection,orgCode);
                    if (orgRegionDetails && Object.keys(orgRegionDetails).length > 0) {
                        let additionalHeaders = await commonLib.stepFunctions.formAdditionalHeaders(orgRegionDetails);
                        //Get database connection
                        let connection = await commonLib.stepFunctions.getConnection(process.env.stageName, process.env.dbPrefix, process.env.dbSecretName, process.env.region, orgCode, 0, additionalHeaders);
                        if (Object.keys(connection).length > 0) {
                            organizationDbConnection = knex(connection.OrganizationDb);
                            
                            await pushAirTicketSummary(orgCode, partnerId);
                            await pushExitDataToSyntrum(organizationDbConnection, orgCode, partnerId);
                            let resignationCallResponse = await resignationCall(organizationDbConnection, partnerId);
                            let deleteAttendenceImportResponse = await deleteAttendenceImport(organizationDbConnection);
                            let deleteBiometricErrorLogResponse = await deleteBiometricErrorLog(organizationDbConnection);
                            let updateEmpJobResponse = await updateEmpJob(organizationDbConnection);
                            let updateEmpLeaveCuttOffResponse = await updateEmpLeaveCuttOff(organizationDbConnection);
                            if (resignationCallResponse && deleteAttendenceImportResponse && deleteBiometricErrorLogResponse && updateEmpJobResponse && updateEmpLeaveCuttOffResponse) {
                                completeStatus = "Success";
                            }
                        }
                    }
                    inputParams = { Complete_Status: completeStatus };
                    await commonLib.func.updateTableBasedOnCondition(appmanagerDbConnection, masterTable, inputParams, "Org_Code", orgCode);
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                }
            }
        }
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        appmanagerDbConnection ? appmanagerDbConnection.destroy() : null;
        let response = {
            nextStep: 'End',
            input: { 'status': inputStatus },
            message: 'Event triggered to end the process because no instance is remaining to process.'
        }
        return response;
    }
    catch (e) {
        console.log('Error in processSystemProcess main catch block', e)
        appmanagerDbConnection ? appmanagerDbConnection.destroy() : null;
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        let response = {
            nextStep: 'End',
            input: { 'status': inputStatus },
            message: 'Event triggered to end the process because error occurred.'
        }
        return response;
    }
}

async function resignationCall(organizationDbConnection, partnerId) {
    try {
        return (
            organizationDbConnection
                .transaction(async (trx) => {
                    await resignationApprovalInsertInTimestamp(organizationDbConnection, trx)
                    await revokeRefreshToken(organizationDbConnection, trx, partnerId)
                    await resignationApprovalEvent(organizationDbConnection, trx);
                    return true;
                })
                .catch(e => {
                    console.log('Error in resignationCall .catch block', e);
                    return false;
                })
        )
    }
    catch (e) {
        console.log('Error in resignationCall main catch block', e)
        return false;
    }
}

async function resignationApprovalInsertInTimestamp(organizationDbConnection, trx) {
    try {
        return (
            organizationDbConnection
                .raw('INSERT INTO employee_info_timestamp_log(Employee_Id,Action,Log_Timestamp) select `EJ`.`Employee_Id`,"InActive",NOW() from `emp_job` as `EJ` inner join `emp_resignation` as `ER` on `EJ`.`Employee_Id` = `ER`.`Employee_Id` where `EJ`.`Emp_Status` = "Active" and `ER`.`Resignation_Date` <= "'+currentUtcDateTime+'" and `ER`.`Approval_Status` = "Approved" group by `EJ`.`Employee_Id`')
                .transacting(trx)
                .then(data => {
                    return true;
                })
                .catch(e => {
                    console.log('Error in resignationApprovalInsertInTimestamp .catch block', e);
                    throw (e);
                })
        )
    }
    catch (e) {
        console.log('Error in resignationApprovalInsertInTimestamp main catch block', e);
        throw (e);
    }
}

async function revokeRefreshToken(organizationDbConnection, trx, partnerId) {
    try {
        return (
            //Getting the Employee's Employee_Id and Firebase User Id to revoke access and delete the user id
            
            organizationDbConnection(ehrTables.empJob)
                .select('EJ.Employee_Id', 'EU.Firebase_Uid')
                .from('emp_job AS EJ')
                .innerJoin('emp_resignation AS ER', 'EJ.Employee_Id', 'ER.Employee_Id')
                .innerJoin('emp_user AS EU', 'EJ.Employee_Id', 'EU.Employee_Id')
                .where('EJ.Emp_Status', '=', 'Active')
                .where('ER.Resignation_Date', '<=', currentUtcDateTime)
                .where('ER.Approval_Status', '=', 'Approved')
                .whereNotNull('EU.firebase_uid')
                .groupBy('EJ.Employee_Id')
                .transacting(trx)
                .then(async (data) => {
                    if (data && data.length) {
                        //Get the Firebase Uid and Employee_Ids in an seperate array
                        let firebaseUids = [];
                        let employeeIds = [];
                        for (let i = 0; i < data.length; i++) {
                            firebaseUids.push(data[i].Firebase_Uid);
                            employeeIds.push(data[i].Employee_Id);
                        }

                        //Call the Firebase Function to revoke the access
                        for (let i = 0; i < firebaseUids.length; i++) {
                            await commonLib.firebase.revokeRefreshToken(partnerId, process.env.region, process.env.dbSecretName, firebaseUids[i])
                        }

                        //Update the Firebase User Id as null
                        return (
                            organizationDbConnection(ehrTables.empUser)
                                .whereIn('Employee_Id', employeeIds)
                                .update({ Firebase_Uid: null })
                                .transacting(trx)
                                .then(data => {
                                    if (data) {
                                        return true
                                    }
                                })
                                .catch((e)=>{
                                    console.log('Error while updating the firebase uid as null', e)
                                    throw (e);
                                })
                        )
                    }
                })
                .catch((e) => {
                    console.log('Error in revokeRefreshToken main catch block', e)
                    throw (e);
                })
        )
    }
    catch (e) {
        console.log('Error in revokeRefreshToken main catch block', e)
        throw (e);
    }
}

async function resignationApprovalEvent(organizationDbConnection, trx) {
    try {
        return (
            organizationDbConnection
                .raw('Update emp_job a inner join emp_resignation b on a.Employee_Id = b.Employee_Id left join emp_user c on c.Employee_Id = b.Employee_Id left join em_members d on d.Employee_Id = b.Employee_Id left join asset_management e on e.Employee_Id = d.Employee_Id set a.Emp_Status = "InActive",a.Emp_Email = null,a.Emp_InActive_Date = b.Resignation_Date,a.Reason_Id=b.Reason_Id, c.Firebase_Uid = NULL, d.Member_Status = "Inactive", d.Updated_On = NOW(), d.Updated_By = b.Updated_By, e.Employee_Id= NULL, e.Updated_On = NOW() where  b.Approval_Status LIKE "Approved" AND b.Resignation_Date <= "'+currentUtcDateTime+'" AND a.Emp_Status = "Active"')
                .transacting(trx)
                .then(data => {
                    return true;
                })
                .catch(e => {
                    console.log('Error while resignationApprovalEvent .catch block', e);
                    throw (e);
                })
        )
    }
    catch (e) {
        console.log('Error in resignationApprovalEvent main catch block', e);
        throw (e);
    }
}

async function updateEmpLeaveCuttOff(organizationDbConnection) {
    try {
        return (
            organizationDbConnection
                .raw('Update emp_leaves a inner join leave_types b on a.LeaveType_Id = b.LeaveType_Id set a.Approval_Status = "Returned" where hour(timediff(CURRENT_TIMESTAMP, a.Added_On)) > b.Leave_Approval_Cutoff')
                .then(data => {
                    return true;
                })
                .catch(e => {
                    console.log('Error in updateEmpLeaveCuttOff .catch block', e);
                    return false;
                })
        )
    }
    catch (e) {
        console.log('Error in updateEmpLeaveCuttOff main catch block', e);
        return false;
    }
}

async function updateEmpJob(organizationDbConnection) {
    try {
        return (
            organizationDbConnection
                .raw('Update emp_job a inner join designation b on a.Designation_Id = b.Designation_Id  set a.Confirmed =1,a.Confirmation_Date = DATE_ADD(a.Probation_Date, INTERVAL 1 DAY) where a.Confirmed = 0 AND a.Probation_Date <= "'+currentUtcDateTime+'" AND a.Emp_Status = "Active" AND b.Employee_Confirmation="Automatic"')
                .then(data => {
                    return true;
                })
                .catch(e => {
                    console.log('Error in updateEmpJob .catch block', e);
                    return false;
                })
        )
    }
    catch (e) {
        console.log('Error in updateEmpJob main catch block', e);
        return false;
    }
}

async function deleteBiometricErrorLog(organizationDbConnection) {
    try {
        return (
            organizationDbConnection
                .raw('DELETE FROM `biometric_error_log` WHERE Added_On <= DATE_SUB(NOW(), INTERVAL (SELECT Attendance_Log_Retention_Period FROM `org_details`) DAY)')
                .then(data => {
                    return true;
                })
                .catch(e => {
                    console.log('Error in deleteBiometricErrorLog .catch block', e);
                    return false;
                })
        )
    }
    catch (e) {
        console.log('Error in deleteBiometricErrorLog main catch block', e);
        return false;
    }
}

async function deleteAttendenceImport(organizationDbConnection) {
    try {
        return (
            organizationDbConnection
                .raw('DELETE FROM `emp_attendance_import` WHERE Added_On <= DATE_SUB(NOW(), INTERVAL (SELECT Attendance_Log_Retention_Period FROM `org_details`) DAY) AND Rollup_Flag IN(-1,3)')
                .then(data => {
                    return true;
                })
                .catch(e => {
                    console.log('Error in deleteAttendenceImport .catch block', e);
                    return false;
                })
        )
    }
    catch (e) {
        console.log('Error in deleteAttendenceImport main catch block', e);
        return false;
    }
}


async function pushExitDataToSyntrum(organizationDbConnection, orgCode, partnerId) {
    
    try {

        const syntrumResignationActive = await commonLib.func.integrationAPIIsEnabled(organizationDbConnection, {integrationType: 'Syntrum', direction: 'Push', action: 'Create', entityType: 'Resignation'});
        if(syntrumResignationActive){
            const resignationIds = await organizationDbConnection('emp_job as EJ').pluck('ER.Resignation_Id')
            .innerJoin('emp_resignation as ER', 'EJ.Employee_Id', 'ER.Employee_Id').where('ER.Approval_Status', 'LIKE', 'Approved')
            .where('ER.Resignation_Date', '<=', currentUtcDateTime).where('EJ.Emp_Status', '=', 'Active')
            .groupBy('ER.Resignation_Id').orderBy('ER.Resignation_Id', 'asc');

            if(resignationIds && resignationIds.length){
                const params = { orgCode, partnerId, employeeIds: resignationIds, entityType: 'resignation', action: 'create'};
                console.log('Syntrum Resignation Push API request params => ', params);
                await commonLib.stepFunctions.triggerStepFunction(process.env.asyncSyntrumAPIStepFunction, 'asyncSyntrumAPIStepFunction', null, params);
            }
        } else {
            console.log('Syntrum Resignation Push API is not enabled');
        }

    } catch (error) {
        console.error('Error occured in pushExitDataToSyntrum main catch block ', error);
        return false;
    }
}

async function pushAirTicketSummary(orgCode, partnerId) {
    
    try {

        const employeeIds = await organizationDbConnection('emp_job as EJ').pluck('EJ.Employee_Id')
        .innerJoin('emp_resignation as ER', 'EJ.Employee_Id', 'ER.Employee_Id')
        .innerJoin('emp_air_ticket_policy as EATP', "EJ.Employee_Id", "EATP.Employee_Id")
        .where('ER.Approval_Status', 'LIKE', 'Approved').where('EATP.Status', 'Active')
        .where('ER.Resignation_Date', '<=', currentUtcDateTime).where('EJ.Emp_Status', '=', 'Active')
        .groupBy('EJ.Employee_Id').orderBy('EJ.Employee_Id', 'asc');

        if(employeeIds && employeeIds.length){
            const params = { orgCode, partnerId, employeeIds: employeeIds, employeeIsInActiveAction: true};
            await commonLib.stepFunctions.triggerStepFunction(process.env.processAirTicketSummary, 'processAirTicketSummary', null, params);
        }

    } catch (error) {
        console.error('Error occured in pushAirTicketSummary main catch block ', error);
        return false;
    }

}