// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');

module.exports.retrieveLogoPath = async (parent, args, context, info) => {
    try {
        console.log("Inside retrieveLogoPath function.")

        let organizationDbConnection;
        organizationDbConnection = knex(context.connection.OrganizationDb);

        let partnerId = context.partnerid, orgCode=context.Org_Code;
       
        let logoPath = await organizationDbConnection('org_details')
            .select('Use_Report_Logo_As_Product_Logo', 'Report_LogoPath')
            .first().then((orgDetail) =>{
                let logoPath
                if (orgDetail.Use_Report_Logo_As_Product_Logo === 'Yes') {
                    logoPath =  `hrapp_upload/${orgCode}_tmp/logos/${orgDetail.Report_LogoPath}`
                }else{
                    if (partnerId === 'camu') {
                        logoPath = `hrapp_upload/platform/partners/camu/logo.png`; // Hardcoded path
                    } else if (partnerId === 'entomo') {
                        logoPath = `hrapp_upload/platform/partners/entomo/logo.png`; // Hardcoded path
                    }
                }
                return logoPath;
            }).catch((err) => {
                console.error('Error in retrieveLogoPath .catch() block', err);
                return '';
            })
        
        // Check if the logo path exists, and fallback to the default logo if it doesn't
        const fileExists = await checkFileExists(logoPath)
        if (!fileExists) {
            logoPath = `hrapp_upload/platform/default/logo.png`;
        } 
        logoPath = await commonLib.func.getFileURL(process.env.region, process.env.logoBucket, logoPath);    
        //destroy the connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        return { errorCode: "", message: "logo retrieved successfully.", logoPath: logoPath };
               
    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.error('Error in retrieveLogoPath function main catch block.', e);
        let errResult = commonLib.func.getError(e, '_UH0001');
        throw new ApolloError(errResult.message, errResult.code);
    }
}

async function checkFileExists(filePath) {
    try {
        const AWS = require('aws-sdk');
        // Create object for s3 bucket
        const s3 = new AWS.S3({ region: process.env.region });

        await s3.headObject({ Bucket: process.env.logoBucket, Key: filePath }).promise();
        return true;
    } catch (error) {
        return false;
    }
}
