//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
let moment = require('moment-timezone');
//Require table alias
const { ehrTables } = require('../../common/tablealias');
const { formName } = require('../../common/appconstants');

//function to update holiday settings
module.exports.updateHolidaySettings = async (parent, args, context, info) => {
    console.log('Inside updateHolidaySettings function');
    let organizationDbConnection;
    let validationError = {};
    try {
        let loginEmployeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, formName.coreHr, '', 'UI');

        //Input Validation
        if (args.holidaySettings && args.holidaySettings !== 'LOCATION' && args.holidaySettings !== 'CUSTOMGROUP') {
            validationError['IVE0290'] = commonLib.func.getError('', 'IVE0290').message;
        }

        if (Object.keys(validationError).length === 0) {
            if (Object.keys(checkRights).length > 0 && checkRights.Role_Update === 1) {
                return (
                    organizationDbConnection
                        .transaction(function (trx) {
                            if (args.holidaySettings) {
                                return (
                                    organizationDbConnection(ehrTables.holidayCustomGroup)
                                        .count('Holiday_Assign_Id as holidayCustomGroupCount')
                                        .transacting(trx)
                                        .then((customGroupCount) => {
                                            if (customGroupCount && customGroupCount.length > 0 && customGroupCount[0].holidayCustomGroupCount === 0) {
                                                return (
                                                    organizationDbConnection(ehrTables.holidayAssignment)
                                                        .count('Holiday_Assign_Id as holidayLocationCount')
                                                        .transacting(trx)
                                                        .then((locationCount) => {
                                                            if (locationCount && locationCount.length > 0 && locationCount[0].holidayLocationCount === 0) {
                                                                return (
                                                                    organizationDbConnection(ehrTables.holidaySettings)
                                                                        .update({
                                                                            'Holiday_Settings_Type': args.holidaySettings,
                                                                            'Display_Personal_Choice_Holiday_In_Dashboard': args.displayPersonalChoiceHolidays,
                                                                            'Updated_On': moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                                                                            'Updated_By': loginEmployeeId
                                                                        })
                                                                        .transacting(trx)
                                                                        .then((data) => {
                                                                            if (data) {
                                                                                return 'success'
                                                                            }
                                                                        })
                                                                )
                                                            }
                                                            else {
                                                                //Location based holidays contain data
                                                                throw 'CGH0119';
                                                            }
                                                        })
                                                )
                                            } else {
                                                // Custom Group Holidays contains data
                                                throw 'CGH0118';
                                            }
                                        })
                                )
                            } else {
                                return (
                                    organizationDbConnection(ehrTables.holidaySettings)
                                        .update({
                                            'Display_Personal_Choice_Holiday_In_Dashboard': args.displayPersonalChoiceHolidays,
                                            'Updated_On': moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                                            'Updated_By': loginEmployeeId
                                        })
                                        .transacting(trx)
                                        .then((data) => {
                                            if (data) {
                                                return 'success'
                                            }
                                        })
                                )
                            }
                        })
                        .then(() => {
                            //Destroy DB connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            //Return the response
                            return { errorCode: '', message: 'Holiday settings updated successfully.' };
                        })
                        .catch((catchError) => {
                            console.log('Error in updateHolidaySettings .catch() block', catchError);
                            let errResult = commonLib.func.getError(catchError, 'CGH0120');
                            //Destroy DB connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            //Return error response
                            throw new ApolloError(errResult.message, errResult.code);
                        })
                )
            } else {
                console.log('No rights to update the holiday settings');
                throw '_DB0102';
            }
        } else {
            throw 'IVE0000';
        }
    } catch (mainCatchError) {
        console.log('Error in updateHolidaySettings function main block', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        if (mainCatchError === 'IVE0000') {
            let errResult = commonLib.func.getError('', 'IVE0000');
            console.log('Validation error in updateHolidaySettings function - ', validationError);
            // return response
            throw new UserInputError(errResult.message, { validationError: validationError });
        } else {
            errResult = commonLib.func.getError(mainCatchError, 'CGH0006');
            // return response
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
}