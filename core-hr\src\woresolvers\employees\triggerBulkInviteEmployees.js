'use strict';
//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
const { formIds } = require('../../../common/appconstants');

// Function to initiate triggerBulkInviteEmployees step function
module.exports.triggerBulkInviteEmployees = async (parent, args, context) => {
    let organizationDbConnection;
    try {
        console.log('Inside triggerBulkInviteEmployees function', args);
        let loginEmployeeId = context.Employee_Id;
        let orgCode = context.Org_Code
        let partnerId = context.partnerid
        // We will be triggering the step function to send invites in background process.
        organizationDbConnection = knex(context.connection.OrganizationDb);

        const [adminRights, serviceProviderAdminRights, employeeAdminRights] = await Promise.all([
            commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, null, null, 'ui', null, formIds.admin),
            commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, null, null, 'ui', null, formIds.serviceProviderAdmin),
            commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, null, null, 'ui', null, formIds.employeeAdmin),
        ]);

        if ((adminRights && adminRights.Role_Update === 1) || (serviceProviderAdminRights && serviceProviderAdminRights.Role_Update === 1) || (employeeAdminRights && employeeAdminRights.Role_Update === 1)) {
            let inputParams = { 'orgCode': orgCode, 'partnerId': partnerId, employeeData: args.employeeData }
            let triggerbulkInviteEmployeesResponse = await commonLib.stepFunctions.triggerStepFunction(process.env.bulkInviteEmployeesStepFunction, 'triggerBulkInviteEmployees', '', inputParams);
            console.log('Response after triggering triggerBulkInviteEmployees step function', triggerbulkInviteEmployeesResponse);
            if (triggerbulkInviteEmployeesResponse) {
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                return { errorCode: '', message: 'Sign-in invitation for the selected employees has been initiated successfully.' };
            } else {
                throw 'EBI00101'
            }
        }
        else {
            console.log('No rights to invite employees');
            throw '_DB0111';
        }
    }
    catch (mainCatchError) {
        console.log('Error in triggerBulkInviteEmployees function main block', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        let errResult = commonLib.func.getError(mainCatchError, 'EBI00001');
        // return response
        throw new ApolloError(errResult.message, errResult.code);
    }
};