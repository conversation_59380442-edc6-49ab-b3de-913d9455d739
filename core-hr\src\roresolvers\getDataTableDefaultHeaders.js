// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../common/tablealias');
// require apollo server to return error message
const { ApolloError, UserInputError } = require('apollo-server-lambda');

/**
 * GraphQL resolver to retrieve data table default headers combined with employee visibility settings
 * Combines data from custom_additional_table_headers (key-label JSON array) and
 * custom_employee_table_headers (key string array) to determine visibility
 * @param {Object} parent - Parent resolver
 * @param {Object} args - Arguments containing formId and optional screenType
 * @param {Object} context - GraphQL context containing connection and employee info
 * @param {Object} info - GraphQL info object
 * @returns {Object} Response object with errorCode, message, and combined header data with visibility
 */
module.exports.getDataTableDefaultHeaders= async (parent, args, context, info) => {
    let organizationDbConnection, validationError = {}; 
    try {
        console.log("Inside getDataTableDefaultHeaders function");
        
        // Establish database connection
        organizationDbConnection = knex(context.connection.OrganizationDb);

        // Input validation for formId
        if (!args.formId || args.formId <= 0) {
            validationError['IVE0000'] = 'Please enter a valid Form ID.';
            throw 'IVE0000'; // Form ID is required
        }

        // Query the custom_additional_table_headers table (contains key-label pairs as JSON array)
        const customAdditionalHeaderQuery = organizationDbConnection(ehrTables.dataTableDefaultHeaders)
            .select('Custom_Additional_Header')
            .where('Form_Id', args.formId)
            .modify(queryBuilder => {
                // Add screenType condition if provided
                if (args.screenType) {
                    queryBuilder.where('Screen_Type', args.screenType);
                } 
            })
            .first(); // Get single record

        // Query the custom_employee_table_headers table (contains keys as string array)
        const customEmployeeHeaderQuery = organizationDbConnection(ehrTables.customEmployeeTableHeaders)
            .select('Employee_Custom_Header').where('Form_Id', args.formId)
            .where(function () {
                this.where('Employee_Id', context.Employee_Id)
                .orWhereNull('Employee_Id')
                .orWhere('Employee_Id', 0);
            })
            .modify(queryBuilder => {
                // Add screenType condition if provided
                if (args.screenType) {
                    queryBuilder.where('Screen_Type', args.screenType);
                } 
            })
            .orderByRaw(`CASE WHEN Employee_Id = ? THEN 1 WHEN Employee_Id = 0 THEN 2 WHEN Employee_Id IS NULL THEN 3 END`, [context.Employee_Id])
            .first(); // Get single record

         const [customAdditionalHeaderResult, customEmployeeHeaderResult] = await Promise.all([customAdditionalHeaderQuery, customEmployeeHeaderQuery]);

        // Check if custom additional headers exist
        if (!customAdditionalHeaderResult || !customAdditionalHeaderResult?.Custom_Additional_Header) {
            return {
                errorCode: "",
                message: "No custom additional table headers found for the specified form ID",
                dataTableDefaultHeaders: []
            };
        }

        // Parse the additional headers (array of JSON objects with key and label)
        let additionalHeaders = [];
        try {
            additionalHeaders = JSON.parse(customAdditionalHeaderResult.Custom_Additional_Header);
        } catch (parseError) {
            console.log('Error parsing Custom Additional Header JSON:', parseError);
            additionalHeaders = [];
        }

        // Parse the employee headers (array of strings - keys only)
        let employeeHeaderKeys = [];
        if (customEmployeeHeaderResult && customEmployeeHeaderResult.Employee_Custom_Header) {
            try {
                employeeHeaderKeys = JSON.parse(customEmployeeHeaderResult.Employee_Custom_Header);
            } catch (parseError) {
                console.log('Error parsing Employee Custom Header JSON:', parseError);
                employeeHeaderKeys = [];
            }
        }

        // Combine logic: set visibility based on whether key exists in employee headers
        const combinedHeaders = additionalHeaders.map(header => ({
            key: header.key,
            label: header.label,
            visible: employeeHeaderKeys.includes(header.key) ? 'Yes' : 'No'
        }));

        // Return success response with combined headers
        return {
            errorCode: "",
            message: "Table header details retrieved successfully",
            dataTableDefaultHeaders: combinedHeaders
        };

    } catch (error) {
        console.log('Error in getDataTableDefaultHeaders function:', error);
        
        // Handle validation errors
        if (error === 'IVE0000') {
            const errResult = commonLib.func.getError('', 'IVE0000');
            throw new UserInputError(errResult.message, { validationError: validationError });
        }
        // Handle all other errors
        const errResult = commonLib.func.getError(error, 'CHR00117');
        throw new ApolloError(errResult.message, errResult.code);
        
    } finally {
        // Close database connection
        if (organizationDbConnection) {
            organizationDbConnection.destroy();
        }
        // Reset variables to prevent memory leaks
        validationError = {};
    }
};
