//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../../../common/tablealias');
//Require constants
const { formName, systemLogs } = require('../../../common/appconstants');

//Update the user account
module.exports.updateUserAccounts = async (parent, args, context, info) => {
    console.log('Inside updateUserAccounts() function.');
    let organizationDbConnection;
    let errResult;
    let systemLogParam;
    try {
        let loginEmployeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        //Checking the rights if we can update the user account
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, formName.userAccounts, '', 'UI');
        //Check update rights exist or not                 
        if (Object.keys(checkRights).length > 0 && checkRights.Role_Update === 1) {
            return (
                organizationDbConnection(ehrTables.empPersonalInfo)
                    .update({
                        'Allow_User_Signin': args.allowUserSignIn,
                        'Enable_Sign_In_With_Mobile_No': args.enableSignInWithMobileNo
                    })
                    .where('Employee_Id', args.employeeId)
                    .then(async (data) => {
                        if (data) {
                            if(args.revokeRefreshToken){
                                await commonLib.firebase.revokeRefreshTokenAndUpdateEmpUser(organizationDbConnection, args.employeeId, context.partnerid, process.env.region, process.env.dbSecretName)
                            }
                            //Destroy DB connection
                            systemLogParam = {
                                action: systemLogs.roleUpdate,
                                userIp: context.User_Ip,
                                employeeId: loginEmployeeId,
                                formName: formName.userAccounts,
                                trackingColumn: '',
                                organizationDbConnection: organizationDbConnection,
                                uniqueId: args.employeeId
                            }
                            await commonLib.func.createSystemLogActivities(systemLogParam)
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            return { errorCode: '', message: 'User accounts updated successfully.' };
                        } else {
                            throw 'EBI00103'
                        }
                    })
                    .catch((err) => {
                        console.log('Error in updateUserAccounts() function .catch() block', err);
                        errResult = commonLib.func.getError(err, 'EBI00103');
                        //Destroy DB connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        throw new ApolloError(errResult.message, errResult.code);//return response
                    })
            )
        }
        else {
            console.log('User does not have access to update User Accounts Email/Phone');
            throw ('_DB0102');
        }
    } catch (err) {
        console.log('Error in the updateUserAccounts() function main catch block. ', err);
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        errResult = commonLib.func.getError(err, 'EBI00004');
        throw new ApolloError(errResult.message, errResult.code);//return response
    }
};