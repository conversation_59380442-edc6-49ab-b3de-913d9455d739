// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formIds } = require('../../../common/appconstants');

//fuction to list holidays based on location
let organizationDbConnection;
module.exports.retrieveTimesheetSettings = async (parent, args, context, info) => {
    console.log('Inside retrieveTimesheetSettings function');
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let loginEmployeeId = context.Employee_Id;
        let accessFormId= args.formId?args.formId : formIds.timeSheetMyTeam;
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, null, '', 'UI', false, accessFormId);
            if (Object.keys(checkRights).length > 0 && checkRights.Role_View === 1) {
                return(
                    await organizationDbConnection(ehrTables.projectSettings)
                    .select('Timesheet_Setting_Id','Timesheet_Submission_Before_Weekend_Date','Present_Time_Slot','Enforce_Note')
                    .then(async (data) => {
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        return { errorCode: "", message: "timesheet settings has been retrieved successfully.", timesheetSettingData: data};
                    })
                    .catch((catchError) => {
                        let errResult = commonLib.func.getError(catchError, 'CHR0098');
                        //Destroy DB connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        throw new ApolloError(errResult.message, errResult.code);
                    })
                )
            }
            else {
                console.log('Employee do not have view access rights');
                throw '_DB0100';
            }
    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in retrieveTimesheetSettings function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'CHR0099');
        throw new ApolloError(errResult.message, errResult.code)
       
    }
}
