const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const knex = require('knex');
const { ApolloError } = require('apollo-server-lambda');
const { ehrTables } = require('../../../common/tablealias');

module.exports.getEmployeeTravelSetting = async (parent, args, context) => {
  let organizationDbConnection;
  try {
    console.log('Inside getEmployeeTravelSetting function', args);
    const loginEmployeeId = context.Employee_Id;
    const { employeeTravelSettingId, formId } = args;
    // Check access rights
    organizationDbConnection = knex(context.connection.OrganizationDb);
    const checkRights = await commonLib.func.checkEmployeeAccessRights(
      organizationDbConnection,
      loginEmployeeId,
      null,
      '',
      'UI',
      false,
      formId
    );

    if (Object.keys(checkRights).length <= 0 || checkRights.Role_View !== 1) {
      console.log('No view access to this form');
      throw '_DB0111';
    }

    // Retrieve employee travel settings
    const travelSetting = await organizationDbConnection(ehrTables.employeeTravelSetting+ ' as T')
    .select(
        'T.Employee_Travel_Setting_Id',
        'T.Email_Recipients',
        'T.Additional_Recipients',
        organizationDbConnection.raw(`
        CONCAT_WS(" ", EP_U.Emp_First_Name, EP_U.Emp_Middle_Name, EP_U.Emp_Last_Name) as Updated_By
      `),
      organizationDbConnection.raw(`
        CONCAT_WS(" ", EP_A.Emp_First_Name, EP_A.Emp_Middle_Name, EP_A.Emp_Last_Name) as Added_By
      `))
      .leftJoin(
        `${ehrTables.empPersonalInfo} as EP_U`,
        'EP_U.Employee_Id',
        'T.Updated_By'
      )
      .leftJoin(
        `${ehrTables.empPersonalInfo} as EP_A`,
        'EP_A.Employee_Id',
        'T.Added_By'
      )
      .first();

    if (!travelSetting) {
      throw 'ELR0139'; // No data found
    }

    return {
      errorCode: '',
      message: 'Employee travel settings retrieved successfully.',
      travelSetting,
    };
  } catch (error) {
    console.error('Error in getEmployeeTravelSetting function:', error);
    const errResult = commonLib.func.getError(error, 'ELR0139');
    throw new ApolloError(errResult.message, errResult.code);
  } finally {
    if (organizationDbConnection) await organizationDbConnection.destroy();
  }
};