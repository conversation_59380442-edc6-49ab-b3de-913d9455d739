//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require constants
const { formName } = require('../../common/appconstants');
const { ApolloError } = require('apollo-server-lambda');
const moment = require('moment');
module.exports.getEmployeeWeekOffAndHolidayDetails = async (_, args, context) => {
    let organizationDbConnection;

    try {
        const logInEmpId = context.Employee_Id;
        const { month, year, employeeId } = args;

        // Get the organization database connection
        organizationDbConnection = knex(context.connection.OrganizationDb);
        const startDate = moment([year, month - 1]).format('YYYY-MM-DD');
        const endDate = moment(startDate).endOf('month').format('YYYY-MM-DD');

        // Check access rights
        const checkRights = await commonLib.func.checkEmployeeAccessRights(
            organizationDbConnection,
            logInEmpId,
            formName.preApprovalRequests,
            '',
            'UI',
            false,
            args.formId
        );

        if (Object.keys(checkRights).length === 0) {
            throw '_DB0100';
        }

        if (checkRights.Role_View !== 1) {
            throw '_DB0100';
        }
        let employeeWorkScheduleHolidayDetails = await commonLib.shiftAndTimeManagement.fetchEmployeeWeekOffAndHolidayDetails(
            organizationDbConnection,
            employeeId,
            startDate,
            endDate
        );
        
        // Optional chaining and fallback to an empty object to avoid runtime errors
        const employeeData = employeeWorkScheduleHolidayDetails?.[employeeId] || {};
        
        // Destructure with default values
        const {
            weekOffAndHolidayDetails = [],
            totalBusinessWorkingDays = 0,
            workScheduleToSend = []
        } = employeeData;
        
        // Destroy the connection
        if (organizationDbConnection) {
            organizationDbConnection.destroy();
        }

        return {
            errorCode: "",
            message: "Employee weekoff and holiday details retrieved successfully.",
            totalBusinessWorkingDays,
            weekOffAndHolidayDetails,
            workScheduleToSend
        };
    } catch (e) {
        if (organizationDbConnection) {
            organizationDbConnection.destroy();
        }

        const errResult = commonLib.func.getError(e, 'CHR0061');
        throw new ApolloError(errResult.message, errResult.code);
    }
}