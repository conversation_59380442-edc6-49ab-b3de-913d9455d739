// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../common/tablealias');
//Require apollo server to return error message
const { ApolloError,UserInputError } = require('apollo-server-lambda');
let moment = require('moment-timezone');
// require common constant files
const { formName } = require('../../common/appconstants');
const { validateCommonRuleInput } = require('../../common/inputValidations');

module.exports.updateRosterManagmentSetting = async (parent, args, context, info) => {
    let organizationDbConnection;
    let validationError = {};
    let fieldValidations={}
    try {
        console.log("Inside updateRosterManagmentSetting function.")
        let loginEmployeeId = context.Employee_Id;
        let accessFormName = args.formName ? args.formName : formName.roster;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        if (args.swapApprovalRestriction === 'Yes' && args.maxSwapRequestsPerMonth) {
            fieldValidations.maxSwapRequestsPerMonth = 'IVE0513'
        }
          
        if(args.swapApprovalRestriction.toLowerCase() === 'yes' && !args.maxSwapRequestsPerMonth){
            validationError['IVE0514'] = commonLib.func.getError(
              'IVE0514',
              ''
            ).message
        }

        fieldValidations.allowPastShiftSwaps='IVE0526';

        if (fieldValidations && Object.keys(fieldValidations).length > 0) {
            validationError = await validateCommonRuleInput(args, fieldValidations)
        }

        if(args.allowPastShiftSwaps && args.allowPastShiftSwaps.toLowerCase() === 'yes'){
            (args.maxShiftSwapDays < 0 || args.maxShiftSwapDays == null) ? validationError['IVE0524'] = commonLib.func.getError('IVE0524','').message : null;
            (args.maxShiftSwapDays >= 0 && args.maxShiftSwapDays > 40) ? validationError['IVE0525'] = commonLib.func.getError('IVE0525','').message : null;
        }
        
        if (Object.keys(validationError).length > 0) {
            throw new Error('IVE0000');
        }

        let rosterManagmentUpdatedData = {
            "Dynamic_Week_Off": args.dynamicWeekOff,
            "Allow_Past_Shift_Swaps": args.allowPastShiftSwaps,
            "Max_Shift_Swap_Days": args.allowPastShiftSwaps.toLowerCase() === 'yes' ? args.maxShiftSwapDays : null,
            'Updated_On': moment().utc().format('YYYY-MM-DD HH:mm:ss'),
            'Updated_By': loginEmployeeId
        }
        if(args.swapApprovalRestriction){
            rosterManagmentUpdatedData['Enable_Shift_Swap_Restriction'] = args.swapApprovalRestriction;
        }
        if(args.maxSwapRequestsPerMonth || args.swapApprovalRestriction.toLowerCase() === 'no'){
            rosterManagmentUpdatedData['Max_Swap_Requests_Per_Month'] = args.maxSwapRequestsPerMonth;
        }
        // check their rights
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, accessFormName, '', 'UI');
        if (Object.keys(checkRights).length > 0 && checkRights.Role_Update === 1) {

                      return(
                        organizationDbConnection(ehrTables.rosterManagementSettings)
                        .update(rosterManagmentUpdatedData).then((data) => {
                            if(data){
                                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                return { errorCode: "", message: "Roster management settings updated successfully."}; 
                            }
                            else{
                                throw 'SET0106';
                             }
                    //destroy the connection
                    
                }).catch((catchError) => {
                    console.log('Error while updaing the roster management settings', catchError);
                    //Destroy DB connection
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    
                    let errResult = commonLib.func.getError(catchError, 'SET0105');
                    throw new ApolloError(errResult.message, errResult.code);
                })
            )
          
        }
        else {
            throw '_DB0102';
        }

    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in updateRosterManagmentSetting function main catch block.', e);
        if (e.message === 'IVE0000') {
            const errResult = commonLib.func.getError('', 'IVE0000');
            throw new UserInputError(errResult.message, { validationError });
        }else{
        let errResult = commonLib.func.getError(e, 'SET0004');
        throw new ApolloError(errResult.message, errResult.code);
    }
}
}