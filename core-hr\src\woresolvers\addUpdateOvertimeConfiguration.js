// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../common/tablealias');
//Require apollo server to return error message
const { UserInputError, ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formName, systemLogs, formIds } = require('../../common/appconstants');
const { insertDataInTable } = require('../../common/commonfunctions');
//Require validation function
const { validateCommonRuleInput } = require('../../common/inputValidations');
const moment = require('moment');

module.exports.addUpdateOvertimeConfiguration = async (parent, args, context, info) => {
    let organizationDbConnection;
    let errResult;
    let validationError = {};
    let employeeId = context.Employee_Id;
    let overtimeCoverage;

    try {
        let loginEmployeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);

        let formId = args.formId || formIds.overTime;

        const overtimeCoverageArray = await commonLib.func.getCoverage(organizationDbConnection, formId, ehrTables.formLevelCoverage);
        overtimeCoverage = overtimeCoverageArray[0].Coverage;
        
        let { Configuration_Id, CustomGroup_Id, Wage_Factor, Salary_Type, Special_Work_Days, Status, Overtime_Type, overtimeFixedAmount } = args;
        
        //check their rights
        let checkRights = await commonLib.func.checkEmployeeAccessRights(
            organizationDbConnection,
            employeeId,
            '',
            '',
            'UI',
            false,
            formId
        );
        
        if (Object.keys(checkRights).length > 0 && ((Configuration_Id === 0 && checkRights.Role_Add === 1) || (Configuration_Id > 0 && checkRights.Role_Update === 1))) {
            
            const fieldValidations = {};
            if (Wage_Factor) {
                fieldValidations.Wage_Factor = "IVE0376";
                validationError = await validateCommonRuleInput(args, fieldValidations);
            }

            if (Overtime_Type.toLowerCase() === 'fixed amount' && overtimeFixedAmount) {
                fieldValidations.overtimeFixedAmount = "IVE0460";
                validationError = await validateCommonRuleInput(args, fieldValidations);
            }
            else if (Overtime_Type.toLowerCase() === 'fixed amount' && !overtimeFixedAmount) {
                throw 'SOT0107';
            }

            if (Object.keys(validationError).length > 0) {
                throw ('IVE0000');
            } else {
                const customGroupSettings = await organizationDbConnection
                    .transaction(async function (trx) {
                        const query = organizationDbConnection(ehrTables.overtimeConfiguration + " as OC")
                            .transacting(trx)
                            .select('OC.Configuration_Id', 'OC.Salary_Type', 'OC.Special_Work_Days', 'FLC.Coverage')
                            .leftJoin(ehrTables.formLevelCoverage + " as FLC", "FLC.Form_Id", formId)
                            .where("OC.Salary_Type", Salary_Type)
                            .where("OC.Special_Work_Days", Special_Work_Days)
                            .where("OC.Status", "Active");
                            
                        // Conditionally add the 'Configuration_Id' filter
                        if (Configuration_Id !== 0) {
                            query.whereNot('OC.Configuration_Id', Configuration_Id);
                        }
                        
                        if ((CustomGroup_Id && overtimeCoverage.toLowerCase() === "custom group") || (CustomGroup_Id && Configuration_Id)) {
                            query.leftJoin(ehrTables.customGroupAssociated + " as CGA", "OC.Configuration_Id", "CGA.Parent_Id");
                            query.where("CGA.Custom_Group_Id", CustomGroup_Id);
                            query.where("CGA.Form_Id", formId);
                            query.select('CGA.Custom_Group_Id');
                        }
                        
                        const result = await query;
                        
                        if ((overtimeCoverage === "Organization" && CustomGroup_Id) || (overtimeCoverage.toLowerCase() === "custom group" && !CustomGroup_Id)) {
                            throw 'SOT0106';
                        }

                        if (result.length > 0) {
                            console.log("duplicate record exist");
                            throw 'SOT0104';
                        }
                        
                        let currentDateTime = moment().utc().format('YYYY-MM-DD HH:mm:ss');
                        let overtimeData = {
                            "Salary_Type": Salary_Type,
                            "Special_Work_Days": Special_Work_Days,
                            "Status": Status,
                            "Wage_Factor": Wage_Factor,
                            "Overtime_Type": Overtime_Type,
                            "Amount": overtimeFixedAmount
                        };
                        
                        if (Configuration_Id) {
                            overtimeData.Updated_On = currentDateTime;
                            overtimeData.Updated_By = loginEmployeeId;
                            
                            const updateResult = await organizationDbConnection(ehrTables.overtimeConfiguration)
                                .transacting(trx)
                                .where("Configuration_Id", Configuration_Id)
                                .update(overtimeData);
                                
                            if (updateResult) {
                                if (CustomGroup_Id) {
                                    let customGroupData = [{ "Parent_Id": Configuration_Id, "Form_Id": formId, "Custom_Group_Id": CustomGroup_Id }];
                                    
                                    await organizationDbConnection(ehrTables.customGroupAssociated)
                                        .transacting(trx)
                                        .del()
                                        .where('Form_Id', formId)
                                        .where('Parent_Id', Configuration_Id);
                                        
                                    await insertDataInTable(organizationDbConnection, trx, customGroupData, ehrTables.customGroupAssociated);
                                }
                                return true;
                            } else {
                                throw 'SOT0102';
                            }
                        } else {
                            overtimeData.Added_On = currentDateTime;
                            overtimeData.Added_By = loginEmployeeId;
                            
                            const insertData = await organizationDbConnection(ehrTables.overtimeConfiguration)
                                .transacting(trx)
                                .insert(overtimeData);
                                
                            if (insertData && insertData.length) {
                                if (CustomGroup_Id) {
                                    let customGroupData = [{ "Parent_Id": insertData[0], "Form_Id": formId, "Custom_Group_Id": CustomGroup_Id }];
                                    await insertDataInTable(organizationDbConnection, trx, customGroupData, ehrTables.customGroupAssociated);
                                }
                                overtimeData.Configuration_Id = insertData[0];
                                Configuration_Id=insertData[0];
                                return true;
                            } else {
                                throw 'SOT0102';
                            }
                        }
                    });
                    
                if (customGroupSettings) {
                    let systemLogParam = {
                        userIp: context.User_Ip,
                        employeeId: loginEmployeeId,
                        uniqueId: Configuration_Id,
                        changedData: args,
                        organizationDbConnection: organizationDbConnection,
                        message: `Overtime configuration details ${Configuration_Id ? 'updated' : 'added'} successfully.`
                    };
                    await commonLib.func.createSystemLogActivities(systemLogParam);
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    
                    if (Configuration_Id) {
                        return { errorCode: "", message: "Overtime configuration details updated successfully." };
                    }
                    return { errorCode: "", message: "Overtime configuration details added successfully." };
                } else {
                    throw 'SOT0103';
                }
            }
        } else {
            if (Configuration_Id) {
                console.log("The employee does not have edit access.");
                throw '_DB0102';
            } else {
                console.log("The employee does not have add access.");
                throw '_DB0101';
            }
        }
    } catch (e) {
        console.log('Error in addUpdateOvertimeConfiguration function main catch block.', e);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        
        if (e === 'IVE0000') {
            console.log('Validation error in the addUpdateOvertimeConfiguration function', validationError);
            errResult = commonLib.func.getError('', 'IVE0000');
            throw new UserInputError(errResult.message, { validationError: validationError });
        } else {
            errResult = commonLib.func.getError(e, 'SOT0002');
            console.log(errResult,"errResult");
            if (typeof e === 'string' && e === 'SOT0106' && overtimeCoverage) {
                let errorMessage = `Overtime configuration is set to ${overtimeCoverage === 'Organization' ? 'Custom Group' : 'Organization'}, conflicting with the platform's ${overtimeCoverage} level setting. Please adjust the platform to ${overtimeCoverage} or establish a new configuration.`;
                errResult.message = errorMessage;
            }
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
}
