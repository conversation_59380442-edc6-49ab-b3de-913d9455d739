// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formName } = require('../../common/appconstants');


let organizationDbConnection;
module.exports.retrieveRosterManagmentSetting = async (parent, args, context, info) => {
    try {
        console.log("Inside retrieveRosterManagmentSetting function.")
        let employeeId = context.Employee_Id;
        let accessFormName = args.formName ? args.formName : formName.roster;
        let rosterManagmentSettingRetrieved = {};
        organizationDbConnection = knex(context.connection.OrganizationDb);
        // check their rights
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, employeeId, accessFormName, '', 'UI');
        if (Object.keys(checkRights).length > 0 && checkRights.Role_View === 1) {
            return (
                organizationDbConnection(ehrTables.rosterManagementSettings + " as RMS")
                    .select('*', organizationDbConnection.raw("CONCAT_WS(' ',EPI.Emp_First_Name, EPI.Emp_Middle_Name, EPI.Emp_Last_Name) as Updated_By")).from(ehrTables.rosterManagementSettings + " as RMS")
                    .leftJoin(ehrTables.empPersonalInfo + " as EPI", "EPI.Employee_Id", "RMS.Updated_By")
                    .then((data) => {
                        if (data && data.length) {
                            rosterManagmentSettingRetrieved.Dynamic_Week_Off = data[0].Dynamic_Week_Off;
                            rosterManagmentSettingRetrieved.Overlap_Shift_Schedule = data[0].Overlap_Shift_Schedule;
                            rosterManagmentSettingRetrieved.Updated_By = data[0].Updated_By;
                            rosterManagmentSettingRetrieved.Updated_On = data[0].Updated_On;
                            rosterManagmentSettingRetrieved.Enable_Shift_Swap_Restriction=data[0].Enable_Shift_Swap_Restriction;
                            rosterManagmentSettingRetrieved.Max_Swap_Requests_Per_Month=data[0].Max_Swap_Requests_Per_Month;
                            rosterManagmentSettingRetrieved.Allow_Past_Shift_Swaps=data[0].Allow_Past_Shift_Swaps;
                            rosterManagmentSettingRetrieved.Max_Shift_Swap_Days=data[0].Max_Shift_Swap_Days;
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            return { errorCode: "", message: "Roster managment settings retrieved successfully.", rosterManagmentSetting: rosterManagmentSettingRetrieved };
                        } else {
                            console.log('Roster managment settings data not found')
                            throw 'SET0104'
                        }
                    })
                    .catch((err) => {
                        console.log('Error in retrieveRosterManagmentSetting .catch() block', err);
                        let errResult = commonLib.func.getError(catchError, 'SET0104');
                        throw new ApolloError(errResult.message, errResult.code);
                    })
            )
        }
        else {
            throw '_DB0100';
        }

    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in retrieveRosterManagmentSetting function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'SET0003');
        throw new ApolloError(errResult.message, errResult.code);
    }
}
