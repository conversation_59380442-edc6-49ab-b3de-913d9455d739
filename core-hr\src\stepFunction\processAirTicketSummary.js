//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require table alias
const { ehrTables } = require('../../common/tablealias');
const moment = require('moment-timezone');

module.exports.processAirTicketSummary = async (args) => {
    // Log the arguments received by the function for debugging
    console.log('Inside processAirTicketSummary args value => ', args);

    let organizationDbConnection;
    // Destructure necessary properties from args with default values
    const { employeeIds, orgCode, partnerId, employeeIsInActiveAction = false, dailyAccruedAction = 'settlement', inActiveRollBack = false, inActiveDate } = args;

    try {
        // Get the database connection
        let connection = await commonLib.stepFunctions.getConnection(
            process.env.stageName, process.env.dbPrefix, process.env.dbSecretName, 
            process.env.region, orgCode, 0, {}
        );

        // Check if the connection is valid
        if (!connection || Object.keys(connection).length === 0) {
            console.error('Organization database connection not found.', orgCode);
            return false;
        }

        // Initialize the database connection with knex
        organizationDbConnection = knex(connection.OrganizationDb);

        // Retrieve employee air ticket policies from the database
        const employeeAirTicketPolicyList = await organizationDbConnection(ehrTables.empAirTicketPolicy + ' as EATP')
            .select('EATP.*', 'ATS.*', 'EJ.Emp_InActive_Date')
            .where('ATS.Status', 'Active')
            .innerJoin(ehrTables.airTicketSettings + " as ATS", "ATS.Air_Ticket_Setting_Id", "EATP.Air_Ticket_Setting_Id")
            .innerJoin(ehrTables.empJob + " as EJ", "EJ.Employee_Id", "EATP.Employee_Id")
            .whereIn('EATP.Employee_Id', employeeIds);

        // If no policies are found, return false
        if (!employeeAirTicketPolicyList || employeeAirTicketPolicyList.length === 0) {
            return false;
        }

        // Retrieve dependent dependentCategory from the database
        const dependentCategory = await organizationDbConnection('dependent_category').select('Category', 'Min_Age', 'Max_Age');

        // Function to get count of dependents in each age category
        const getAgeCategoryCount = (dependentsDOBs) => {
            return dependentsDOBs.reduce((acc, dob) => {
                if (dob) {
                    const ageInMonths = moment().diff(moment(dob), 'months', true);
                    const category = dependentCategory.find(cat => ageInMonths >= cat.Min_Age && (cat.Max_Age === null || ageInMonths <= cat.Max_Age));
                    if (category) {
                        acc[category.Category] = (acc[category.Category] || 0) + 1;
                    }
                }
                return acc;
            }, {});
        };

        let employeeIdData = [];

        // Handle InActiveRollBack logic before processing individual employees
        if (inActiveRollBack) {
            await handleInActiveRollBack(organizationDbConnection, employeeAirTicketPolicyList, inActiveDate, getAgeCategoryCount);
            return;
        }

        // Loop through each employee ticket policy
        for (let employeeTicket of employeeAirTicketPolicyList) {

            try {
                // Determine the last available date for the ticket
                const lastAvailableDate = employeeTicket.Last_Availed_Date ? moment(employeeTicket.Last_Availed_Date) : moment(employeeTicket.Effective_Date);

                // Calculate the eligibility of ticket claim months
                let eligibilityOfTicketClaimMonths = employeeIsInActiveAction ? moment(employeeTicket.Emp_InActive_Date).diff(lastAvailableDate, 'months') : employeeTicket.Eligibility_Of_Ticket_Claim_Months;
                const currentAvailableDate = employeeIsInActiveAction ? moment(employeeTicket.Emp_InActive_Date) : lastAvailableDate.clone().add(eligibilityOfTicketClaimMonths, 'months');

                const daysSinceLast = dailyAccruedAction.toLowerCase() === "dailyaccrued" ? moment().utc().diff(lastAvailableDate, "days") :
                currentAvailableDate.diff(lastAvailableDate, "days");
        
                // Calculate dependent counts and ticket amounts using common function
                const ticketCalculationResult = await calculateEmployeeTicketDetails(
                    organizationDbConnection,
                    employeeTicket,
                    getAgeCategoryCount,
                    daysSinceLast
                );

                const {
                    infantCount,
                    childCount,
                    adultCount,
                    numberOfDependentCount,
                    dependentRelationShip,
                    infantAccruedTicket,
                    childAccruedTicket,
                    adultAccruedTicket,
                    totalAccruedTickets,
                    infantAccruedAmount,
                    childAccruedAmount,
                    adultAccruedAmount,
                    totalAccruedAmount
                } = ticketCalculationResult;

                if (dailyAccruedAction.toLowerCase() === "dailyaccrued") {
                    // If action is daily accrued, prepare data for update
            
                    const dailyAirTicketAccrued = {
                        Employee_Id: employeeTicket.Employee_Id,
                        Accrual_Month_Year: moment().startOf('month').format('YYYY-MM-DD'),
                        Last_Availed_Date: lastAvailableDate.format('YYYY-MM-DD'),
                        Entitled_Tickets_Per_Year: eligibilityOfTicketClaimMonths,
                        Accrued_Infant_Tickets: infantAccruedTicket,
                        Accrued_Child_Tickets: childAccruedTicket,
                        Accrued_Adult_Tickets: adultAccruedTicket,
                        Accrued_Total_Tickets: totalAccruedTickets,
                        Accrued_Infant_Amount: infantAccruedAmount,
                        Accrued_Child_Amount: childAccruedAmount,
                        Accrued_Adult_Amount: adultAccruedAmount,
                        Accrued_Total_Amount: totalAccruedAmount,
                        Accrued_Infant_Count: infantCount,
                        Accrued_Child_Count: childCount,
                        Accrued_Adult_Count: adultCount,
                        Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                    };

                    await organizationDbConnection('emp_air_ticket_accrual')
                    .insert(dailyAirTicketAccrued).onConflict(['Employee_Id', 'Accrual_Month_Year'])
                    .merge();

                } else {
                    
                    const settlementData = {
                        Employee_Id: employeeTicket.Employee_Id,
                        Destination_City: employeeTicket.Destination_City,
                        Destination_Country: employeeTicket.Destination_Country,
                        Air_Ticketing_Category: employeeTicket.Air_Ticketing_Category,
                        Eligibility_Of_Ticket_Claim_Months: employeeTicket.Eligibility_Of_Ticket_Claim_Months,
                        Air_Ticket_To_Dependent: employeeTicket.Air_Ticket_To_Dependent,
                        Dependent_Relationship: JSON.stringify(dependentRelationShip),
                        Effective_Date_Enable: employeeTicket.Effective_Date_Enable,
                        Effective_Date: employeeTicket.Effective_Date,
                        Infant_Policy_Amount: employeeTicket.Infant_Amount,
                        Child_Policy_Amount: employeeTicket.Child_Amount,
                        Adult_Policy_Amount: employeeTicket.Adult_Amount,
                        Settlement_From_Date: lastAvailableDate.format('YYYY-MM-DD'),
                        Availed_Date: currentAvailableDate.format('YYYY-MM-DD'),
                        No_Of_Dependents: numberOfDependentCount,
                        Infant_Dependents_Ticket: infantCount,
                        Child_Dependents_Ticket: childCount,
                        Adult_Dependents_Ticket: adultCount - 1,
                        Infant_Accrued_Tickets: infantAccruedTicket,
                        Child_Accrued_Tickets: childAccruedTicket,
                        Adult_Accrued_Tickets: adultAccruedTicket,
                        Total_Accrued_Tickets: totalAccruedTickets,
                        Infant_Accrued_Amount: infantAccruedAmount,
                        Child_Accrued_Amount: childAccruedAmount,
                        Adult_Accrued_Amount: adultAccruedAmount,
                        Settlement_Amount: totalAccruedAmount,
                        Settlement_Status: 'Yet to be Settled',
                        Payroll_Month: moment().utc().format('YYYY-MM'),
                        Added_On:  moment().utc().format('YYYY-MM-DD'),
                        Added_By: 0,
                    };

                    const dailyAccruedAirTicketReset ={
                        Accrued_Infant_Tickets: 0,
                        Accrued_Child_Tickets: 0,
                        Accrued_Adult_Tickets: 0,
                        Accrued_Total_Tickets: 0,
                        Accrued_Infant_Amount: 0,
                        Accrued_Child_Amount: 0,
                        Accrued_Adult_Amount: 0,
                        Accrued_Total_Amount: 0,
                        Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                    }

                    await organizationDbConnection.transaction(async function (trx) {

                        let accrualMonthYear = moment().startOf('month').format('YYYY-MM-DD');
                        if(employeeIsInActiveAction){
                            accrualMonthYear = moment(currentAvailableDate.format('YYYY-MM-DD')).startOf('month').format('YYYY-MM-DD');
                            await trx('emp_air_ticket_accrual').del()
                            .where('Employee_Id', employeeTicket.Employee_Id)
                            .where('Accrual_Month_Year','>', accrualMonthYear)
                        }

                        await trx('emp_air_ticket_accrual').update(dailyAccruedAirTicketReset)
                        .where('Employee_Id', employeeTicket.Employee_Id)
                        .where('Accrual_Month_Year', accrualMonthYear)

                        await trx(ehrTables.airTicketSettlementSummary).insert(settlementData);

                        await trx(ehrTables.empAirTicketPolicy)
                        .update({ Last_Availed_Date: currentAvailableDate.format('YYYY-MM-DD') })
                        .where('Employee_Id', employeeTicket.Employee_Id);

                    }).then(function () {
                        console.log(`Processed settlement summary`);
                    })
                    .catch(function (err) {
                        console.error('Error in settlement summary catch function', err);
                    });

                    employeeIdData.push(employeeTicket.Employee_Id);
                }
                
            } catch(error){
                console.error("error")
            }
        }

        if (dailyAccruedAction.toLowerCase() === "dailyaccrued") {

        } else {
            // Check if Syntrum integration is enabled and trigger the step function
            const syntrumAirTicketActive = await commonLib.func.integrationAPIIsEnabled(organizationDbConnection, { integrationType: 'Syntrum', direction: 'Push', action: 'Create', entityType: 'Employee-Air-Ticket' });
            if (syntrumAirTicketActive && employeeIdData.length) {
                const params = { orgCode, partnerId, employeeIds: employeeIdData, entityType: 'air_ticket', action: 'create' };
                console.log('Syntrum Air Ticket Push API request params => ', params);
                await commonLib.stepFunctions.triggerStepFunction(process.env.asyncSyntrumAPIStepFunction, 'asyncSyntrumAPIStepFunction', null, params);
            }
        }

    } catch (error) {
        // Log any errors that occur during processing
        console.error("Error occured while processAirTicketSummary main catch block => ", error);
    } finally {
        // Destroy the database connection if it exists
        organizationDbConnection ? organizationDbConnection.destroy() : null;
    }
}


async function calculateEmployeeTicketDetails(organizationDbConnection, employeeTicket, getAgeCategoryCount, daysSinceLast) {
        
    // Initialize counts for different age categories
    let infantCount = 0, childCount = 0, adultCount = 1, numberOfDependentCount = 0, dependentRelationShip = [];

    // Check if dependent ticket is allowed and get the relationships
    const relationships = JSON.parse(employeeTicket.Dependent_Relationship || '[]');

    if (employeeTicket.Air_Ticket_To_Dependent?.toLowerCase() === "yes" &&
        Array.isArray(relationships) && relationships.length > 0) {
        // Retrieve dependent details from the database
        const employeeDependentsList = await organizationDbConnection(ehrTables.empdependent)
            .select('Dependent_DOB', 'Relationship').whereIn('Relationship', relationships)
            .andWhere('Employee_Id', employeeTicket.Employee_Id);

        if (employeeDependentsList.length) {
            // Map dependent details to required information
            const dependentsDOBs = employeeDependentsList.map(item => item.Dependent_DOB);
            dependentRelationShip = employeeDependentsList.map(item => item.Relationship);
            const ageGroups = getAgeCategoryCount(dependentsDOBs);

            // Update counts based on age groups
            infantCount += ageGroups?.Infant || 0;
            childCount += ageGroups?.Child || 0;
            adultCount += ageGroups?.Adult || 0;
            numberOfDependentCount = employeeDependentsList.length;
        }
    }

    // Function to calculate tickets based on days since last availed, person count, and total days
    const calculateTickets = (daysSinceLast, personCount, totalDays) => Number((((daysSinceLast / totalDays) * personCount)).toFixed(2));

    // Calculate total days and days since last availed
    const totalDays = Number((365 * parseInt(employeeTicket.Eligibility_Of_Ticket_Claim_Months || 0)) / 12);
  
    // Calculate the number of tickets for each category
    const infantAccruedTicket = calculateTickets(daysSinceLast, infantCount, totalDays);
    const childAccruedTicket = calculateTickets(daysSinceLast, childCount, totalDays);
    const adultAccruedTicket = calculateTickets(daysSinceLast, adultCount, totalDays);
    const totalAccruedTickets = (infantAccruedTicket || 0.00) + (childAccruedTicket || 0.00) + (adultAccruedTicket || 0.00);

    // Calculate the amount for each ticket category
    const infantAccruedAmount = infantAccruedTicket * (employeeTicket.Infant_Amount || 0);
    const childAccruedAmount = childAccruedTicket * (employeeTicket.Child_Amount || 0);
    const adultAccruedAmount = adultAccruedTicket * (employeeTicket.Adult_Amount || 0);
    const totalAccruedAmount = infantAccruedAmount + childAccruedAmount + adultAccruedAmount;

    return {
        infantCount,
        childCount,
        adultCount,
        numberOfDependentCount,
        dependentRelationShip,
        infantAccruedTicket,
        childAccruedTicket,
        adultAccruedTicket,
        totalAccruedTickets,
        infantAccruedAmount,
        childAccruedAmount,
        adultAccruedAmount,
        totalAccruedAmount
    };
}

/**
 * Handle InActiveRollBack logic for air ticket processing
 * @param {Object} organizationDbConnection - Database connection
 * @param {Array} employeeAirTicketPolicyList - List of employee air ticket policies
 * @param {Object} ehrTables - Table aliases
 * @param {Array} dependentCategory - Dependent category data
 * @param {Function} getAgeCategoryCount - Function to get age category count
 */
async function handleInActiveRollBack(organizationDbConnection, employeeAirTicketPolicyList, inActiveDate, getAgeCategoryCount) {
    console.log('Processing InActiveRollBack logic for air tickets');

    try {
        if(inActiveDate && employeeAirTicketPolicyList && employeeAirTicketPolicyList.length){

            const employeeTicket = employeeAirTicketPolicyList[0];
            const inactiveDate = moment(inActiveDate);
            const currentDate = moment();

            // Requirement 2: Update settlement summary status to 'Cancelled' where availed_date matches inactive date
            await updateSettlementSummaryStatus(organizationDbConnection, employeeTicket, inactiveDate);

            // Requirement 1: Create records for each month from Inactive date up to current date
            await createMonthlyAccrualRecords(organizationDbConnection, employeeTicket, inactiveDate, currentDate, getAgeCategoryCount);
        }
    } catch (error) {
        console.error('Error in handleInActiveRollBack:', error);
        throw error;
    }
}

/**
 * Create monthly accrual records from inactive date to current date
 */
async function createMonthlyAccrualRecords(organizationDbConnection, employeeTicket, inactiveDate, currentDate, getAgeCategoryCount) {
    const inActiveStartMonth = inactiveDate.clone().startOf('month');
    const endMonth = currentDate.clone().startOf('month');

    let currentMonth = inActiveStartMonth.clone();

    const airTicketPolicy = await organizationDbConnection(ehrTables.empAirTicketPolicy)
    .where('Employee_Id', employeeTicket.Employee_Id).first()

    while (currentMonth.isSameOrBefore(endMonth)) {

     
        // For each month, calculate from start of month to end of month
        const monthEndDate = currentMonth.clone().endOf('month');
       

        const previousStartMonth = moment(currentMonth).subtract(1, 'month').startOf('month').format('YYYY-MM-DD');

        const previousAirTicketAccrual = await organizationDbConnection('emp_air_ticket_accrual')
        .where('Employee_Id', employeeTicket.Employee_Id).where('Accrual_Month_Year', previousStartMonth).first();

        if(previousAirTicketAccrual){
            const insertData = {
                Employee_Id: previousAirTicketAccrual.Employee_Id,
                Accrual_Month_Year: currentMonth.format('YYYY-MM-DD'),
                Entitled_Tickets_Per_Year: previousAirTicketAccrual.Entitled_Tickets_Per_Year,
                Opening_Balance_Total_Tickets: previousAirTicketAccrual.Accrued_Total_Tickets,
                Opening_Balance_Infant_Tickets: previousAirTicketAccrual.Accrued_Infant_Tickets,
                Opening_Balance_Child_Tickets: previousAirTicketAccrual.Accrued_Child_Tickets,
                Opening_Balance_Adult_Tickets: previousAirTicketAccrual.Accrued_Adult_Tickets,
                Opening_Balance_Total_Amount: previousAirTicketAccrual.Accrued_Total_Amount,
                Opening_Balance_Infant_Amount: previousAirTicketAccrual.Accrued_Infant_Amount,
                Opening_Balance_Child_Amount: previousAirTicketAccrual.Accrued_Child_Amount,
                Opening_Balance_Adult_Amount: previousAirTicketAccrual.Accrued_Adult_Amount
            };

            await organizationDbConnection('emp_air_ticket_accrual')
                .insert(insertData)
                .onConflict(['Employee_Id', 'Accrual_Month_Year'])
                .merge();
            
        }
 
console.log(airTicketPolicy)
        // Store the original last availed date from employee policy for record keeping
        const originalLastAvailedDate = airTicketPolicy.Last_Availed_Date ? moment(airTicketPolicy.Last_Availed_Date) : moment(airTicketPolicy.Effective_Date);
        const daysSinceLast = currentMonth.isSame(endMonth) ? moment().diff(originalLastAvailedDate, "days"): monthEndDate.diff(originalLastAvailedDate, "days");

        const ticketCalculationResult = await calculateEmployeeTicketDetails(
            organizationDbConnection,
            employeeTicket,
            getAgeCategoryCount,
            daysSinceLast
        );

        
        const accrualRecord = {
            Employee_Id: employeeTicket.Employee_Id,
            Accrual_Month_Year: currentMonth.format('YYYY-MM-DD'),
            Last_Availed_Date: originalLastAvailedDate.format('YYYY-MM-DD'),
            Entitled_Tickets_Per_Year: employeeTicket.Eligibility_Of_Ticket_Claim_Months || 0,
            Accrued_Infant_Tickets: ticketCalculationResult.infantAccruedTicket,
            Accrued_Child_Tickets: ticketCalculationResult.childAccruedTicket,
            Accrued_Adult_Tickets: ticketCalculationResult.adultAccruedTicket,
            Accrued_Total_Tickets: ticketCalculationResult.totalAccruedTickets,
            Accrued_Infant_Amount: ticketCalculationResult.infantAccruedAmount,
            Accrued_Child_Amount: ticketCalculationResult.childAccruedAmount,
            Accrued_Adult_Amount: ticketCalculationResult.adultAccruedAmount,
            Accrued_Total_Amount: ticketCalculationResult.totalAccruedAmount,
            Accrued_Infant_Count: ticketCalculationResult.infantCount,
            Accrued_Child_Count: ticketCalculationResult.childCount,
            Accrued_Adult_Count: ticketCalculationResult.adultCount,
            Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
        };

        await organizationDbConnection('emp_air_ticket_accrual')
            .insert(accrualRecord)
            .onConflict(['Employee_Id', 'Accrual_Month_Year'])
            .merge();

        currentMonth.add(1, 'month');
    }
}


/**
 * Update settlement summary status to 'Cancelled' where availed_date matches inactive date
 */
async function updateSettlementSummaryStatus(organizationDbConnection, employeeTicket, inactiveDate) {
    const inactiveDateFormatted = inactiveDate.format('YYYY-MM-DD');

    const settlementSummary = await organizationDbConnection(ehrTables.airTicketSettlementSummary)
        .where('Employee_Id', employeeTicket.Employee_Id)
        .where('Settlement_Status', 'Yet to be Settled')
        .where('Availed_Date', inactiveDateFormatted)
        .orderBy('Availed_Date', 'desc')
        .first();

    if(settlementSummary){
        await organizationDbConnection(ehrTables.airTicketSettlementSummary)
            .where('Air_Ticket_Settlement_Summary_Id', settlementSummary.Air_Ticket_Settlement_Summary_Id)
            .update({
                Settlement_Status: 'Cancelled',
                Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss')
            });

        await organizationDbConnection(ehrTables.empAirTicketPolicy)
            .update({
                Last_Availed_Date: settlementSummary.Settlement_From_Date === settlementSummary.Effective_Date
                    ? null : settlementSummary.Settlement_From_Date
            })
            .where('Employee_Id', employeeTicket.Employee_Id);
    }
}
