// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formIds } = require('../../common/appconstants');

//fuction to list holidays based on location
let organizationDbConnection;
module.exports.listProjectActivities = async (parent, args, context, info) => {
    console.log('Inside listProjectActivities function');
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let loginEmployeeId = context.Employee_Id;
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, null, '', 'UI', false, formIds.projects);
            if (Object.keys(checkRights).length > 0 && checkRights.Role_View === 1) {
                return(
                    await organizationDbConnection(ehrTables.activitiesMaster + " as AM")
                    .select('AM.Activity_Id as activityId','AM.Activity_Name as activityName', 'AM.Is_Billable as isBillable', 'AM.Description as description', 'AM.Added_On as addedOn', 'AM.Updated_On as updatedOn',organizationDbConnection.raw("CONCAT_WS(' ',EPI.Emp_First_Name, EPI.Emp_Middle_Name, EPI.Emp_Last_Name) as updatedByName"), organizationDbConnection.raw("CONCAT_WS(' ',EPI2.Emp_First_Name,EPI2.Emp_Middle_Name, EPI2.Emp_Last_Name) as addedByName"))
                    .leftJoin(ehrTables.empPersonalInfo + " as EPI", "EPI.Employee_Id", "AM.Updated_By")
                    .leftJoin(ehrTables.empPersonalInfo + " as EPI2", "EPI2.Employee_Id", "AM.Added_By")
                    .orderBy('Activity_Id', 'desc')
                    .then(async (data) => {
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        return { errorCode: "", message: "Project activities has been listed successfully.", activities: data};
                    })
                    .catch((catchError) => {
                        let errResult = commonLib.func.getError(catchError, 'CHR0107');
                        //Destroy DB connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        throw new ApolloError(errResult.message, errResult.code);
                    })
                )
            }
            else {
                console.log('Employee do not have view access rights');
                throw '_DB0100';
            }
    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in listProjectActivities function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'CHR0075');
        throw new ApolloError(errResult.message, errResult.code)
       
    }
}
