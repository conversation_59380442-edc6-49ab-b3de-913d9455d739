// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../common/tablealias');
//Require apollo server to return error message
const { UserInputError, ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formName, formIds, systemLogs } = require('../../../common/appconstants');
const { validateCommonRuleInput } = require('../../../common/inputValidations');
const moment = require('moment');

module.exports.addUpdateGeneralSettings = async (parent, args, context, info) => {
    let organizationDbConnection;
    let validationError = {};
    try {
        console.log("Inside addUpdateGeneralSettings function.");
        let employeeId = context.Employee_Id;
        let accessFormId = args.formId || formIds.generalSettings;

        organizationDbConnection = knex(context.connection.OrganizationDb);

        // Check employee access rights
        let checkRights = await commonLib.func.checkEmployeeAccessRights(
            organizationDbConnection,
            employeeId,
            null,
            '',
            'UI',
            false,
            accessFormId
        );

        // Determine if this is add or update operation
        let isAdd = !args.generalSettingId || args.generalSettingId === 0;

        if (checkRights?.Role_Update === 1 &&
            checkRights?.Employee_Role?.toLowerCase() === 'admin') {

            // Validate inputs
            validationError = await validateGeneralSettingsInputs(args);

            if (Object.keys(validationError).length > 0) {
                throw 'IVE0000';
            }

            // Prepare data for insert/update
            let generalSettingsData = {
                Page_Title: args.pageTitle || null,
                Favicon_Filename: args.faviconFilename || null,
                Company_Logo: args.companyLogo || null,
                Use_Company_Logo_As_Product_Logo: args.useCompanyLogoAsProductLogo || "No",
                Primary_Color: args.primaryColor || null,
                Secondary_Color: args.secondaryColor || null,
                Hover_Color: args.hoverColor || null,
                Table_Header_Color: args.tableHeaderColor || null,
                Table_Header_Text_Color: args.tableHeaderTextColor || null,
                Career_Banner_Image: args.careerBannerImage || null,
                Career_Headline_Text: args.careerHeadlineText || null,
                Career_Sub_Headline_Text: args.careerSubHeadlineText || null,
                Career_Text_Horizontal_Position: args.careerTextHorizontalPosition || null,
                Career_Text_Vertical_Position: args.careerTextVerticalPosition || null,
                Career_Banner_Opacity: args.careerBannerOpacity || null,
                Career_Headline_Font_Family: args.careerHeadlineFontFamily || null,
                Career_Heading_Font_Size: args.careerHeadingFontSize || null,
                Career_Headline_Font_Color: args.careerHeadlineFontColor || null,
                Career_Sub_Headline_Font_Family: args.careerSubHeadlineFontFamily || null,
                Career_Sub_Headline_Font_Size: args.careerSubHeadlineFontSize || null,
                Career_Sub_Headline_Font_Color: args.careerSubHeadlineFontColor || null,
                Career_Logo_Path: args.careerLogoPath || null
            };

            // Execute database operations within a transaction
            await organizationDbConnection
                .transaction(function (trx) {
                    if (isAdd) {
                        // Add operation
                        generalSettingsData.Added_On = moment().utc().format('YYYY-MM-DD HH:mm:ss');
                        generalSettingsData.Added_By = employeeId;

                        // Execute both operations concurrently using Promise.all
                        return Promise.all([
                            organizationDbConnection(ehrTables.generalSettings)
                                .insert(generalSettingsData)
                                .transacting(trx),
                            organizationDbConnection(ehrTables.orgDetails)
                                .update({
                                    Report_LogoPath: args.companyLogo || null,
                                    Use_Report_Logo_As_Product_Logo: args.useCompanyLogoAsProductLogo || "No"
                                })
                                .transacting(trx)
                        ]);

                    } else {
                        // Update operation
                        generalSettingsData.Updated_On = moment().utc().format('YYYY-MM-DD HH:mm:ss');
                        generalSettingsData.Updated_By = employeeId;

                        // Execute both operations concurrently using Promise.all
                        return Promise.all([
                            organizationDbConnection(ehrTables.generalSettings)
                                .where('General_Setting_Id', args.generalSettingId)
                                .update(generalSettingsData)
                                .transacting(trx),
                            organizationDbConnection(ehrTables.orgDetails)
                                .update({
                                    Report_LogoPath: args.companyLogo || null,
                                    Use_Report_Logo_As_Product_Logo: args.useCompanyLogoAsProductLogo || "No"
                                })
                                .transacting(trx)
                        ]);
                    }
                });

            // Add System Log
            let systemLogParams = {
                action: systemLogs.roleUpdate,
                userIp: context.User_Ip,
                employeeId: employeeId,
                formName: formName.generalSettings,
                formId: formIds.generalSettings,
                organizationDbConnection: organizationDbConnection,
                message: `General settings updated successfully`
            };

            // Call function to add the system log
            await commonLib.func.createSystemLogActivities(systemLogParams);

            // Destroy DB connection
            organizationDbConnection ? organizationDbConnection.destroy() : null;

            // Return success response
            return {
                errorCode: "",
                message: "General settings have been updated successfully."
            };

        } else {
            console.error('Login employee id does not have update access');
            if (!checkRights || checkRights.Role_Update !== 1) {
                throw '_DB0102';
            } else {
                throw '_DB0109';
            }
        }
    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in addUpdateGeneralSettings function main catch block.', e);

        if (e === 'IVE0000') {
            let errResult = commonLib.func.getError('', 'IVE0000');
            throw new UserInputError(errResult.message, { validationError: validationError });
        } else {
            let errResult = commonLib.func.getError(e, 'GS0002');
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
}


/**
 * Validates the input arguments for general settings
 * @param {Object} args - The input arguments object
 * @param {string} [args.careerHeadlineText] - Career headline text
 * @param {string} [args.careerSubHeadlineText] - Career sub headline text
 * @param {string} [args.useCompanyLogoAsProductLogo] - Whether to use company logo as product logo ('Yes'/'No')
 * @param {string} [args.primaryColor] - Primary color in hex format
 * @param {string} [args.secondaryColor] - Secondary color in hex format
 * @param {string} [args.hoverColor] - Hover color in hex format
 * @param {string} [args.tableHeaderColor] - Table header color in hex format
 * @param {string} [args.tableHeaderTextColor] - Table header text color in hex format
 * @param {string} [args.careerHeadlineFontColor] - Career headline font color in hex format
 * @param {string} [args.careerSubHeadlineFontColor] - Career sub headline font color in hex format
 * @param {number} [args.careerBannerOpacity] - Career banner opacity (0-100)
 * @returns {Object} Validation error object with error codes as keys and messages as values
 * @throws {Error} If validation process encounters an error
 */
async function validateGeneralSettingsInputs(args) {
    let validationError = {};

    try {
        // Define field validations based on documentation requirements
        const fieldValidations = {};

        // Add validations for fields that have specific rules  
        if (args.careerHeadlineText) {
            fieldValidations.careerHeadlineText = 'IVE0625';
        }
        if (args.careerSubHeadlineText) {
            fieldValidations.careerSubHeadlineText = 'IVE0628';
        }

        // Validate using common rule input function
        if (Object.keys(fieldValidations).length > 0) {
            validationError = await validateCommonRuleInput(args, fieldValidations);
        }

        // Custom validations

        // Validate enum values
        if (args.useCompanyLogoAsProductLogo &&
            !['Yes', 'No'].includes(args.useCompanyLogoAsProductLogo)) {
            validationError['IVE0626'] = 'Invalid value. Must be Yes or No.';
        }

        // Validate color formats (hex codes)
        const colorFields = [
            { field: args.primaryColor, name: 'Primary Color' },
            { field: args.secondaryColor, name: 'Secondary Color' },
            { field: args.hoverColor, name: 'Hover Color' },
            { field: args.tableHeaderColor, name: 'Table Header Color' },
            { field: args.tableHeaderTextColor, name: 'Table Header Text Color' },
            { field: args.careerHeadlineFontColor, name: 'Career Headline Font Color' },
            { field: args.careerSubHeadlineFontColor, name: 'Career Sub Headline Font Color' }
        ];

        colorFields.forEach((colorField, index) => {
            if (colorField.field && !isValidHexColor(colorField.field)) {
                validationError[`IVE0624_${index}`] = colorField.name + ' - Invalid color format. Must be a valid hex color code (e.g., #FF0000).';
            }
        });

        // Validate opacity range (0-100)
        if (args.careerBannerOpacity !== null && args.careerBannerOpacity !== undefined) {
            if (args.careerBannerOpacity < 0 || args.careerBannerOpacity > 100) {
                validationError['IVE0627'] = 'Opacity must be between 0 and 100.';
            }
        }

        return validationError;

    } catch (error) {
        console.log('Error in validateGeneralSettingsInputs:', error);
        throw error;
    }
}


/**
 * Validates if a given color string is a valid hexadecimal color code
 * @param {string} [color] - The color string to validate (e.g., "#FF0000" or "#F00")
 * @returns {boolean} True if color is valid hex code or empty, false otherwise
 */
function isValidHexColor(color) {
    if (!color) return true;
    const hexColorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
    return hexColorRegex.test(color);
}
