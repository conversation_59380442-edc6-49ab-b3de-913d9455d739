// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../common/tablealias');
//Require apollo server to return error message
const { UserInputError, ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formName, formIds, systemLogs } = require('../../../common/appconstants');
//Require validation function
const { numberValidation } = require('../../../common/commonvalidation');

module.exports.deleteProjectActivity = async (parent, args, context, info) => {
    let organizationDbConnection;
    let errResult;
    let validationError = {};
    try {
        console.log("Inside deleteProjectActivity function.");
        let employeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        // check their rights
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, employeeId, null, '', 'UI', false, formIds.projects);
        if (Object.keys(checkRights).length > 0 && checkRights.Role_Delete === 1) {
            let activityId = args.activityId;
            if(args.activityId){
                if(args.activityId < 1 || !(numberValidation(args.activityId)) ){
                    validationError['IVE0419'] = commonLib.func.getError('', 'IVE0419').message1;
                }
            } else{
                validationError['IVE0419'] = commonLib.func.getError('', 'IVE0419').message;
            }
            //Check validation error exist or not
            if (Object.keys(validationError).length === 0) {
                        return(
                            organizationDbConnection(ehrTables.projectActivities)
                            .select("*")
                            .where('Activity_Id',activityId)
                            .then((result) => {
                                if(result.length === 0){
                                    return(
                                        organizationDbConnection(ehrTables.activitiesMaster)
                                        .delete()
                                        .where('Activity_Id', activityId)
                                        .then(async(result) => {
                                            if(result){
                                                let systemLogParams = {
                                                    userIp: context.User_Ip,
                                                    employeeId: employeeId,
                                                    organizationDbConnection: organizationDbConnection,
                                                    message: `Activity Id ${args.activityId} was deleted by ${employeeId}`
                                                };
                                                //Call function to add the system log
                                                await commonLib.func.createSystemLogActivities(systemLogParams);
                                                //destroy the connection
                                                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                                return { errorCode: "", message: "Project activity deleted successfully."};
                                            }
                                            else {
                                                console.log('The project activity from activities master table cannot be deleted');
                                                throw 'CHR0108';
                                            }
                                        })
                                    )                                           
                                } else {
                                    console.log("Unable to delete the project activity as this activity is associated with a project.");
                                    throw('CHR0109');
                                }
                            })
                            .catch((e) => {
                                console.log('Error while deleting the project activity .catch block', e);
                                throw(commonLib.func.getError(e, 'CHR0111'));
                            })
                        )
            } else {
                throw 'IVE0000';
            } 
        }
        else {
            console.log("The employee does not have delete access.");
            throw '_DB0103';
        }

    }
    catch (e) {
        console.log('Error in deleteProjectActivity function main catch block.', e);
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        //If validation error exist
        if (e === 'IVE0000') {
            console.log('Validation error in the deleteProjectActivity() function', validationError);
            errResult = commonLib.func.getError('', 'IVE0000');
            throw new UserInputError(errResult.message, { validationError: validationError });//Return error response
        } else {
            errResult = commonLib.func.getError(e, 'CHR0076');
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
}