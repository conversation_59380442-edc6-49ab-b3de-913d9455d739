// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formName, formIds } = require('../../common/appconstants');


module.exports.retrieveCompoffRules = async (parent, args, context, info) => {
    let organizationDbConnection;
    try {
        console.log("Inside retrieveCompoffRules function.")
        let employeeId = context.Employee_Id;
        let accessFormName = args.formName ? args.formName : formName.compOff;
        let formId = formIds.compOff;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        // check their rights
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, employeeId, accessFormName, '', 'UI');
        if (Object.keys(checkRights).length > 0 && checkRights.Role_View === 1) {
            return (
                organizationDbConnection(ehrTables.compoffConfiguration + " as CC")
                    .select('CC.*', 'CEG.Group_Name', 'CGA.Custom_Group_Id', 'COB.Comp_Off_Balance_Id',
                        organizationDbConnection.raw("CASE WHEN CGA.Custom_Group_Id IS NOT NULL THEN (CASE WHEN COB.Comp_Off_Balance_Id IS NOT NULL THEN 'true' ELSE 'false' END) ELSE NULL END AS compOffBalance")
                        , organizationDbConnection.raw("CONCAT_WS(' ',EPI.Emp_First_Name, EPI.Emp_Middle_Name, EPI.Emp_Last_Name) as Updated_By"), organizationDbConnection.raw("CONCAT_WS(' ',EPI2.Emp_First_Name,EPI2.Emp_Middle_Name, EPI2.Emp_Last_Name) as Added_By"))
                    .from(ehrTables.compoffConfiguration + " as CC")
                    .leftJoin(ehrTables.empPersonalInfo + " as EPI", "EPI.Employee_Id", " CC.Updated_By")
                    .leftJoin(ehrTables.empPersonalInfo + " as EPI2", "EPI2.Employee_Id", "CC.Added_By")
                    .leftJoin(ehrTables.customGroupAssociated + " as CGA", function () {
                        this.on("CC.Configuration_Id", "=", "CGA.Parent_Id")
                            .andOn("CGA.Form_Id", "=", formId);
                    }).leftJoin(ehrTables.cusEmpGroup + " as CEG", "CEG.Group_Id", "CGA.Custom_Group_Id")
                    .leftJoin(ehrTables.customGroupEmployees + " as CGE", "CGE.Group_Id", "CGA.Custom_Group_Id")
                    .leftJoin(ehrTables.compensatoryOffBalance + " as COB", 'COB.Employee_Id', 'CGE.Employee_Id')
                    .groupBy('CC.Configuration_Id')
                    .then(async (data) => {
                        if (data) {
                            const compOffBalancedata = await organizationDbConnection(ehrTables.compensatoryOffBalance).select('*');

                            if (compOffBalancedata && compOffBalancedata.length > 0) {
                                await updateCompOffBalance(data, "true")
                            } else {
                                await updateCompOffBalance(data, "false")
                            }
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            return { errorCode: "", message: "Compoff rules retrieved successfully.", CompoffRules: data };

                        } else {
                            console.log('Compoff rules data not found')
                            throw 'SCR0101'
                        }
                    })
                    .catch((err) => {
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        console.log('Error in retrieveCompoffRules .catch() block', err);
                        let errResult = commonLib.func.getError(err, 'SCR0101');
                        throw new ApolloError(errResult.message, errResult.code);
                    })
            )
        }
        else {
            throw '_DB0100';
        }

    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in retrieveCompoffRules function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'SCR0001');
        throw new ApolloError(errResult.message, errResult.code);
    }
}

async function updateCompOffBalance(data, valueToBeSet) {

    for (const item of data) {
        if (!item.compOffBalance) {
            item.compOffBalance = valueToBeSet;
        }
    }
}