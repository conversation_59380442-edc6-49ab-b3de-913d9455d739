//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require constants
const { ehrTables } = require('../../../common/tablealias');
const { formName } = require('../../../common/appconstants');
const { ApolloError } = require('apollo-server-lambda');
const { getWorkingHours } = require('../../../common/commonfunctions');
const moment = require('moment');
module.exports.retrieveTimesheetEmpLeaveWeekOff = async (parent, args, context, info) => {
    let organizationDbConnection;
    
    try {
        console.log("Inside retrieveTimesheetEmpLeaveWeekOff function()", context.Employee_Id)
        let logInEmpId = context.Employee_Id;
        // get the organization database connection
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let endDate = moment(args.weekendDate).format('YYYY-MM-DD');
        let startDate = moment(endDate).subtract(6, 'days').format('YYYY-MM-DD');
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, logInEmpId, formName.timeSheet, '', 'UI',false,args.formId);
        if (Object.keys(checkRights).length === 0) {
            throw ('_DB0100');
        }
            let [dateOfJoin,exitDate,leaveResponse,compOffResponse,employeeWorkScheduleHolidayDetails,workHoursDetails]=await Promise.all([
                commonLib.func.getDateOfJoiningBasedOnEmpId(organizationDbConnection, args.employeeId),
                commonLib.payroll.getEmployeeResignationDate(organizationDbConnection,args.employeeId),
                getLeavesBetweenDates(organizationDbConnection, startDate, endDate,args.employeeId),
                commonLib.employees.getCompensatoryOff(args.employeeId, startDate, endDate, 'shift-calendar', organizationDbConnection),
                commonLib.shiftAndTimeManagement.fetchEmployeeWeekOffAndHolidayDetails(organizationDbConnection, args.employeeId, startDate, endDate),
                getWorkingHours(organizationDbConnection,args.employeeId)
            ]);

            // Extract employee-specific data from the common function response
            const employeeData = employeeWorkScheduleHolidayDetails?.[args.employeeId] || {};
            const {
                weekOffAndHolidayDetails = []
            } = employeeData;

            let responseData=[{
                employeeId: args.employeeId,
                dateOfJoin,
                exitDate,
                leaveResponse,
                compOffResponse,
                weekOffHolidayDetails: weekOffAndHolidayDetails,
                workHoursDetails
            }]

                organizationDbConnection ? organizationDbConnection.destroy() : null;
                return { errorCode: "", message: "employee details retrieved successfully.", employeeDetails: JSON.stringify(responseData) };
            
                
            
        
    }
    catch (e) {
        console.log('Error in the retrieveTimesheetEmpLeaveWeekOff() function catch block. ', e);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        
        let errResult = commonLib.func.getError(e, 'CHR0083');
        // return response
        throw new ApolloError(errResult.message, errResult.code);
    }
}


async function getLeavesBetweenDates(organizationDbConnection, leaveStartDate, leaveEndDate,employeeId) {
    try {
        const leaves = await organizationDbConnection(ehrTables.empLeaves).select('Start_Date', 'End_Date', 'Hours','Approval_Status','LeaveType_Id')
        .whereIn('Approval_Status', ['Applied', 'Approved', 'Returned', 'Cancel Applied'])
        .where('Employee_Id', employeeId)
            .where(function () {
                this.whereBetween('Start_Date', [leaveStartDate, leaveEndDate])
                    .orWhereBetween('End_Date', [leaveStartDate, leaveEndDate])
                    .orWhere(function () {
                        this.where('Start_Date', '<=', leaveStartDate)
                            .andWhere('End_Date', '>=', leaveStartDate)
                    })
                    .orWhere(function () {
                            this.where('Start_Date', '<=', leaveEndDate)
                            .andWhere('End_Date', '>=', leaveEndDate)})
                    });
            // Extract and combine dates
  const leaveDates = leaves.flatMap(leave => {
    const startDate = moment(leave.Start_Date);
    const endDate = moment(leave.End_Date);

    const datesInRange = [];
    for (let date = startDate.clone(); date.isSameOrBefore(endDate); date.add(1, 'day')) {
        datesInRange.push({date: date.format('YYYY-MM-DD'),startDate,endDate,hours:leave.Hours});
    }
    return datesInRange;
});
  
  // Remove duplicates and sort
  const uniqueDates = [...new Set(leaveDates)].sort();
        return uniqueDates;
    } catch (error) {
        console.log('Error fetching leaves:', error);
        throw error;
    }
}
