// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../common/tablealias');
// require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');

//function to retrieve dynamic form field values
module.exports.retrieveDynamicFieldValues = async (parent, args, context, info) => {
    let organizationDbConnection;
    let result = [];
    try {
        console.log("Inside retrieveDynamicFieldValues function.");
        const loginEmployeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);

        let employeeRolesId = null

        // Access Check
        if (args.formId) {
            if (!process.env.endPoint || process.env.endPoint?.toLowerCase() !== 'external') {
                let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, null, '', 'UI', false, args.formId);
                if (Object.keys(checkRights).length === 0 || checkRights.Role_View !== 1) {
                    throw ('_DB0100');
                }
                //Get the employee role id
                const employeeRoles = await organizationDbConnection(ehrTables.empJob)
                    .select('Roles_Id')
                    .where('Employee_Id', loginEmployeeId)
                    .first();

                if (employeeRoles) {
                    employeeRolesId = employeeRoles.Roles_Id
                }
            }
        } else {
            //Form Id does not exist
            throw '_DB0104'
        }

        // Step 1: Retrieve dynamic fields data table name
        let dynamicFieldsDataTable = await organizationDbConnection(ehrTables.customFieldTables)
            .where('Form_Id', args.formId)
            .first()

        dynamicFieldsDataTable = dynamicFieldsDataTable ? dynamicFieldsDataTable.Table_Name : null;

        if (!dynamicFieldsDataTable) {
            console.log('No dynamic fields data table found for the form.');
            throw ('CF00003');
        }

        // Step 2: Retrieve dynamic fields enabled in the form
        // Step 3: Retrieve dynamic fields values
        let [dynamicFieldsData, dynamicFieldsValues] = await Promise.all([
            organizationDbConnection(ehrTables.customFields + " as CF")
                .select("CF.*", "CFAF.*")
                .leftJoin(ehrTables.customFieldAssociatedForms + " as CFAF", "CFAF.Custom_Field_Id", "CF.Custom_Field_Id")
                .where("CFAF.Form_Id", args.formId)
                .where(function () {
                    if (employeeRolesId) {
                        this.whereNull('CF.Roles_Id')
                            .orWhereRaw("JSON_CONTAINS(CF.Roles_Id, ?)", [JSON.stringify(employeeRolesId)]);
                    }
                }),
            organizationDbConnection(dynamicFieldsDataTable)
                .select("*")
                .where("Primary_Id", args.primaryId)
                .first()
        ]);

        // Step 4: Parse dynamic fields values
        let dynamicFieldValueData = null;
        if (dynamicFieldsValues?.Custom_Field_Value) {
            dynamicFieldValueData = JSON.parse(dynamicFieldsValues.Custom_Field_Value);
        }

        // Step 5: Merge dynamic fields data and values
        if (dynamicFieldsData && dynamicFieldsData.length) {
            dynamicFieldsData.forEach((dynamicField) => {
                if (dynamicFieldValueData && dynamicFieldValueData[dynamicField.Custom_Field_Id]) {
                    dynamicField.Value = dynamicFieldValueData[dynamicField.Custom_Field_Id];
                } else {
                    dynamicField.Value = null;
                }
            });
        }

        result = JSON.stringify(dynamicFieldsData);

        return {
            errorCode: "",
            message: "Custom field values retrieved successfully.",
            dynamicFieldValues: result
        };

    } catch (e) {
        console.log('Error in retrieveDynamicFieldValues function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'CF00001');
        throw new ApolloError(errResult.message, errResult.code);
    } finally {
        //Destroy DB connection
        if (organizationDbConnection) {
            organizationDbConnection.destroy();
        }
    }
}
