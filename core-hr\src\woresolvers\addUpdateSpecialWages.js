// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../common/tablealias');
//Require apollo server to return error message
const { UserInputError, ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formName, systemLogs, formIds } = require('../../common/appconstants');
const { insertDataInTable } = require('../../common/commonfunctions');
//Require validation function
const { validateCommonRuleInput } = require('../../common/inputValidations');
const moment = require('moment');
module.exports.addUpdateSpecialWages = async (parent, args, context, info) => {
    let organizationDbConnection;
    let errResult;
    let validationError = {};
    let formId = formIds.specialWages;
    try {
        let loginEmployeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        const Special_Wages_Coverage_array = await commonLib.func.getCoverage(organizationDbConnection, formId,ehrTables.formLevelCoverage);
        const Special_Wages_Coverage=Special_Wages_Coverage_array[0].Coverage
        const { Configuration_Id, CustomGroup_Id, Attendance_Required, Wage_Factor, Salary_Type, Special_Work_Days, Status, } = args;
        let systemLogParams = {
            userIp: context.User_Ip,
            employeeId: loginEmployeeId,
            formName: formName.specialWages,
            trackingColumn: 'Configuration_Id',
            organizationDbConnection: organizationDbConnection,
            uniqueId: Configuration_Id
        };
        // check their rights
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, formName.specialWages, '', 'UI');
        if (Object.keys(checkRights).length > 0 && ((Configuration_Id === 0 && checkRights.Role_Add === 1) || (Configuration_Id > 0 && checkRights.Role_Update === 1))) {

            const fieldValidations = {};
            if(Wage_Factor){
            fieldValidations.Wage_Factor= "IVE0376";
            validationError = await validateCommonRuleInput(args, fieldValidations);
        }
            if (Object.keys(validationError).length > 0) {
                throw ('IVE0000');
            }

            else {
                return (
                    await organizationDbConnection
                        .transaction(function (trx) {
                            return (
                                organizationDbConnection(ehrTables.specialWages + " as SW")
                                    .transacting(trx)
                                    .select('SW.Configuration_Id', 'SW.Salary_Type', 'SW.Special_Work_Days', 'FLC.Coverage')
                                    .leftJoin(ehrTables.formLevelCoverage + " as FLC", "FLC.Form_Id", formId)
                                    .where("SW.Salary_Type", Salary_Type)
                                    .where("SW.Special_Work_Days", Special_Work_Days)
                                    .where("SW.Status", "Active")
                                    .modify(function (queryBuilder) {


                                        // Conditionally add the 'Configuration_Id' filter
                                        if (Configuration_Id !== 0) {
                                            queryBuilder.whereNot('SW.Configuration_Id', Configuration_Id);
                                        }
                                        if ((CustomGroup_Id && Special_Wages_Coverage.toLowerCase() === "custom group") || (CustomGroup_Id && Configuration_Id)) {
                                            queryBuilder.leftJoin(ehrTables.customGroupAssociated + " as CGA", "SW.Configuration_Id", "CGA.Parent_Id");
                                            queryBuilder.where("CGA.Custom_Group_Id", CustomGroup_Id);
                                            queryBuilder.where("CGA.Form_Id", formId);
                                            queryBuilder.select('CGA.Custom_Group_Id');
                                        }
                                    })
                                    .then(async (customGroupSettings) => {
                                        if ((Special_Wages_Coverage === "Organization" && CustomGroup_Id) || (Special_Wages_Coverage.toLowerCase() === "custom group" && !CustomGroup_Id)) {
                                            throw 'SSW0106'
                                        }
                                        if (Salary_Type === "Monthly" && Attendance_Required === "Yes") {
                                            throw 'SSW0105'
                                        }

                                        else if
                                            (customGroupSettings.length > 0) {
                                            console.log("duplicate record exist")
                                            throw 'SSW0104';
                                        }

                                        else {
                                            let currentDateTime = moment().utc().format('YYYY-MM-DD HH:mm:ss');
                                            let specialWagesData = {
                                                "Salary_Type": Salary_Type,
                                                "Special_Work_Days": Special_Work_Days,
                                                "Attendance_Required": Attendance_Required,
                                                "Status": Status,
                                                "Wage_Factor": Wage_Factor,
                                            }

                                            if (Configuration_Id) {

                                                specialWagesData.Updated_On = currentDateTime;
                                                specialWagesData.Updated_By = loginEmployeeId;
                                                return (
                                                    organizationDbConnection(ehrTables.specialWages)
                                                        .transacting(trx)
                                                        .where("Configuration_Id", Configuration_Id)
                                                        .update(specialWagesData)
                                                        .then(async (updateResult) => {

                                                            if (updateResult) {
                                                                if (CustomGroup_Id) {
                                                                    let customGroupData = [{ "Parent_Id": Configuration_Id, "Form_Id": formId, "Custom_Group_Id": CustomGroup_Id }];
                                                                    /**Delete the custom group association */
                                                                    return (organizationDbConnection(ehrTables.customGroupAssociated)
                                                                        .transacting(trx)
                                                                        .del()
                                                                        .where('Form_Id', formId)
                                                                        .where('Parent_Id', Configuration_Id)

                                                                        .then(async () => {
                                                                            await insertDataInTable(organizationDbConnection, trx, customGroupData, ehrTables.customGroupAssociated);
                                                                            return true;
                                                                        }).catch((catchError) => {
                                                                            console.log('Error while deleting the custom group associtaion', catchError);
                                                                            throw 'SSW0103'
                                                                        })
                                                                    )
                                                                    /** Insert the custom group association */
                                                                }
                                                                systemLogParams.action = systemLogs.roleUpdate;

                                                                return true;
                                                            }
                                                            else {
                                                                throw 'SSW0102'
                                                            }
                                                        })
                                                )

                                            } else {
                                                //If it is a add action then update the Added_on and Added_By
                                                specialWagesData.Added_On = currentDateTime;
                                                specialWagesData.Added_By = loginEmployeeId;
                                                return (
                                                    organizationDbConnection(ehrTables.specialWages)
                                                        .transacting(trx)
                                                        .insert(specialWagesData)
                                                        .then(async (insertData) => {
                                                            if (insertData && insertData.length) {
                                                                if (CustomGroup_Id) {
                                                                    /** Insert the custom group association */
                                                                    let customGroupData = [{ "Parent_Id": insertData[0], "Form_Id": formId, "Custom_Group_Id": CustomGroup_Id }];
                                                                    insertDataInTable(organizationDbConnection, trx, customGroupData, ehrTables.customGroupAssociated);
                                                                }
                                                                specialWagesData.Configuration_Id = insertData[0];
                                                                systemLogParams.action = systemLogs.roleAdd;
                                                                return true;
                                                            }
                                                            else {
                                                                throw 'SSW0102'
                                                            }

                                                        })
                                                )
                                            }
                                        }
                                    })
                                    .catch((err) => {
                                        //Destroy DB connection
                                        let errResult = commonLib.func.getError(err, 'SSW0103');
                                        if(errResult.code === 'SSW0106'){
                                            let errorMessage = `Special wages configuration is set to ${Special_Wages_Coverage === 'Organization' ? 'Custom Group' : 'Organization'}, conflicting with the platform's ${Special_Wages_Coverage} level setting. Please adjust the platform to ${Special_Wages_Coverage} or establish a new configuration.`; 
                                            errResult.message= errorMessage;
                                        }
                                        // throw err;
                                        throw new ApolloError(errResult.message, errResult.code);
                                    }))

                        }
                        ).then(async (data) => {
                            if (data) {
                                await commonLib.func.createSystemLogActivities(systemLogParams);
                                //destroy the connection
                                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                if (Configuration_Id) {
                                    return { errorCode: "", message: "Special wages configuration details updated successfully." };
                                }
                                return { errorCode: "", message: "Special wages configuration details added successfully." };
                            }
                            else {
                                throw 'SSW0103'
                            }
                        }).catch((catchError) => {
                            console.log('Error while adding the Special wages configuration details', catchError);
                            errResult = commonLib.func.getError(catchError, 'SSW0103');
                            //Destroy DB connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;

                            errResult = commonLib.func.getError(catchError, 'SSW0103');
                            throw new ApolloError(errResult.message, errResult.code);
                        }))
            }

        }

        else {
            if (Configuration_Id) {
                console.log("The employee does not have edit access.");
                throw '_DB0102';
            } else {
                console.log("The employee does not have add access.");
                throw '_DB0101';
            }
        }
    }
    catch (e) {
        console.log('Error in addUpdateSpecialWages  function main catch block.', e);
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        //If validation error exist
        if (e === 'IVE0000') {
            console.log('Validation error in the addUpdateSpecialWages  function', validationError);
            errResult = commonLib.func.getError('', 'IVE0000');
            throw new UserInputError(errResult.message, { validationError: validationError });//Return error response
        }
        else {
            errResult = commonLib.func.getError(e, 'SSW0002');
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
}