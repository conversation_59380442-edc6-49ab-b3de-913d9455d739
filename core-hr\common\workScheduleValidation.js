//Require common validation
const commonValidation = require('./commonvalidation');
//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require table alias
const { ehrTables } = require('./tablealias');

//Function to validate the add work schedule form inputs.
async function validateAddUpdateWorkScheduleInputs(organizationDbConnection,args,isAdd){
    try{
        let validationError = {};
        let workScheduleId = 0;

        if(isAdd === 1){
            workScheduleId = 0;
        }else{
            workScheduleId = args.workScheduleId;
            //Validate the work schedule id
            if(!(args.workScheduleId || commonValidation.numberValidation(args.workScheduleId))
            || args.workScheduleId < 1){
                validationError['IVE0150'] = commonLib.func.getError('', 'IVE0150').message;
            }

            //Validate dashboard type
            if(!['HRMSDASHBOARD','EMPLOYEEMONITORINGDASHBOARD','RECRUITMENTDASHBOARD'].includes(args.dashboardType)){
                validationError['IVE0130'] = commonLib.func.getError('', 'IVE0130').message;
            }
        }

        //Validate the work schedule code
        if(args.workScheduleCode){
            if(!commonValidation.alphaNumSpCDotHySlashValidation(args.workScheduleCode)){
                validationError['IVE0622'] = commonLib.func.getError('', 'IVE0622').message2;
            }else if(!commonValidation.checkLength(args.workScheduleCode,3,20)){
                validationError['IVE0622'] = commonLib.func.getError('', 'IVE0622').message3;
            }
        }

        //Validate the work schedule name
        if(args.workSchedule){
            if(!commonValidation.alphaNumSpCDotHySlashValidation(args.workSchedule)){
                validationError['IVE0188'] = commonLib.func.getError('', 'IVE0188').message2;
            }else if(!commonValidation.checkLength(args.workSchedule,3,100)){
                validationError['IVE0188'] = commonLib.func.getError('', 'IVE0188').message3;
            }else{
                let isWorkScheduleNameExist = await validateWorkScheduleName(organizationDbConnection,args.workSchedule,workScheduleId, args.workScheduleCode);
                if(!isWorkScheduleNameExist){
                    if(args.workScheduleCode){
                        validationError['IVE0188'] = commonLib.func.getError('', 'IVE0188').message5;
                    }else{
                        validationError['IVE0188'] = commonLib.func.getError('', 'IVE0188').message4;
                    }
                }
            }
        }else{
            validationError['IVE0188'] = commonLib.func.getError('', 'IVE0188').message1;
        }

        //Validate the overlapping days
        if(!commonValidation.booleanNumberValidation(args.overlappingDays)){
            validationError['IVE0189'] = commonLib.func.getError('', 'IVE0189').message;
        }

        //Validate the business hours start time
        if(!args.businessHoursStartTime){
            validationError['IVE0190'] = commonLib.func.getError('', 'IVE0190').message;
        }

        //Validate the business hours end time
        if(!args.businessHoursEndTime){
            validationError['IVE0191'] = commonLib.func.getError('', 'IVE0191').message;
        }

        //Validate the shift margin start time(check-in consideration start time)
        if(!commonValidation.checkMinMaxValue(args.shiftMarginStartTimeInHours,0,24)){
            validationError['IVE0192'] = commonLib.func.getError('', 'IVE0192').message;
        }

        //Validate the shift margin end time(check-out consideration end time)
        if(!commonValidation.checkMinMaxValue(args.shiftMarginEndTimeInHours,0,24)){
            validationError['IVE0193'] = commonLib.func.getError('', 'IVE0193').message;
        }

        //Validate the timezone id
        if(args.timezoneId){
            if(!commonValidation.numberValidation(args.timezoneId)){
                validationError['IVE0194'] = commonLib.func.getError('', 'IVE0194').message2;
            }
        }else{
            validationError['IVE0194'] = commonLib.func.getError('', 'IVE0194').message1;
        }

        //Validate the target effort in hours per week
        if(args.targetEffortInHoursPerWeek){
            if(!commonValidation.checkMinMaxValue(args.targetEffortInHoursPerWeek,1,168)){
                validationError['IVE0195'] = commonLib.func.getError('', 'IVE0195').message2;
            }
        }else{
            validationError['IVE0195'] = commonLib.func.getError('', 'IVE0195').message1;
        }

        //Validate the regular hours per day
        if(args.regularHoursPerDay){
            if(!commonValidation.checkMinMaxValue(args.regularHoursPerDay,0.5,24)){
                validationError['IVE0196'] = commonLib.func.getError('', 'IVE0196').message2;
            }
        }else{
            validationError['IVE0196'] = commonLib.func.getError('', 'IVE0196').message1;
        }

        //Validate the overtime hours per day
        if(!commonValidation.checkMinMaxValue(args.overtimeHoursPerDay,0,24)){
            validationError['IVE0197'] = commonLib.func.getError('', 'IVE0197').message;
        }

        //If the regular and overtime hours exist then sum of regular and overtime hours should not exceed 24 hours
        if(args.regularHoursPerDay > 0 && args.overtimeHoursPerDay > 0 &&
        (args.regularHoursPerDay + args.overtimeHoursPerDay) > 24){
            validationError['IVE0205'] = commonLib.func.getError('', 'IVE0205').message;
        }

        // Validate threshold overtime hours per day
        if(args.thresholdOverTimeHoursPerDay !== undefined && args.thresholdOverTimeHoursPerDay !== null){
            if(!commonValidation.checkMinMaxValue(args.thresholdOverTimeHoursPerDay,0,24)){
                validationError['IVE0620'] = commonLib.func.getError('', 'IVE0620').message2;
            }

            // Threshold overtime hours can only be set if overtime hours per day is also set
            if(args.overtimeHoursPerDay !== undefined && args.overtimeHoursPerDay !== null && args.thresholdOverTimeHoursPerDay > args.overtimeHoursPerDay){
                validationError['IVE0621'] = commonLib.func.getError('', 'IVE0621').message1;
            }
        }

        /** Validating the non-mandatory fields. */

        //Validate description
        if(args.description){
            if (!commonValidation.descriptionValidation(args.description)) {
                validationError['IVE0037'] = commonLib.func.getError('', 'IVE0037').message2;
            }else if(!commonValidation.checkLength(args.description,5,500)){
                validationError['IVE0037'] = commonLib.func.getError('', 'IVE0037').message4;
            }
        }

        //Validate the overtime cooling period
        if(!commonValidation.checkMinMaxValue(args.overTimeCoolingPeriod,0,300)){
            validationError['IVE0198'] = commonLib.func.getError('', 'IVE0198').message;
        }

        //Validate the allow attendance outside regular hours
        if(!commonValidation.booleanNumberValidation(args.allowAttendanceOutsideRegularHours)){
            validationError['IVE0199'] = commonLib.func.getError('', 'IVE0199').message1;
        }else{
            //If attendance outside regular hours is zero
            if(args.allowAttendanceOutsideRegularHours === 0){
                //Validate the early check-in override
                if(!commonValidation.booleanNumberValidation(args.earlyCheckInOverride)){
                    validationError['IVE0200'] = commonLib.func.getError('', 'IVE0200').message;
                }

                //Validate the enable grace time
                if(!commonValidation.booleanNumberValidation(args.enableGraceTime)){
                    validationError['IVE0201'] = commonLib.func.getError('', 'IVE0201').message1;
                }else{
                    //If enable grace time is enabled then grace period for check-in is required.
                    if(args.enableGraceTime===1){
                        //Validate the grace period for check-in
                        if(args.gracePeriodForCheckIn){
                            if(!commonValidation.checkMinMaxValue(args.gracePeriodForCheckIn,1,1440)){
                                validationError['IVE0202'] = commonLib.func.getError('', 'IVE0202').message2;
                            }
                        }else{
                            validationError['IVE0202'] = commonLib.func.getError('', 'IVE0202').message1; 
                        }
                    }else{
                        //If the grace time is not enabled then grace period for check-in should not be enabled.
                        if(args.gracePeriodForCheckIn){
                            validationError['IVE0201'] = commonLib.func.getError('', 'IVE0201').message2; 
                        }
                    }
                }

                //Validate the check-out buffer time
                if(args.checkoutBufferTime){
                    if(!commonValidation.checkMinMaxValue(args.checkoutBufferTime,1,60)){
                        validationError['IVE0203'] = commonLib.func.getError('', 'IVE0203').message;
                    }
                }
            }else{
                //If attendance outside regular hours is 1 then the below fields are not applicable
                if(args.earlyCheckInOverride || args.enableGraceTime ||
                args.gracePeriodForCheckIn || args.checkoutBufferTime){
                    validationError['IVE0199'] = commonLib.func.getError('', 'IVE0199').message2;
                }
            }
        }

        return validationError;
    }catch(addWorkScheduleValidationCatchError){
        console.log('Error in the validateAddUpdateWorkScheduleInputs() function in the main catch block.',addWorkScheduleValidationCatchError);
        throw addWorkScheduleValidationCatchError;
    }
}

//Function to check whether the work schedule name exist or not in the table
async function validateWorkScheduleName(organizationDbConnection,workScheduleName,workScheduleId, workScheduleCode){
    try{
        let workScheduleExistQuery = organizationDbConnection(ehrTables.workSchedule)
        .count('WorkSchedule_Id as workScheduleNameExistCount')
        .where('Title',workScheduleName)
        .modify(function(queryBuilder){
            if(workScheduleCode){
                queryBuilder.where('WorkSchedule_Code', workScheduleCode)
            }
        });
        //If work schedule id exist
        if(workScheduleId>0){
            //Form the where condition to check work schedule title exist for the work schedule other than this work schedule id
            workScheduleExistQuery = workScheduleExistQuery.whereNot('WorkSchedule_Id',workScheduleId);
        }
        return(
        workScheduleExistQuery
        .then(async (workScheduleNameExistResult) =>{
            //If the work schedule name does not exist in the table.
            if(workScheduleNameExistResult && workScheduleNameExistResult.length>0 &&
            workScheduleNameExistResult[0].workScheduleNameExistCount===0){
                return 1;
            }else{
                return 0;
            }
        })
        .catch(function (workScheduleNameCatchError) {
            console.log('Error in validateWorkScheduleName() function .catch() block', workScheduleNameCatchError);
            throw workScheduleNameCatchError;
        }))
    }catch(workScheduleNameMainCatchError){
        console.log('Error in the validateWorkScheduleName() function main catch block.',workScheduleNameMainCatchError);
        throw workScheduleNameMainCatchError;
    }
}

//Function to validate whether the work schedule timing does not exceed 24 hours or not.
async function validateWorkScheduleTime(workScheduleTimeValidationArgs){
    try{
        // require moment-timezone
        const moment = require('moment');

        //Variable declaration
        let { businessHoursStartTime, businessHoursEndTime,overlappingDays,
            shiftMarginStartTimeInMinutes,shiftMarginEndTimeInMinutes} = workScheduleTimeValidationArgs;
        let considerationStartDateTime = '',considerationEndDateTime = '';
        
        //Split the business time
        let businessStartTimeInHoursMinutes = businessHoursStartTime.split(":");//09:00
        let businessEndTimeInHoursMinutes = businessHoursEndTime.split(":");//18:00
        
        //Current Date. Example: 2021-09-23
        let currentDate = moment().format("YYYY-MM-DD");
        //Add business start hours and minutes to the current date. Example: 2021-09-23 09:00
        let businessHoursStartDateTime = moment(currentDate).add({hours:businessStartTimeInHoursMinutes[0],minutes:businessStartTimeInHoursMinutes[1]}).format('YYYY-MM-DD HH:mm');
        //If the shift margin start time is greater than 0
        if(shiftMarginStartTimeInMinutes > 0){
            /** Find consideration start date-time by subtracting shift margin start time(minutes)
             * from the regular start date-time. Example: 2021-09-23 07:00 */
            considerationStartDateTime = moment(businessHoursStartDateTime).subtract(shiftMarginStartTimeInMinutes, 'm');
            considerationStartDateTime = moment(considerationStartDateTime).format('YYYY-MM-DD HH:mm');
        }else{
            considerationStartDateTime = moment(businessHoursStartDateTime).format('YYYY-MM-DD HH:mm');//2021-09-23 07:00
        }

        //If the overlapping days is enabled. That is the shift end time falls on the next day from the current day.
        if(overlappingDays){
            let nextDate = moment(currentDate).add(1,'d').format('YYYY-MM-DD');
            businessHoursEndDate = nextDate;
        }else{
            businessHoursEndDate = currentDate;
        }

        //Add business end hours and minutes to the current date. Example: 2021-09-23 18:00
        let businessHoursEndDateTime = moment(businessHoursEndDate).add({hours:businessEndTimeInHoursMinutes[0],minutes:businessEndTimeInHoursMinutes[1]}).format('YYYY-MM-DD HH:mm');
        //If the shift margin end time is greater than 0
        if(shiftMarginEndTimeInMinutes > 0){
            /** Find consideration end date-time by add shift margin start time(minutes)
            * from the regular end date-time. Example: 2021-09-23 21:00 */
            considerationEndDateTime = moment(businessHoursEndDateTime).add(shiftMarginEndTimeInMinutes, 'm');
            considerationEndDateTime = moment(considerationEndDateTime).format('YYYY-MM-DD HH:mm');
        }else{
            considerationEndDateTime = moment(businessHoursEndDateTime).format('YYYY-MM-DD HH:mm');//2021-09-23 21:00
        }

        //Convert consideration start date and end time to another format for comparison
        considerationStartDateTime = moment(considerationStartDateTime);//Moment<2021-09-23T07:00:00+05:30>
        considerationEndDateTime = moment(considerationEndDateTime);//Moment<2021-09-23T21:00:00+05:30>

        //If the consideration end date-time is greater than the consideration start date-time
        if(considerationEndDateTime.isAfter(considerationStartDateTime)){
            //Find the difference between consideration start date-time and consideration end date-time
            let differenceInDateTime=considerationEndDateTime.diff(considerationStartDateTime, "seconds");
            /**One day = 86400 seconds. If the difference is less than or equal to 86400 then 
             * check-in date-time and the checkout date-time does not exceed 23:59 hours.*/
            if (differenceInDateTime > 0 && differenceInDateTime <= 86399) {
                return 'success';
            }else{
                throw 'CWS0003';
            }
        }
        else{
            throw 'CWS0002';
        }
    }catch(validateWorkScheduleTimeCatchError){
        console.log('Error in the validateWorkScheduleTime() function main catch block.',validateWorkScheduleTimeCatchError);
        throw validateWorkScheduleTimeCatchError;
    }
}

//Function to form the week off input list
async function formWeekOffInputList(weekOffList,args){
    try{
        //Variable declaration
        let weekOffInsertArray = [];
        let {workScheduleId,currentDateTime,loginEmployeeId}=args;
        //Iterate the week-off list inputs
        weekOffList.forEach(function(weekOffDayDetail) {
            let weekNumberConstantKeyName = 'enableWeek';
            let durationConstantKeyName = 'durationForWeek';

            /**There are maximum 5 weeks in a month. So iterate for 5 times
             * to form the week off details for all the weeks.*/
            for(let i=1;i<=5;i++){
                let weekOffEnabledForDay = weekOffDayDetail[weekNumberConstantKeyName+i]
                let weekOffDayDuration = weekOffDayDetail[durationConstantKeyName+i];
                //If the week off is enabled and week off duration also exist
                if(weekOffEnabledForDay && weekOffDayDuration){
                    let weekOffDayInsertDetail = {
                        WorkSchedule_Id: workScheduleId,
                        Day_Id: weekOffDayDetail.dayId,
                        Week_Number: i,
                        Exclude_Last_Week: weekOffDayDetail.excludeLastWeek,
                        Duration: weekOffDayDuration,
                        Added_On: currentDateTime,
                        Added_By: loginEmployeeId
                    };

                    weekOffInsertArray.push(weekOffDayInsertDetail);
                }
            }
        });
        //Return response
        return weekOffInsertArray;
    }catch(formWeekOffInputListCatchError){
        console.log('Error in the formWeekOffInputList() function in the main catch block.',formWeekOffInputListCatchError);
        throw formWeekOffInputListCatchError;
    }
}

//Function to validate the week off inputs.
async function validateWeekOffInputs(args){
    try{
        //Require constants
        const { defaultValues } = require('./appconstants');
        //Variable declaration
        let validationError = {};

        //Validate the work schedule id
        if(!(args.workScheduleId || commonValidation.numberValidation(args.workScheduleId))
        || args.workScheduleId < 1){
            validationError['IVE0150'] = commonLib.func.getError('', 'IVE0150').message;
        }

        //Validate dashboard type
        if(!['HRMSDASHBOARD','EMPLOYEEMONITORINGDASHBOARD','RECRUITMENTDASHBOARD'].includes(args.dashboardType)){
            validationError['IVE0130'] = commonLib.func.getError('', 'IVE0130').message;
        }

        //Validate the week-off list
        if(!(args.weekOffList && args.weekOffList.length>0)){
            validationError['IVE0204'] = commonLib.func.getError('', 'IVE0204').message1;
        }else{
            let weekOffListInput = args.weekOffList;
            let dayIdList = defaultValues.dayIdList;
            //Iterate week off list inputs
            for(let key in weekOffListInput){
                //Validate day id
                if(!dayIdList.includes(weekOffListInput[key].dayId)){
                    validationError['IVE0204'] = commonLib.func.getError('', 'IVE0204').message3;
                    break;
                }

                //Validate enableWeek flag for all the weeks
                if(!commonValidation.booleanNumberValidation(weekOffListInput[key].enableWeek1)
                || !commonValidation.booleanNumberValidation(weekOffListInput[key].enableWeek2)
                || !commonValidation.booleanNumberValidation(weekOffListInput[key].enableWeek3)
                || !commonValidation.booleanNumberValidation(weekOffListInput[key].enableWeek4)
                || !commonValidation.booleanNumberValidation(weekOffListInput[key].enableWeek5)){
                    validationError['IVE0204'] = commonLib.func.getError('', 'IVE0204').message4;
                    break;
                }
                
                //Validate duration for all the weeks
                if(!commonValidation.weekOffDurationValidation(weekOffListInput[key].durationForWeek1)
                || !commonValidation.weekOffDurationValidation(weekOffListInput[key].durationForWeek2)
                || !commonValidation.weekOffDurationValidation(weekOffListInput[key].durationForWeek3)
                || !commonValidation.weekOffDurationValidation(weekOffListInput[key].durationForWeek4)
                || !commonValidation.weekOffDurationValidation(weekOffListInput[key].durationForWeek5)){
                    validationError['IVE0204'] = commonLib.func.getError('', 'IVE0204').message5;
                    break;
                }

                //Validate the exclude last week off flag
                if(!['Yes','No'].includes(weekOffListInput[key].excludeLastWeek)){
                    validationError['IVE0204'] = commonLib.func.getError('', 'IVE0204').message6;
                    break;
                }

                //Validate the 4th and 5th week is enabled when the exclude week off flag is selected
                if(weekOffListInput[key].excludeLastWeek === 'Yes' && !(weekOffListInput[key].enableWeek5 
                && weekOffListInput[key].enableWeek4)){
                    validationError['IVE0204'] = commonLib.func.getError('', 'IVE0204').message2;
                    break;
                }
            }
        }
        return validationError;
    }catch(validateWeekOffInputsCatchError){
        console.log('Error in the validateWeekOffInputs() function in the main catch block.',validateWeekOffInputsCatchError);
        throw validateWeekOffInputsCatchError;
    }
}

//Function to validate and form the work schedule update input details based on the add or update work schedule
async function validateAndFormWSUpdateDetails(organizationDbConnection,args,loginEmployeeId,isAdd){
    try{
        //Convert shift margin time from hours to minutes
        let shiftMarginStartTimeInMinutes = args.shiftMarginStartTimeInHours*60;
        let shiftMarginEndTimeInMinutes = args.shiftMarginEndTimeInHours*60;

        let workScheduleTimeValidationArgs = {
            businessHoursStartTime: args.businessHoursStartTime,
            businessHoursEndTime: args.businessHoursEndTime,
            overlappingDays: args.overlappingDays,
            shiftMarginStartTimeInMinutes: shiftMarginStartTimeInMinutes,
            shiftMarginEndTimeInMinutes: shiftMarginEndTimeInMinutes
        };

        //Validate whether the work schedule timing does not exceed 24 hours or not.
        await validateWorkScheduleTime(workScheduleTimeValidationArgs);

        //Get the login employee current date and time based on login employee location
        let currentDateTime = await commonLib.func.getEmployeeTimeZone(loginEmployeeId, organizationDbConnection, 1);

        let workScheduleUpdateJson = {
            'WorkSchedule_Code': args.workScheduleCode,
            'Title':args.workSchedule,
            'Regular_Work_Start_Time':args.businessHoursStartTime,
            'Regular_Work_End_Time':args.businessHoursEndTime,
            'Break_Schedule_Start_Time':args.breakScheduleStartTime,
            'Break_Schedule_End_Time':args.breakScheduleEndTime,
            'Twodays_Flag':args.overlappingDays,
            'OverTime_Cooling_Period':args.overTimeCoolingPeriod,
            'Allow_Attendance_Outside_Regular_WorkHours':args.allowAttendanceOutsideRegularHours,
            'Description':args.description,
            'Grace_Time_Flag':args.enableGraceTime,
            'Check_In_Grace_Time':args.gracePeriodForCheckIn,
            'Check_Out_Time_Buffer':args.checkoutBufferTime,
            'Early_Check_In_Override':args.earlyCheckInOverride,
            'Check_In_Consideration_Time':shiftMarginStartTimeInMinutes,
            'Check_Out_Consideration_Time':shiftMarginEndTimeInMinutes,
            'Target_Effort_In_Hours_Per_Week':args.targetEffortInHoursPerWeek,
            'Zone_Id':args.timezoneId,
            'Regular_Hours_Per_Day':args.regularHoursPerDay,
            'Overtime_Hours_Per_Day':args.overtimeHoursPerDay,
            'Overtime_Threshold_Per_Day':args.thresholdOverTimeHoursPerDay,
            'WorkSchedule_Status': args.status
        };

        //Based on the user action, update the details
        if(isAdd === 1){
            workScheduleUpdateJson.Added_On = currentDateTime;
            workScheduleUpdateJson.Added_By = loginEmployeeId;
        }else{
            workScheduleUpdateJson.Updated_On = currentDateTime;
            workScheduleUpdateJson.Updated_By = loginEmployeeId;
        }

        return workScheduleUpdateJson;
    }catch(workScheduleUpdateDetailsCatchError){
        console.log('Error in the validateAndFormWSUpdateDetails() function main catch block.',workScheduleUpdateDetailsCatchError);
        throw workScheduleUpdateDetailsCatchError;
    }
}

/** Function to validate the following,
 * 1. Validate the attendance exist or not for the non-shift roster employees.
 * 2. Get the shift type associated with the work schedule and validate the
 * shift is scheduled or not for that shift type.
 */
async function validateWSAndAttendance(organizationDbConnection,workScheduleId){
    try{
        return(
        organizationDbConnection(ehrTables.empAttendance+' as EA')
        .count('EA.Attendance_Id as attendanceCount')
        .innerJoin(ehrTables.empJob+' as EJ','EA.Employee_Id','EJ.Employee_Id')
        .where('EJ.Work_Schedule',workScheduleId)
        .then(employeeLevelWSAttendanceResult=>{
            //If the attendance exist
            if(employeeLevelWSAttendanceResult && employeeLevelWSAttendanceResult.length>0 
                && employeeLevelWSAttendanceResult[0].attendanceCount===0){
                return(
                    organizationDbConnection(ehrTables.shiftEmpMapping+' as SEM')
                    .count('SEM.Shift_Schedule_Id as shiftScheduledCount')
                    .innerJoin(ehrTables.empShiftType+' as EST','EST.Shift_Type_Id','SEM.Shift_Type_Id')
                    .where('EST.WorkSchedule_Id',workScheduleId)
                    .then(shiftScheduledResult=>{
                        //If the work schedule id in the shift roster settings and if the shift is scheduled for the work schedule
                        if(shiftScheduledResult && shiftScheduledResult.length>0
                        && shiftScheduledResult[0].shiftScheduledCount===0){
                            return 'success';
                        }else{
                            throw 'CWS0016';
                        }
                    })
                    )
            }else{
                throw 'CWS0015';
            }
        })
        .catch(catchError => {
            console.log('Error in validateWSAndAttendance() function .catch block',catchError);
            throw catchError;
        })
        )
    }catch(validateWSAndAttendanceCatchError){
        console.log('Error in the validateWSAndAttendance() function main catch block.',validateWSAndAttendanceCatchError);
        throw validateWSAndAttendanceCatchError;
    }
}

async function validateWSAssociated(organizationDbConnection,workScheduleId,title,dashboardType,$source){
    try{
        let associatedFormName = [];
        let assForms = '';
        let exInJobQuery = organizationDbConnection(ehrTables.empJob)
                    .count('Work_Schedule as workScheduleInJobCount')
                    .where('Work_Schedule', workScheduleId);
        if($source == 'edit'){
            exInJobQuery = exInJobQuery.where('Emp_Status', 'Active');
        }
        let exInJob = await exInJobQuery.then((count)=>{return count[0].workScheduleInJobCount;});
        if(exInJob > 0) {
            associatedFormName.push('employee job details');
        }

        let exInShiftAllowanceQuery = organizationDbConnection(ehrTables.shiftType)
                        .count('Shift_Name as workScheduleInShiftTypeCount')
                        .where('Shift_Name', workScheduleId); 
        if($source == 'edit'){
            exInShiftAllowanceQuery = exInShiftAllowanceQuery.where('ShiftType_Status', 'Active');
        }
        let exInShiftAllowance = await exInShiftAllowanceQuery.then((count)=>{return count[0].workScheduleInShiftTypeCount;});
        if(exInShiftAllowance > 0) {
            associatedFormName.push('shift allowance');
        }

        let exInShiftTypeQuery =  organizationDbConnection(ehrTables.empShiftType)
                            .count('WorkSchedule_Id as workScheduleInEmpShiftTypeCount')
                            .where('WorkSchedule_Id', workScheduleId);
        if($source == 'edit'){
            exInShiftTypeQuery = exInShiftTypeQuery.where('Status', 'Active');
        }
        let exInShiftType = await exInShiftTypeQuery.then((count)=>{return count[0].workScheduleInEmpShiftTypeCount;});
        if(exInShiftType > 0) {
            associatedFormName.push('shift allowance');
        }
        if(exInShiftType > 0) {
            associatedFormName.push('shift type');
        }

        let exInAttendanceSettingsQuery = organizationDbConnection(ehrTables.attendanceSettings)
                                        .count('WorkSchedule_Id as workScheduleInAttendanceSettingsCount')
                                        .where('WorkSchedule_Id', workScheduleId);
        if($source == 'edit'){
            exInAttendanceSettingsQuery = exInAttendanceSettingsQuery.where('Attendance_Settings_Status', 'Active');
        }
        let exInAttendanceSettings = await exInAttendanceSettingsQuery.then((count)=>{return count[0].workScheduleInAttendanceSettingsCount;});
        if(exInAttendanceSettings > 0) {
            associatedFormName.push('attendance settings');
        }
        let exInCandidates =0, exInEmpMonitoringSettings =0;

        if(dashboardType === 'HRMSDASHBOARD' || dashboardType === 'RECRUITMENTDASHBOARD'){
            let exInCandidatesQuery = organizationDbConnection(ehrTables.candidateJob)
                                .count('Work_Schedule as workScheduleInCandidatesCount')
                                .where('Work_Schedule', workScheduleId);
            if($source == 'edit'){
                exInCandidatesQuery = exInCandidatesQuery.where('Emp_Status', 'Active');
            }
            exInCandidates = await exInCandidatesQuery.then((count)=>{return count[0].workScheduleInCandidatesCount;});
           
            if(exInCandidates > 0) {
                associatedFormName.push('candidate job');
            }
        }else{
            exInEmpMonitoringSettings = await organizationDbConnection(ehrTables.employeeMonitorSettings)
                                        .count('WorkSchedule_Id as workScheduleInMonitoringCount')
                                        .where('WorkSchedule_Id', workScheduleId)
                                        .then((count)=>{return count[0].workScheduleInMonitoringCount;});
            if(exInEmpMonitoringSettings > 0) {
                associatedFormName.push('employee monitoring settings');
            }
        }
        if (associatedFormName.length >0){
            assForms = associatedFormName.join(',');
        }
        return assForms;
    }catch(validateWSAssociatedCatchError){
        console.log('Error in the validateWSAssociated() function main catch block.',validateWSAssociatedCatchError);
        throw validateWSAssociatedCatchError;
    }
}
module.exports={
    validateAddUpdateWorkScheduleInputs,
    validateWorkScheduleTime,
    formWeekOffInputList,
    validateWeekOffInputs,
    validateAndFormWSUpdateDetails,
    validateWSAndAttendance,
    validateWSAssociated
};