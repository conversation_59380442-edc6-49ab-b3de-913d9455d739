const formName = {
    workSchedule: 'Work Schedule',
    accreditation: "Accreditation",
    leaveSettings: 'Leave',
    task_Management: 'Approval Management',
    holidays: 'Holidays',
    projects: 'Projects',
    designationOrPosition: 'Designations/Positions',
    organizationDetails: 'Organization Details',
    coreHr: 'Core HR',
    roster: 'Roster',
    employees: 'Employees',
    userAccounts: 'User Accounts',
    serviceProviderAdmin: 'Service Provider Admin',
    preApprovalSettings: 'Pre Approvals',
    compOff: 'Comp Off',
    preApprovalRequests: 'Pre Approval',
    admin: 'Admin',
    productivityMonitoringAdmin: 'Productivity Monitoring Admin',
    specialWages: 'Special Wages',
    overTime: 'Overtime Configuration',
    businessUnit: 'Business Unit / Cost Center',
    onDutySettings: 'Short Time Off (On Duty)',
    shortTimeOffSettings: 'Short Time Off (Permission)',
    lopRecovery: 'LOP Recovery',
    timeSheet: 'Timesheets',
    leaveOverride: 'Leave Override',
    teamSummary: 'Team Summary',
    room: 'Room',
    customFields: 'Custom Fields',
    employeeIdPrefix: 'Employee Number Series'
};

const status = {
    statusApplied: 'Applied',
    statusRejected: 'Rejected',
    statusApproved: 'Approved',
    statusWaitingForApproval: 'Waiting for Approval',
    statusCompleted: 'Completed',
    statusSubmitted: 'Submitted',
    statusDraft: 'Draft',
    statusIncomplete: 'Incomplete',
    statusDeleted: 'Deleted',
    cancelApproved: "Cancel Approved",
    cancelRejected: "Cancel Rejected"
}

const systemLogs = {
    roleAdd: 'Add',
    roleUpdate: 'Update',
    roleDelete: 'Delete'
};

const defaultValues = {
    dayIdList: [1, 2, 3, 4, 5, 6, 7],
    activeStatus: ['Active'],
    activeInstanceToBeProcessed: 50
};

const awsSesTemplates = {
    inviteTeamMembers: 'InviteTeamMembers',
    requestRights: 'RequestRights',
    performanceGoalsReminder: 'PerformanceGoalsReminder',
    performanceRatingsReminder: 'PerformanceRatingsReminder',
    performanceGoalsPublishedNotification: 'PerformanceGoalsPublishedNotification',
    performanceRatingPublishedNotification: 'PerformanceRatingPublishedNotification',
    commonNotification: 'CommonNotification',
    CommonNotificationEngine: 'CommonNotificationEngine',
    commonTemplate: 'CommonTemplate'
}

const formIds = {
    holidays: 8,
    projects: 3,
    onDuty: 224,
    compOff: 250,
    lopRecovery: 252,
    lopRecoverySettings: 253,
    specialWages: 85,
    overTime: 363,
    preApprovalSelfService: 247,
    preApprovalMyTeam: 257,
    preApprovalSetting: 240,
    holidayTypes: 136,
    projects: 3,
    leaveTypes: 32,
    timeSheetMyTeam: 23,
    timeSheet: 262,
    designations: 237,
    projectSettings: 268,
    organizationGroup: 269,
    leaveOverride: 277,
    room: 280,
    locations: 1,
    serviceProviderAdmin: 219,
    employeeAdmin: 148,
    admin: 22,
    customFields: 309,
    customEmailTemplate:310,
    airTicketSettings:319,
    leaveSettings:235,
    employeeIdPrefix: 359
}

module.exports = {
    formName,
    systemLogs,
    defaultValues,
    status,
    formIds,
    awsSesTemplates
};