//Require table alias
const { ehrTables } = require('./tablealias');

//Function to validate whether designation is associated with any of the forms
async function validateDesignationAssociated(organizationDbConnection,designationId,designationName,source){
    try{
        let associatedFormName = [];
        let assForms = '';

        /*check whether any employees designation is associated with this designation id*/
        let exInJobQuery = organizationDbConnection(ehrTables.empJob)
                            .count('Designation_Id as totalDesignationCount')
                            .where('Designation_Id', designationId);
        if(source == 'edit'){
            exInJobQuery = exInJobQuery.where('Emp_Status', 'Active');
        }
        let exInJob = await exInJobQuery.then((count)=>{return count[0].totalDesignationCount;});
        if(exInJob > 0) {
            associatedFormName.push('employee job details');
        }
        
        let exInDesignationHistory = 0;
        if(source == 'delete'){
            /*check whether any employees designation history is associated with this designation id*/
            exInDesignationHistory = await organizationDbConnection(ehrTables.empDesignationHistory)
                                        .count('Previous_Designation_Id as totalDesignationCount')
                                        .where('Previous_Designation_Id', designationId)
                                        .then((count)=>{return count[0].totalDesignationCount;});
            if(exInDesignationHistory > 0) {
                associatedFormName.push('designation history');
            }
        }

        let exInCandidatesQuery = organizationDbConnection(ehrTables.candidateJob)
                                    .count('Designation_Id as totalDesignationCount')
                                    .where('Designation_Id', designationId);

        if(source == 'edit'){
            exInCandidatesQuery = exInCandidatesQuery.where('Emp_Status', 'Active');
        }
        let exInCandidate = await exInCandidatesQuery.then((count)=>{return count[0].totalDesignationCount;});
                                   
        if(exInCandidate > 0) {
            associatedFormName.push('candidate job');
        }

        let exInSkillLevels = await organizationDbConnection(ehrTables.skillLevels)
                                    .count('Designation_Id as totalDesignationCount')
                                    .where('Designation_Id', designationId)
                                    .then((count)=>{return count[0].totalDesignationCount;});
        if(exInSkillLevels > 0) {
            associatedFormName.push('skill levels');
        }

        let exInLwf = await organizationDbConnection(ehrTables.labourWelfareFundDesignation)
                                    .count('Designation_Id as totalDesignationCount')
                                    .where('Designation_Id', designationId)
                                    .then((count)=>{return count[0].totalDesignationCount;});
        if(exInLwf > 0) {
            associatedFormName.push('labour welfare fund');
        }
      
        let exInExpenseTypeQuery = organizationDbConnection(ehrTables.expenseTypeGrade)
                                    .count('Designation_Id as totalDesignationCount')
                                    .where('Designation_Id', designationId);
        if(source == 'edit'){
            exInExpenseTypeQuery = exInExpenseTypeQuery.where('Status', 'Active');
        }
        let exInExpenseType = await exInExpenseTypeQuery.then((count)=>{return count[0].totalDesignationCount;});
                            
        if(exInExpenseType > 0) {
            associatedFormName.push('expense type');
        }                    
        
        if (associatedFormName.length >0){
            assForms = associatedFormName.join(',');
        }
        
        return assForms;
    }catch(designationAssCatchError){
        console.log('Error in the validateDesignationAssociated() function main catch block.',designationAssCatchError);
        throw designationAssCatchError;
    }
}

async function validateDesignationAssociatedToJobRole(organizationDbConnection, designationId) {
    try {
        // Query to fetch job roles where Designation_Ids contains the given designationId
        const records = await organizationDbConnection('job_roles as JR')
            .select('JR.Job_Role_Id','JR.Job_Role')
            .whereRaw("JSON_CONTAINS(JR.Designation_Ids, CAST(? AS CHAR))", [designationId]);

            console.log(records,"records")
        if (!records || records.length === 0) {
            return {
                isAssociated: false,
                associatedJobRoleIds: [],
            };
        }

        // Extract unique Job_Role_Ids
        const uniqueJobRoleIds = [...new Set(records.map(record => record.Job_Role_Id))];
        const uniqueJobRoles = [...new Set(records.map(record => record.Job_Role))];

        return {
            isAssociated: uniqueJobRoleIds.length > 0,
            associatedJobRoleIds: uniqueJobRoleIds,
            associatedJobRoles: uniqueJobRoles,
        };
    } catch (error) {
        console.error('Error in validateDesignationAssociatedToJobRole:', error);
        throw error;
    }
}



module.exports={
    validateDesignationAssociated,
    validateDesignationAssociatedToJobRole
};