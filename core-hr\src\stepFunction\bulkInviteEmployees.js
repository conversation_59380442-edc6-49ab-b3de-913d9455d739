//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../../common/tablealias');
const moment = require('moment');
const {
	sendNotificationToEmployees,
	sendEmailNotifications
} = require('../../common/commonfunctions');

module.exports.bulkInviteEmployees = async (args) => {
	console.log('Inside bulkInviteEmployees function');
	let organizationDbConnection;
	let orgCode = args.orgCode
	let partnerId = args.partnerId
	try {
		let databaseConnection = await commonLib.stepFunctions.getConnection(process.env.stageName, process.env.dbPrefix, process.env.dbSecretName,
			process.env.region, '', 1);
		// check whether data exist or not
		if (Object.keys(databaseConnection).length) {
			// form app manager database connection
			appmanagerDbConnection = knex(databaseConnection.AppManagerDb);
			let orgRegionDetails = await commonLib.func.getOrgRegionDetails(appmanagerDbConnection, orgCode);
			appmanagerDbConnection ? appmanagerDbConnection.destroy() : null;
			if (orgRegionDetails && Object.keys(orgRegionDetails).length > 0) {
				let additionalHeaders = await commonLib.stepFunctions.formAdditionalHeaders(orgRegionDetails);
				//Get database connection
				let connection = await commonLib.stepFunctions.getConnection(process.env.stageName, process.env.dbPrefix, process.env.dbSecretName,
					process.env.region, orgCode, 0, additionalHeaders);
				if (Object.keys(connection).length > 0) {
					organizationDbConnection = knex(connection.OrganizationDb);
					//get the orgDetails data for the aws ses template
					let orgDetails = await commonLib.func.getOrgDetails(orgCode, organizationDbConnection, 1, 86400);
					let loginEmployeeCurrentDateTime = moment.utc().format("YYYY-MM-DD HH:mm:ss");
					//get the email data params in a form of array
					let emailMobileDataParams = await sendNotificationToEmployees(organizationDbConnection, partnerId, args.employeeData, orgDetails, orgCode, loginEmployeeCurrentDateTime)
					if (emailMobileDataParams) {
						let invitedEmployeeIdArray = [];
						let failedEmployeeIdArray = [];
						//for each email data send notifications using sendEmailNotifications
						if (emailMobileDataParams.emailData.length) {
							for (let email of emailMobileDataParams.emailData) {
								let employeeId = email.Employee_Id
								delete email.Employee_Id
								let sendEmailNotificationsResponse = await sendEmailNotifications(email);
								//if notification response is success add it to the inviteEmployee
								if (sendEmailNotificationsResponse.success) {
									invitedEmployeeIdArray.push({
										'Employee_Id': employeeId,
										'Message': sendEmailNotificationsResponse.message,
										'Invitation_Status': 'Invited',
										'Invited_Time': loginEmployeeCurrentDateTime
									})
									//if failed, add it to failedEmployee
								} else {
									failedEmployeeIdArray.push({
										'Employee_Id': employeeId,
										'Message': sendEmailNotificationsResponse.message,
										'Invitation_Status': 'Pending',
										'Invited_Time': loginEmployeeCurrentDateTime
									})
								}
							}
						}
						//for the respones, add it to the faileEmployees and invitedEmployees
						if (emailMobileDataParams.responses.failedEmployeeIdArray.length || emailMobileDataParams.responses.invitedEmployeeIdArray
							.length) {
							if (emailMobileDataParams.responses.invitedEmployeeIdArray) {
								invitedEmployeeIdArray = invitedEmployeeIdArray.concat(emailMobileDataParams.responses.invitedEmployeeIdArray)
							}
							if (emailMobileDataParams.responses.failedEmployeeIdArray.length) {
								failedEmployeeIdArray.concat(
									failedEmployeeIdArray = emailMobileDataParams.responses.failedEmployeeIdArray
								)
							}
						}
						console.log('Invited Employee Data', invitedEmployeeIdArray)
						console.log('Failed Employee Data', failedEmployeeIdArray)
						await updateEmployeeInvitationStatus(organizationDbConnection, invitedEmployeeIdArray, failedEmployeeIdArray,
							loginEmployeeCurrentDateTime);

						organizationDbConnection ? organizationDbConnection.destroy() : null;
						return {
							errorCode: "",
							message: "Employee has been invited successfully"
						};
					} else {
						throw 'EBI00101'
					}
				}
			}

		}
	} catch (mainCatchError) {
		console.log('Error in bulkInviteEmployees function main block', mainCatchError);
		organizationDbConnection ? organizationDbConnection.destroy() : null;
		let errResult = commonLib.func.getError(mainCatchError, 'EBI00002');
		// return response
		throw new ApolloError(errResult.message, errResult.code);
	}
}

async function updateEmployeeInvitationStatus(organizationDbConnection, invitedEmployeeIdArray, failedEmployeeIdArray, loginEmployeeCurrentDateTime) {
	try {
		console.log("Inside updateEmployeeInvitationStatus function.")

		//Get the employee Ids to a seperate array
		let invitedEmployeeIds = [];
		for (let employee of invitedEmployeeIdArray) {
			invitedEmployeeIds.push(employee.Employee_Id)
		}

		//update the status according to the status of sending email
		if (invitedEmployeeIds.length) {
			return (
				organizationDbConnection(ehrTables.empJob)
				.update({
					Invitation_Status: 'Invited',
					Invited_Time: loginEmployeeCurrentDateTime,
					Message: 'Invited Successfully'
				})
				.whereIn("Employee_Id", invitedEmployeeIds)
				.then(() => {
							if (failedEmployeeIdArray.length) {
								for (let i = 0; i < failedEmployeeIdArray.length; i++) {
									return (
										organizationDbConnection(ehrTables.empJob)
										.update({
											"Invitation_Status": 'Pending',
											"Invited_Time": loginEmployeeCurrentDateTime,
											"Message": failedEmployeeIdArray[i].message
										})
										.where("Employee_Id", failedEmployeeIdArray[i].Employee_Id)
										.then(() => {
											console.log('Employee invitation status updated successfully.', failedEmployeeIdArray[i]
												.Employee_Id)
										})
										.catch(e => {
											console.log('Error in updateEmployeeInvitationStatus .catch block', e);
										})
									)
								}
							} else {
								console.log('Employee invitation status updated successfully.')
							}
						

				})
				.catch(e => {
					console.log('Error in updateEmployeeInvitationStatus .catch block', e);
				})
			)
		} else {
			for (let i = 0; i < failedEmployeeIdArray.length; i++) {
				return (
					organizationDbConnection(ehrTables.empJob)
					.update({
						"Invitation_Status": 'Pending',
						"Invited_Time": loginEmployeeCurrentDateTime,
						"Message": failedEmployeeIdArray[i].Message
					})
					.where("Employee_Id", failedEmployeeIdArray[i].Employee_Id)
					.then(() => {
						console.log('Employee invitation status updated successfully.', failedEmployeeIdArray[i].Employee_Id)
					})
					.catch(e => {
						console.log('Error in updateEmployeeInvitationStatus .catch block', e);
					})
				)
			}
		}
	} catch (e) {
		console.log('Error in updateEmployeeInvitationStatus main catch block', e);
	}
}