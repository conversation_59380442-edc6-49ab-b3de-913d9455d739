const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const knex = require('knex');
const { ehrTables } = require('../../../common/tablealias');
const { ApolloError } = require('apollo-server-lambda');
const { formIds } = require('../../../common/appconstants');
const { getExistingTimesheetData } = require('../../../common/commonfunctions');
module.exports.timesheetApprovalWithdraw = async (parent, args, context, info) => {
  let organizationDbConnection;
  try {
    console.log("Inside timesheetApprovalWithdraw function.");
    let employeeId = context.Employee_Id;
    organizationDbConnection = knex(context.connection.OrganizationDb);
    const formId = args.selfService === 1 ? formIds.timeSheet : formIds.timeSheetMyTeam;
    // Checking employee access rights
    let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, employeeId, '', '', 'UI', false, formId);
    console.log(checkRights,"checkRights");
    if(args.selfService === 0 && Object.keys(checkRights).length >= 0 && checkRights.Is_Manager !== 1 && checkRights.Employee_Role.toLowerCase() !== 'admin'){
      throw '_DB0114'
  }
    if(Object.keys(checkRights).length <= 0 || checkRights.Role_Update === 0){
      throw "_DB0102";
    }


      //Update the data
      await organizationDbConnection.transaction(async (trx) => {
        let existingData=await getExistingTimesheetData(organizationDbConnection,trx,args)
          if(!existingData ||(existingData.length<=0)){
            throw 'CHR0120'
          }
          if( !existingData[0].Process_Instance_Id){
            throw 'CHR0128'
          }
        await commonLib.func.deleteOldApprovalRecords(organizationDbConnection, existingData[0].Process_Instance_Id, trx);
        let updateParam = { Process_Instance_Id: null,Approval_Status: "Draft" };
        await commonLib.leaveCommonFunction.updateTableWithTrxBasedOnCondition(organizationDbConnection, ehrTables.empTimesheet, updateParam, trx, 'Request_Id', '=', args.requestId, "", "");
        

      }).then(async () => {
        let systemLogParam = {
          userIp: context.User_Ip,
          employeeId: employeeId,
          organizationDbConnection: organizationDbConnection,
          message: ` The timesheet request id ${args.requestId} is being withdrawn.`,
        };
        await commonLib.func.createSystemLogActivities(systemLogParam);

        organizationDbConnection ? organizationDbConnection.destroy() : null;
        return { errorCode: "", message: `The timesheet is being withdrawn successfully.` };
      }).catch((err) => {
        console.log("Error in timesheetApprovalWithdraw function .catch block.",err)
        throw 'CHR0121';
      })
    
  } catch (e) {
    //Destroy DB connection
    organizationDbConnection ? organizationDbConnection.destroy() : null;
    console.log("Error in timesheetApprovalWithdraw function main catch block.", e);

    let errResult = commonLib.func.getError(e, "CHR00100");
    throw new ApolloError(errResult.message, errResult.code);
  }
};
