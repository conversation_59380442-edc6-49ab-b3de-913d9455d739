'use strict';
//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;


// Function to initiate initiateLeaveClosure step function
module.exports.initiateLeaveClosure  = async(event, context) =>{
    try{
        console.log('Inside initiateLeaveClosure function',event);
        // based on event define the status
        let status='';
        if(event.status==='Open')
        {
            status='Open';
        }
        else{
            status=event.status;
        }
        let inputParams={'status':status}
        let triggerLeaveClosure= await commonLib.stepFunctions.triggerStepFunction(process.env.stateMachineArn,'leaveclosure',status,inputParams);
        console.log('Response after triggering initiating leave closure step function',triggerLeaveClosure);


        return {errorCode:'',message: 'TriggerLeaveClosure initiated successfully.'};
    }
    catch(mainCatchError){
        console.log('Error in initiateLeaveClosure function main catch block.', mainCatchError);
        let errResult = commonLib.func.getError(mainCatchError, 'CDG0165');
        return {errorCode:errResult.code,message: errResult.message};
    }
};