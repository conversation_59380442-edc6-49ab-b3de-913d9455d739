// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formName,formIds } = require('../../common/appconstants');

//fuction to list holidays based on location
let organizationDbConnection;
module.exports.retrieveProjectActivities = async (parent, args, context, info) => {
    console.log('Inside retrieveProjectActivities function');
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let loginEmployeeId = context.Employee_Id;
        let inputFormId=args.formId?args.formId:formIds.projects;
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId,'', '', 'UI',false,inputFormId);
            if (Object.keys(checkRights).length > 0 && checkRights.Role_View === 1) {
                return(
                    await organizationDbConnection(ehrTables.projectActivities + " as PA" )
                    .select('PA.Project_Id as projectId','PA.Project_Activity_Id as projectActivityId', 'PA.Activity_Id as activityId', 'AM.Description as description', 'PA.Is_Billable as isBillable', 'PA.Activity_From as activityFrom', 'PA.Activity_To as activityTo', 'AM.Activity_Name as activityName')
                    .innerJoin(ehrTables.activitiesMaster + " as AM", "AM.Activity_Id", "PA.Activity_Id")
                    .where('PA.Project_Id', args.projectId)
                    .modify(async function (queryBuilder) {
                        if(args.formId && args.weekendDate){
                            queryBuilder
                            .andWhere(function() {
                                this.where(function() {
                                        this.where('PA.Activity_From', '<=', args.weekendDate)
                                            .andWhere('PA.Activity_To', '>=', args.weekendDate);
                                    })
                                    .orWhere(function() {
                                        this.whereNull('PA.Activity_From')
                                            .orWhere('PA.Activity_From', '0000-00-00')
                                            .orWhereNull('PA.Activity_To')
                                            .orWhere('PA.Activity_To', '0000-00-00');
                                    });
                            });
                    }
                }
                    )
                    .then(async (data) => {
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        return { errorCode: "", message: "Activities associated with this project has been retrieved successfully.", activityDetails: data};
                    })
                    .catch((catchError) => {
                        let errResult = commonLib.func.getError(catchError, 'CHR0102');
                        //Destroy DB connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        throw new ApolloError(errResult.message, errResult.code);
                    })
                )
            }
            else {
                console.log('Employee do not have view access rights');
                throw '_DB0100';
            }
    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in retrieveProjectActivities function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'CHR0073');
        throw new ApolloError(errResult.message, errResult.code)
       
    }
}
