//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../../../../common/tablealias');
//Require common constant files
const { formIds } = require('../../../../common/appconstants');

//Resolver function to list the compensatory off configuration history for a configuration id
module.exports.listCompOffConfigHistory = async (parent, args, context, info) => {
    console.log('Inside listCompOffConfigHistory function');
    let organizationDbConnection;
    let errResult;
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);
        if(args.configurationId && args.configurationId>0){
            let configurationId = args.configurationId;
            let formId = formIds.compOff;
            //Get the comp off configuration history details
            return( 
            organizationDbConnection(ehrTables.compoffConfigurationHistory+' as CCH')
            .select('CCH.*', 'CEG.Group_Name', 'CGA.Custom_Group_Id', organizationDbConnection.raw("CONCAT_WS(' ',EPI.Emp_First_Name, EPI.Emp_Middle_Name, EPI.Emp_Last_Name) as Added_By_Name"))
            .leftJoin(ehrTables.empPersonalInfo + " as EPI", "EPI.Employee_Id", " CCH.Added_By")
            .leftJoin(ehrTables.customGroupAssociated + " as CGA", function () {
                this.on("CCH.Configuration_Id", "=", "CGA.Parent_Id")
                    .andOn("CGA.Form_Id", "=", formId);
            })
            .leftJoin(ehrTables.cusEmpGroup + " as CEG", "CEG.Group_Id", "CGA.Custom_Group_Id")
            .where('CCH.Configuration_Id',configurationId)
            .then(historyResult=>{
                organizationDbConnection ? organizationDbConnection.destroy() : null;//Destroy DB connection
                return { errorCode: '', message: 'Compensatory off configuration history details has been retrieved successfully.', compOffHistory: historyResult ? historyResult : [] };//Return the response
            })
            .catch(function (catchError) {
                console.log('Error in listCompOffConfigHistory .catch() block', catchError);
                errResult = commonLib.func.getError(catchError, 'SCR0108');
                organizationDbConnection ? organizationDbConnection.destroy() : null;//Destroy DB connection
                throw new ApolloError(errResult.message, errResult.code);//Return Result
            })
            )
        }else{
            console.log("Invalid input",args);
            throw '_EC0007';
        }
    }catch(error){
        console.log('Error in the listCompOffConfigHistory() function in the main catch block.',error);
        organizationDbConnection ? organizationDbConnection.destroy() : null;//Destroy DB connection
        errResult = commonLib.func.getError(error, 'SCR0003');
        throw new ApolloError(errResult.message, errResult.code);//Return Result
    }
}