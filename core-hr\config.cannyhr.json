{"securityGroupIds": ["sg-06e06ee52057fb09f"], "subnetIds": ["subnet-023ff1fb8431b273f", "subnet-09dd9cf2a9239643c"], "dbSecretName": "PROD/CANNY/PGACCESS", "region": "ap-south-1", "lambdaRole": "arn:aws:iam::378423228887:role/lambdaFullAccess", "dbPrefix": "cannyhr_", "domainName": "cannyhr", "authorizerARN": "arn:aws:lambda:ap-south-1:378423228887:function:ATS-cannyhr-firebaseauthorizer", "customDomainName": "api.cannyhr.com", "emailFrom": "<EMAIL>", "emailTo": "<EMAIL>", "sesRegion": "us-east-1", "leaveStatusDomainName": "cannyhr.com", "documentsBucketCloudFront": "http://documents.cannyhr.com", "logoBucket": "s3.logos.cannyhr.com", "mailNotificationEngine": "arn:aws:states:ap-south-1:378423228887:stateMachine:cannyhr-coreHrMailNotificationEngineStepFunction", "refreshCustomGroup": "arn:aws:states:ap-south-1:378423228887:stateMachine:cannyhr-refreshCustomEmpGroupsStepFunction", "startUpdateWeekOffDateStepFunction": "arn:aws:states:ap-south-1:378423228887:stateMachine:cannyhr-startUpdateWeekOffDateStepFunction", "bulkInviteEmployeesStepFunction": "arn:aws:states:ap-south-1:378423228887:stateMachine:cannyhr-bulkInviteEmployeesStepFunction", "leaveClosureStepFunction": "arn:aws:states:ap-south-1:378423228887:stateMachine:cannyhr-leaveClosureStepFunction", "webAddress": ".com", "systemProcessStepFunction": "arn:aws:states:ap-south-1:378423228887:stateMachine:cannyhr-systemProcessStepFunction", "supportEmail": "<EMAIL>", "resourceArnPrefix": "arn:aws:lambda:ap-south-1:378423228887:function:COREHR-cannyhr", "commonStepFunction": "arn:aws:states:ap-south-1:378423228887:stateMachine:cannyhr-commonStepFunction", "asyncSyntrumAPIStepFunction": "arn:aws:states:ap-south-1:378423228887:stateMachine:cannyhr-asyncSyntrumAPIFunction", "processAirTicketSummary": "arn:aws:states:ap-south-1:378423228887:stateMachine:cannyhr-processAirTicketSummary", "experiencePortalUrl": "https://#REPLACE_ORG_CODE_DOMAIN_NAME#/v3/candidate-portal"}