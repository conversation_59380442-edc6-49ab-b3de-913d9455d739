//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError, UserInputError } = require('apollo-server-lambda');
let moment = require('moment');
//Require table alias
const { ehrTables } = require('../../../common/tablealias');
const { formName } = require('../../../common/appconstants');

//function to update short time off settings
module.exports.updateShortTimeOffSettings = async (parent, args, context, info) => {
    console.log('Inside updateShortTimeOffSettings function');
    let organizationDbConnection;
    let validationError = {};
    try {
        let loginEmployeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, formName.shortTimeOffSettings, '', 'UI');

        //Input Validation
        if (args.Maximum_Limit > 183 || args.Maximum_Limit < 0) {
            validationError['IVE0310'] = commonLib.func.getError('', 'IVE0310').message;
        }
        if (args.Max_Short_Time_Per_Request && args.Max_Short_Time_Per_Request.length > 11) {
            validationError['IVE0311'] = commonLib.func.getError('', 'IVE0311').message;
        }
        if (args.Frequency && args.Frequency.length > 11) {
            validationError['IVE0312'] = commonLib.func.getError('', 'IVE0312').message;
        }
        if (args.Leave_Activation_Days > 365) {
            validationError['IVE0313'] = commonLib.func.getError('', 'IVE0313').message;
        }
        if (args.Maximum_Duration && !args.Maximum_Duration.length > 11) {
            validationError['IVE0314'] = commonLib.func.getError('', 'IVE0314').message;
        }
        if(args.Limit_By && args.Limit_By.toLowerCase() === 'duration' && args.Total_Duration && args.Maximum_Duration && parseFloat(args.Total_Duration) < parseFloat(args.Maximum_Duration)){
            validationError['IVE0623'] = commonLib.func.getError('', 'IVE0623').message;
        }
        if (args.Minimum_Duration && args.Minimum_Duration > 270) {
            validationError['IVE0337'] = commonLib.func.getError('', 'IVE0337').message;
        }
        if(args.Min_Short_Time_Per_Request && args.Min_Short_Time_Per_Request > args.Max_Short_Time_Per_Request){
            validationError['IVE0336'] = commonLib.func.getError('', 'IVE0336').message;
        }

        if (Object.keys(validationError).length === 0) {
            if (Object.keys(checkRights).length > 0 && checkRights.Role_Update === 1) {
                return (
                    organizationDbConnection
                        .transaction(async(trx) => {
                            return(
                            organizationDbConnection(ehrTables.shortTimeOffSettings)
                                .select('*')
                                .transacting(trx)
                                .then((settings) => {
                                    if (settings && settings.length) {
                                        return (
                                            organizationDbConnection(ehrTables.shortTimeOffSettings)
                                                .update({
                                                    Maximum_Limit: args.Maximum_Limit,
                                                    Period: args.Period,
                                                    Max_Short_Time_Per_Request: args.Max_Short_Time_Per_Request,
                                                    Min_Short_Time_Per_Request: args.Min_Short_Time_Per_Request,
                                                    Frequency: args.Frequency,
                                                    Gender: args.Gender,
                                                    Leave_Activation_Days: args.Leave_Activation_Days,
                                                    Limit_By: args.Limit_By,
                                                    Maximum_Duration: args.Maximum_Duration,
                                                    Total_Duration: args.Total_Duration,
                                                    Minimum_Duration: args.Minimum_Duration,
                                                    Enforce_Alternate_Person: args.Enforce_Alternate_Person,
                                                    Coverage_For_Alternate_Person: args.Coverage_For_Alternate_Person,
                                                    Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                                                    Updated_By: loginEmployeeId
                                                })
                                                .transacting(trx)
                                                .then((updateSettings) => {
                                                    if (updateSettings) {
                                                        return 'success'
                                                    } else {
                                                        console.log('Error while updating short time off settings')
                                                        throw 'CHS0103'
                                                    }
                                                })
                                        )
                                    } else {
                                        return (
                                            organizationDbConnection(ehrTables.shortTimeOffSettings)
                                                .insert({
                                                    Maximum_Limit: args.Maximum_Limit,
                                                    Period: args.Period,
                                                    Max_Short_Time_Per_Request: args.Max_Short_Time_Per_Request,
                                                    Min_Short_Time_Per_Request: args.Min_Short_Time_Per_Request,
                                                    Frequency: args.Frequency,
                                                    Gender: args.Gender,
                                                    Leave_Activation_Days: args.Leave_Activation_Days,
                                                    Limit_By: args.Limit_By,
                                                    Maximum_Duration: args.Maximum_Duration,
                                                    Total_Duration: args.Total_Duration,
                                                    Minimum_Duration: args.Minimum_Duration,
                                                    Enforce_Alternate_Person: args.Enforce_Alternate_Person,
                                                    Coverage_For_Alternate_Person: args.Coverage_For_Alternate_Person,
                                                    Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                                                    Updated_By: loginEmployeeId
                                                })
                                                .transacting(trx)
                                                .then((updateSettings) => {
                                                    if (updateSettings) {
                                                        return 'success'
                                                    } else {
                                                        console.log('Error while inserting short time off settings')
                                                        throw 'CHS0103'
                                                    }
                                                })
                                        )
                                    }
                                })
                            )
                        })
                        .then((data) => {
                            if (data) {
                                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                return { errorCode: "", message: "Short time off settings has been updated successfully." };
                            } else {
                                throw 'CHS0103'
                            }
                        })
                        .catch((catchError) => {
                            console.log('Error in updateShortTimeOffSettings .catch() block', catchError);
                            let errResult = commonLib.func.getError(catchError, 'CHS0103');
                            //Destroy DB connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            //Return error response
                            throw new ApolloError(errResult.message, errResult.code);
                        })
                )
            } else {
                console.log('No rights to update the short time off settings');
                throw '_DB0111';
            }
        } else {
            throw 'IVE0000';
        }
    } catch (mainCatchError) {
        console.log('Error in updateShortTimeOffSettings function main block', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        if (mainCatchError === 'IVE0000') {
            let errResult = commonLib.func.getError('', 'IVE0000');
            console.log('Validation error in updateShortTimeOffSettings function - ', validationError);
            // return response
            throw new UserInputError(errResult.message, { validationError: validationError });
        } else {
            errResult = commonLib.func.getError(mainCatchError, 'CHS0003');
            // return response
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
}