//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../../../common/tablealias');
//Require moment
const moment = require('moment-timezone');
//Require constants
const { formName } = require('../../../common/appconstants');
const { customGroupRefresh } = require('../../../common/commonfunctions')
//Validation Fail data
let validationFails = []

module.exports.importEmployeeData = async (parent, args, context, info) => {
  console.log('Inside importEmployeeData() function.');
  let organizationDbConnection;
  let errResult;
  let asyncRequest = args.isAsync;
  try {
    let loginEmployeeId = context.Employee_Id;
    organizationDbConnection = knex(context.connection.OrganizationDb);
    let adminRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, formName.admin, '', 'UI');
    let employeeAdminRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, formName.teamSummary, '', 'UI');
    if ((adminRights && adminRights.Role_Update === 1) && (employeeAdminRights && employeeAdminRights.Role_Update === 1)) {
      let asyncTransaction = organizationDbConnection.transaction(async (trx) => {
        await typeOfImport(args, organizationDbConnection, loginEmployeeId, trx, context)
          .then((data) => {
            if (!data) {
              throw 'EDM0102';
            } else {
              console.log('Employee details importing operation completed successfully.');
            }
          })
          .catch((err) => {
            console.log('Error in the importEmployeeData() function .catch block. ', err);
            //Destroy DB connection
            organizationDbConnection ? organizationDbConnection.destroy() : null;
            errResult = commonLib.func.getError(err, 'EDM0102');
            throw new ApolloError(errResult.message, errResult.code);//return response
          })
      })
      if (asyncRequest) {
        asyncTransaction;
        console.log('Employee details import initiated successfully. (Async)');
        return { errorCode: "", message: "Employee details import initiated successfully." };
      } else {
        await asyncTransaction;
        console.log('Employee details imported successfully. (Sync)');
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        return { errorCode: "", message: "Employee details imported successfully.", validationFails };
      }
    }
    else {
      console.log('User does not have access to import employee details');
      throw ('_DB0109');
    }
  } catch (err) {
    console.log('Error in the importEmployeeData() function main catch block. ', err);
    //Destroy DB connection
    organizationDbConnection ? organizationDbConnection.destroy() : null;
    errResult = commonLib.func.getError(err, 'EDM0002');
    throw new ApolloError(errResult.message, errResult.code);//return response
  }
};


//Function to differentiate between the type of import
async function typeOfImport(args, organizationDbConnection, loginEmployeeId, trx, context) {
  try {
    let insertUpdateData = JSON.parse(args.employeeData)
    let query;
    let table;
    let historyTable;
    let onlyUpdateData = [];
    let historyData = [];
    let timeStampLogData = [];

    //history and table selection based on typeOfEmployeeDetails
    if (args.typeOfEmployeeDetails === 'Employee Job Details') {
      table = ehrTables.empJob;
      historyTable = ehrTables.empJobAuditHistory
    } else if (args.typeOfEmployeeDetails === 'Employee Personal Details') {
      table = ehrTables.empPersonalInfo;
    } else {
      throw false;
    }

    //still dev in progress (Add Functionality)
    if (args.typeOfImport === 'add') {
      query = await organizationDbConnection(table)
        .insert(insertUpdateData)
        .transacting(trx)
    }
    else if (args.typeOfImport === 'update') {

      //Get the list of User_Defined_EmpId
      let userDefinedIds = insertUpdateData.map((el) => {
        return el.Employee_Id;
      })

      //Get the Employee_Ids from the User_Defined_EmpId
      await organizationDbConnection(ehrTables.empJob)
        .select('User_Defined_EmpId', 'Employee_Id')
        .whereIn('User_Defined_EmpId', userDefinedIds)
        .transacting(trx)
        .then((data) => {
          if (data && data.length) {

            for (let i = 0; i < insertUpdateData.length; i++) {
              const d = insertUpdateData[i];
              const found = data.find(rd => rd.User_Defined_EmpId === d.Employee_Id);
              if (!found) {
                validationFails.push({ fieldName: 'Employee_Id', field: d.Employee_Id, reason: 'Employee Id does not exist' });
              } else {
                insertUpdateData[i].Employee_Id = found.Employee_Id;
              }
            }

          } else {
            throw false;
          }
        })

      //get the employeeIds from insertUpdateData
      let employeeIds = insertUpdateData.map(el => el.Employee_Id)
      //get the history logs based on employee id
      query = await organizationDbConnection(historyTable)
        .select('*')
        .transacting(trx)
        .whereIn('Employee_Id', employeeIds)
        .then(async (data) => {
          let employeeDetailsHistory = data && data.length ? JSON.parse(JSON.stringify((data))) : []
          await organizationDbConnection(table)
            .select('*')
            .transacting(trx)
            .whereIn('Employee_Id', employeeIds)
            .then(async (data) => {
              const employeeDetails = Array.from(data);
              const dateUpdate = ['Work_Schedule', 'Designation_Id', 'Department_Id', 'Location_Id', 'EmpType_Id', 'Manager_Id', 'Business_Unit_Id', 'Salary_Grade_Id'];
              for (const updateData of insertUpdateData) {
                const employee = employeeDetails.find(emp => emp.Employee_Id === updateData.Employee_Id);
                if (employee) {
                  const differentData = compareKeyValuePairs(updateData, employee);
                  //Validate the effective dates
                  let isEffectiveDateValid = await validateEffectiveDates(organizationDbConnection, employee.Employee_Id, context.Org_Code, differentData, updateData)
                  if (isEffectiveDateValid) {
                    for (const key of differentData) {
                      const objectHistory = {
                        Employee_Id: employee.Employee_Id,
                        [key]: employee[key] || null,
                        ['New_' + key]: updateData[key],
                        Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                        Updated_By: loginEmployeeId
                      };
                      const dataUpdate = {
                        Employee_Id: employee.Employee_Id,
                        [key]: updateData[key]
                      }

                      // Only add effective date if this field supports it
                      if (dateUpdate.includes(key)) {
                        dataUpdate[key + '_Effective_Date'] = updateData[`${key}_End_Date`];
                      }
                      if (dateUpdate.includes(key)) {
                        let employeeHistory = null;

                        employeeDetailsHistory.forEach(item => {
                          if (item.Employee_Id === employee.Employee_Id && item[`${key}_End_Date`] && moment(item[`${key}_End_Date`]).isValid()) {
                            if (!employeeHistory || moment(item[`${key}_End_Date`]).isAfter(moment(employeeHistory[`${key}_End_Date`]))) {
                              employeeHistory = item;
                            }
                          }
                        });
                        objectHistory[`${key}_Start_Date`] = employeeHistory && employeeHistory[`${key}_End_Date`] ? moment(employeeHistory[`${key}_End_Date`]).add(1, 'day').format('YYYY-MM-DD') : employee.Date_Of_Join;
                        objectHistory[`${key}_End_Date`] = updateData[`${key}_End_Date`] ? moment(updateData[`${key}_End_Date`]).subtract(1, 'day').format('YYYY-MM-DD') : moment.utc().format("YYYY-MM-DD HH:mm:ss");
                      }
                      historyData.push(objectHistory);
                      timeStampLogData.push({
                        Employee_Id: employee.Employee_Id,
                        Action: 'Update',
                        Log_Timestamp: moment().format('YYYY-MM-DD HH:mm:ss')
                      });
                      onlyUpdateData.push(dataUpdate);
                    }
                  }
                }
              }

              //Formatting the insert data into single object for single employee
              historyData = Object.values(historyData.reduce((result, obj) => {
                const employeeId = obj.Employee_Id;
                if (!result[employeeId]) {
                  result[employeeId] = { Employee_Id: employeeId };
                }
                for (const key in obj) {
                  if (obj.hasOwnProperty(key) && key !== 'Employee_Id') {
                    if (!result[employeeId].hasOwnProperty(key)) {
                      result[employeeId][key] = obj[key];
                    } else if (key.endsWith('_Start_Date') || key.endsWith('_End_Date')) {
                      if (result[employeeId][key] !== obj[key]) {
                        result[employeeId][key] = null; // Set to null if there are conflicting values
                      }
                    }
                  }
                }
                return result;
              }, {}));

              //Validation 
              if (historyData.length) {
                let validateEmails = [];
                for (let el of historyData) {
                  //Check if the user has changed employeeType and if the Benefits_Applicable is different from previous
                  if (el.New_EmpType_Id || el.New_Location_Id) {
                    await updateSalaryRecalculation(organizationDbConnection, el, trx);
                  }
                  //Validate same email already exists
                  if (el.Emp_Email) {
                    let newData = onlyUpdateData.find(el2 => el2.Employee_Id === el.Employee_Id)
                    validateEmails.push({
                      Employee_Id: el.Employee_Id,
                      Emp_Email: newData.Emp_Email
                    })
                  }
                }
                //get email array
                let emailArray = validateEmails.map(el => el.Emp_Email)
                //check if the email array has duplicate
                if (validateEmails && validateEmails.length) {
                  await organizationDbConnection(ehrTables.empJob)
                    .select('Emp_Email')
                    .transacting(trx)
                    .where('Emp_Status', 'Active')
                    .whereIn('Emp_Email', emailArray)
                    .then((data) => {
                      if (data && data.length) {
                        //Get the duplicate email
                        for (let i = 0; i < validateEmails.length; i++) {
                          for (let j = 0; j < data.length; j++) {
                            if (validateEmails[i].Emp_Email && validateEmails[i].Emp_Email === data[j].Emp_Email) {
                              validationFails.push({
                                fieldName: 'Email',
                                field: validateEmails[i].Emp_Email,
                                reason: 'Same Email Exists'
                              })
                              validationFails = validationFails.filter((obj, index, self) => {
                                return index === self.findIndex((o) => o.field === obj.field);
                              });
                              //Update onlyUpdateData
                              onlyUpdateData = onlyUpdateData.filter(el => el.Employee_Id !== validateEmails[i].Employee_Id)
                              //Update historyData
                              historyData = historyData.filter(el => el.Employee_Id !== validateEmails[i].Employee_Id)
                              //Update timeStampLogData
                              timeStampLogData = timeStampLogData.filter(el => el.Employee_Id !== validateEmails[i].Employee_Id)
                            }
                          }
                        }
                        console.log('Duplicate Email found', validationFails);
                      }
                    })
                }
              }

              //remove duplicates from onlyUpdateData
              onlyUpdateData = onlyUpdateData.filter((item, index) => {
                return index === onlyUpdateData.findIndex(obj => {
                  return JSON.stringify(obj) === JSON.stringify(item);
                });
              });

              // Remove keys ending with "_End_Date" from each object in the array
              // Only transform to "_Effective_Date" for fields that support effective dates
              onlyUpdateData.forEach(function (obj) {
                Object.keys(obj).forEach(function (key) {
                  if (key.endsWith("_End_Date")) {
                    var baseKey = key.replace("_End_Date", "");
                    // Only create _Effective_Date if this field supports it
                    if (dateUpdate.includes(baseKey)) {
                      var newKey = key.replace("_End_Date", "_Effective_Date");
                      obj[newKey] = obj[key];
                    }
                    delete obj[key];
                  }
                });
              });
              let employeeIdsForCustomGroup = onlyUpdateData.map((el) => el.Employee_Id)

              // Update of the data

              if (onlyUpdateData.length) {
                await organizationDbConnection.transaction(async (trx) => {
                  await Promise.all(
                    onlyUpdateData.map(async (details) => {
                      const employeeId = details.Employee_Id;
                      const updateData = { ...details };
                      delete updateData.Employee_Id;

                      // Filter out any keys that don't exist in the database to prevent binding errors
                      const validUpdateData = {};
                      Object.keys(updateData).forEach((key) => {
                        // Only include the key if it's a valid column or a valid effective date column
                        if (key.endsWith('_Effective_Date')) {
                          const baseKey = key.replace('_Effective_Date', '');
                          if (dateUpdate.includes(baseKey)) {
                            validUpdateData[key] = updateData[key];
                          }
                        } else {
                          // For non-effective date columns, include them (they should exist in the table)
                          validUpdateData[key] = updateData[key];
                        }
                      });

                      if (Object.keys(validUpdateData).length > 0) {
                        const updateQuery = organizationDbConnection(table)
                          .where({ Employee_Id: employeeId });

                        Object.keys(validUpdateData).forEach((key) => {
                          updateQuery.update({
                            [key]: organizationDbConnection.raw(`CASE WHEN Employee_Id = ? THEN ? ELSE ?? END`, [employeeId, details[key], key])
                          });
                        });

                        await updateQuery.transacting(trx);
                      }
                    })
                  );

                  console.log(`Data was updated for ${onlyUpdateData.length} employees`);
                }).catch((error) => {
                  console.error(`Error while updating user data`, error);
                  throw error;
                });
              }




              //Custom Group Refresh
              if (employeeIdsForCustomGroup.length) {
                await customGroupRefresh(employeeIdsForCustomGroup, loginEmployeeId, context)
              }
              //Form historyInsertData
              let historyInsertData = []
              for (let history of historyData) {
                for (let employee of employeeDetails) {
                  if (history.Employee_Id === employee.Employee_Id) {
                    historyInsertData.push(mergeObjects(employee, history, onlyUpdateData))
                  }
                }
              }

              // Parallel execution of insertions
              await Promise.all([
                //Insertion of history logs
                historyInsertData.length && organizationDbConnection(historyTable)
                  .insert(historyInsertData)
                  .transacting(trx)
                  .then(() => console.log('Employee history logs were inserted successfully'))
                  .catch((err) => {
                    console.log('Error in inserting history logs', err);
                    throw err;
                  }),

                //Insertion of employee log
                timeStampLogData.length && organizationDbConnection(ehrTables.employeeLog)
                  .insert(timeStampLogData)
                  .transacting(trx)
                  .then(() => console.log('Employee log was added'))
                  .catch((err) => {
                    console.log('Error in inserting employee logs', err);
                    throw err;
                  })
              ]);
            })
        })

    }
    return true;
  } catch (err) {
    console.log('Error in typeOfImport function()', err)
    return false;
  }
}

//Function returns an array of keys that have different values in the two objects and avoids end date
function compareKeyValuePairs(a, b) {
  const diff = {};
  for (const key in a) {
    if (a.hasOwnProperty(key) && b.hasOwnProperty(key) && !key.endsWith("_End_Date") && a[key] !== b[key]) {
      diff[key] = { a: a[key], b: b[key] };
    }
  }
  return Object.keys(diff);
}


function mergeObjects(obj1, obj2, onlyUpdateData) {
  const mergedObject = { ...obj1 };

  for (const prop in obj2) {
    mergedObject[prop] = obj2[prop];
  }

  for (const prop in mergedObject) {
    if (prop.endsWith("_Effective_Date") || prop === "Added_On" || prop === "Added_By") {
      if (prop.endsWith("_Effective_Date")) {
        const startDateProp = prop.replace("_Effective_Date", "_Start_Date");
        mergedObject[startDateProp] = mergedObject[prop];
      }
      delete mergedObject[prop];
    }
  }

  return mergedObject;
}

async function validateEffectiveDates(organizationDbConnection, employeeId, orgCode, differentKey, updateData) {
  try {
    return (
      organizationDbConnection(ehrTables.empJob + " as EJ")
        .select(organizationDbConnection.raw("MAX(STR_TO_DATE(SP.Salary_Month,'%m,%Y')) as maxSalaryMonthYear"), 'EJ.Date_Of_Join', 'EJ.Employee_Id', "EJ.Business_Unit_Id_Effective_Date", "EJ.Designation_Id_Effective_Date", "EJ.Department_Id_Effective_Date", "EJ.Location_Id_Effective_Date", "EJ.Manager_Id_Effective_Date", "EJ.EmpType_Id_Effective_Date", "EJ.Work_Schedule_Effective_Date", "EJ.Salary_Grade_Id_Effective_Date")
        .leftJoin(ehrTables.salaryPayslip + " as SP", "EJ.Employee_Id", "SP.Employee_Id")
        .where('EJ.Employee_Id', employeeId)
        .groupBy('EJ.Employee_Id')
        .then(async (data) => {
          if (data && data.length) {
            data = data[0]
            let maxPayslipDate = null
            let currentDate = moment().format('YYYY-MM-DD')
            let maxPayDate = data.maxSalaryMonthYear
            if (maxPayDate) {
              const month = maxPayDate.slice(5, 7);
              const year = maxPayDate.slice(0, 4);
              const { Last_SalaryDate } = await commonLib.func.getSalaryDay(
                orgCode,
                organizationDbConnection,
                month,
                year
              );
              maxPayslipDate = Last_SalaryDate
            }
            //Loop through the differentKeys
            for (let el of differentKey) {
              if (!data[el + '_Effective_Date'] || data[el + '_Effective_Date'] === '0000-00-00') {
                if (data.Date_Of_Join) {
                  data[el + '_Effective_Date'] = moment(data.Date_Of_Join).subtract(1, 'days')
                }
              }
              let effectiveDate = moment(data[el + '_Effective_Date']).add(1, 'days')
              let payslipDate = maxPayslipDate ? moment(maxPayslipDate) : moment(data.Date_Of_Join)

              let minDate = moment.max(effectiveDate, payslipDate).format('YYYY-MM-DD')
              let maxDate = currentDate

              //Check if min date and max date are correct else keep the min date as max date
              if (!moment(maxDate).isAfter(minDate)) {
                maxDate = minDate;
              }

              if (updateData[el + '_Effective_Date'] && !moment(updateData[el + '_Effective_Date']).isBetween(minDate, maxDate, null, "[]")) {
                validationFails.push({
                  fieldName: el + '_End_Date',
                  field: updateData[el + '_Effective_Date'],
                  reason: 'Invalid Effective Date'
                })
                return false
              }
            }
          }
          return true
        })
    )
  } catch (err) {
    console.log('Error in validateEffectiveDates catch', err)
    throw err
  }
}

async function updateSalaryRecalculation(organizationDbConnection, historyData, trx) {
  try {
    const { Employee_Id, EmpType_Id, New_EmpType_Id, Location_Id, New_Location_Id } = historyData;

    let salaryUpdated = false; // Flag to track if salary recalculation is already updated

    // Function to update salary recalculation
    const updateSalaryRecalc = async () => {
      if (!salaryUpdated) {
        try {
          await organizationDbConnection(ehrTables.salaryDetails)
            .transacting(trx)
            .update('Salary_Recalc', 1)
            .where('Employee_Id', Employee_Id);

          salaryUpdated = true; // Mark as updated
        } catch (err) {
          console.error('Error while updating salary recalculation for employeeId', Employee_Id, err);
          throw err;
        }
      }
    };

    // Check Employee Type changes
    if (New_EmpType_Id && !salaryUpdated) {
      const empTypeData = await organizationDbConnection(ehrTables.employeeType)
        .select('EmpType_Id', 'Benefits_Applicable')
        .transacting(trx)
        .whereIn('EmpType_Id', [EmpType_Id, New_EmpType_Id]);

      if (empTypeData && empTypeData.length > 1 && empTypeData[0].Benefits_Applicable !== empTypeData[1].Benefits_Applicable) {
        await updateSalaryRecalc();
      }
    }

    // Check Location changes
    if (New_Location_Id && !salaryUpdated) {
      const locationData = await organizationDbConnection(ehrTables.allowances)
        .select('Location_Id')
        .whereIn('Location_Id', [Location_Id, New_Location_Id])
        .where('Allowance_Status', 'Active')
        .transacting(trx);

      if (locationData && locationData.length) {
        await updateSalaryRecalc();
      }
    }

  } catch (err) {
    console.log('Error in updateSalaryRecalculation function', err)
    throw err
  }
}