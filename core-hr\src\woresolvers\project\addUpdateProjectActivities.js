// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../common/tablealias');
//Require apollo server to return error message
const { UserInputError, ApolloError } = require('apollo-server-lambda');
// require common constant files
const {formIds } = require('../../../common/appconstants');
//Require validation function
const { validateCommonRuleInput } = require('../../../common/inputValidations');
const moment = require('moment');

let organizationDbConnection;
module.exports.addUpdateProjectActivities = async (parent, args, context, info) => {
    console.log('Inside addUpdateProjectActivities function');
    let validationError = {};
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);
        const activityId = args.activityId;
        const fieldValidations = {
            activityName: "IVE0417",
        };
        if(args.description != null){
            fieldValidations.description = "IVE0415"
        }
        validationError = await validateCommonRuleInput(args, fieldValidations);
        let loginEmployeeId = context.Employee_Id;
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, null, '', 'UI', false, formIds.projects);
           if (Object.keys(checkRights).length > 0 && ((activityId === 0 && checkRights.Role_Add === 1) || (activityId > 0 && checkRights.Role_Update === 1))) {
                    if(Object.keys(validationError).length == 0){
                        if(activityId){
                            return (
                                organizationDbConnection(ehrTables.activitiesMaster)
                                .update({
                                    Activity_Name: args.activityName,
                                    Is_Billable: args.isBillable,
                                    Description: args.description,
                                    Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                                    Updated_By: loginEmployeeId
                                })
                                .where('Activity_Id', args.activityId)
                                    .then(async(data) => {
                                        if(data){
                                            let systemLogParam = {
                                                userIp: context.User_Ip,
                                                employeeId: loginEmployeeId,
                                                organizationDbConnection: organizationDbConnection,
                                                message: `Activity Name ${args.activityName} was updated by ${loginEmployeeId}`
                                            };
                                            await commonLib.func.createSystemLogActivities(systemLogParam);
                                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                                            return { errorCode: "", message: "Project activity has been updated successfully." };
                                        }
                                        else {
                                            throw 'CHR0103'
                                        }
                                    })
                                    .catch((catchError) => {
                                        console.log('Error in UpdateProjectActivities .catch() block', catchError);
                                        let errorCode = catchError.code === 'ER_DUP_ENTRY' ? 'CHR0078' : 'CHR0104';
                                        //Destroy DB connection
                                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                                        let errResult = commonLib.func.getError(errorCode, 'CHR0104');
                                        throw new ApolloError(errResult.message, errResult.code)

                                    })
                            )
                        }
                        else{
                            return (
                                organizationDbConnection(ehrTables.activitiesMaster)
                                    .insert({
                                        Activity_Name: args.activityName,
                                        Is_Billable: args.isBillable,
                                        Description: args.description,
                                        Added_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                                        Added_By: loginEmployeeId
                                    })
                                    .then(async(data) => {
                                        if(data){
                                            let systemLogParam = {
                                                userIp: context.User_Ip,
                                                employeeId: loginEmployeeId,
                                                organizationDbConnection: organizationDbConnection,
                                                message: `Activity Name ${args.activityName} was added by ${loginEmployeeId}`
                                            };
                                            await commonLib.func.createSystemLogActivities(systemLogParam);
                                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                                            return { errorCode: "", message: "Project activity has been added successfully." };
                                        }
                                        else {
                                            throw 'CHR0105'
                                        }
                                    })
                                    .catch((catchError) => {
                                        console.log('Error in addProjectActivities .catch() block', catchError);
                                        let errorCode = catchError.code === 'ER_DUP_ENTRY' ? 'CHR0078' : 'CHR0106';
                                        //Destroy DB connection
                                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                                        let errResult = commonLib.func.getError(errorCode, 'CHR0106');
                                        throw new ApolloError(errResult.message, errResult.code)
                                    })
                            ) 
                        }
                    }
                    else{
                        throw 'IVE0000';
                    }
            }
            else {
                if (activityId) {
                    console.log("The employee does not have edit access.");
                    throw '_DB0102';
                } else {
                    console.log("The employee does not have add access.");
                    throw '_DB0101';
                }
            }
    }
    catch (mainCatchError) {
        console.log('Error in addUpdateProjectActivities function main catch block.', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        if (mainCatchError === 'IVE0000') {
            let errResult = commonLib.func.getError('', 'IVE0000');
            console.log('Validation error in addUpdateProjectActivities function - ', validationError);
            // return response
            throw new UserInputError(errResult.message, { validationError: validationError });
        }
        else{
            let errResult = commonLib.func.getError(mainCatchError, 'CHR0074');
            throw new ApolloError(errResult.message, errResult.code)
        }
       
    }
}







