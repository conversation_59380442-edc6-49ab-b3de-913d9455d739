// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib
// require knex
const knex = require('knex')
// require common table alias
const { ehrTables } = require('../../../common/tablealias')
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda')
// require common constant files
const { formIds } = require('../../../common/appconstants')

let organizationDbConnection

module.exports.retrieveAirTicketSettings = async (
  parent,
  args,
  context,
  info
) => {
  console.log('Inside retrieveAirTicketSettings function')
  try {
    organizationDbConnection = knex(context.connection.OrganizationDb)
    let loginEmployeeId = context.Employee_Id
    let accessFormId = args.formId

    // Check employee access rights
    let checkRights = await commonLib.func.checkEmployeeAccessRights(
      organizationDbConnection,
      loginEmployeeId,
      null,
      '',
      'UI',
      false,
      accessFormId
    )

    if (
      Object.keys(checkRights).length > 0 &&
      checkRights.Role_View === 1 &&
      checkRights.Employee_Role.toLowerCase() === 'admin'
    ) {
      return await organizationDbConnection(
        ehrTables.airTicketSettings + ' as ATS'
      )
        .select(
          'ATS.Air_Ticket_Setting_Id',
          'ATS.Destination_City',
          'ATS.Destination_Country',
          'ATS.Air_Ticketing_Category',
          'ATS.Status',
         'ATS.Infant_Amount',
          'ATS.Child_Amount',
          'ATS.Adult_Amount',
          'ATS.Added_On',
          'ATS.Updated_On',
          organizationDbConnection.raw(
            'CONCAT_WS(" ", EPI.Emp_First_Name, EPI.Emp_Middle_Name, EPI.Emp_Last_Name) as Added_By'
          ),
          organizationDbConnection.raw(
            'CONCAT_WS(" ", EPI2.Emp_First_Name, EPI2.Emp_Middle_Name, EPI2.Emp_Last_Name) as Updated_By'
          )
        )
        .leftJoin(
          ehrTables.empPersonalInfo + ' as EPI',
          'EPI.Employee_Id',
          'ATS.Added_By'
        )
        .leftJoin(
          ehrTables.empPersonalInfo + ' as EPI2',
          'EPI2.Employee_Id',
          'ATS.Updated_By'
        )
        .then(async (data) => {
          organizationDbConnection ? organizationDbConnection.destroy() : null
          return {
            errorCode: '',
            message: 'Air ticket settings have been retrieved successfully.',
            airTicketSettingData: data
          }
        })
        .catch((catchError) => {
          organizationDbConnection ? organizationDbConnection.destroy() : null
          throw catchError
        })
    } else {
      if (checkRights && checkRights.Employee_Role.toLowerCase() !== 'admin') {
        throw '_DB0109'
      } else {
        console.log('Employee does not have view/admin access rights')
        throw '_DB0100'
      }
    }
  } catch (e) {
    // Destroy DB connection
    organizationDbConnection ? organizationDbConnection.destroy() : null
    console.log(
      'Error in retrieveAirTicketSettings function main catch block.',
      e
    )
    let errResult = commonLib.func.getError(e, 'ATT0001')
    throw new ApolloError(errResult.message, errResult.code)
  }
}
