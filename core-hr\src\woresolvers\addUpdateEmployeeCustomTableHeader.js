// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../common/tablealias');
// require apollo server to return error message
const { ApolloError, UserInputError } = require('apollo-server-lambda');
// require common constant files
const { systemLogs } = require('../../common/appconstants');
const moment = require('moment');

/**
 * GraphQL mutation resolver to add or update custom employee table header
 * @param {Object} parent - Parent resolver
 * @param {Object} args - Input arguments containing formId, employeeCustomHeader array, and optional screenType
 * @param {Object} context - GraphQL context containing employee info and database connection
 * @param {Object} info - GraphQL info object
 * @returns {Object} Response object with errorCode and message
 */

let organizationDbConnection;
let inputValidationError = {};

module.exports.addUpdateEmployeeCustomTableHeader = async (parent, args, context, info) => {
    try {
        console.log('Inside addUpdateEmployeeCustomTableHeader function');
        
        // Get login employee ID from context
        const loginEmployeeId = context.Employee_Id;
        
        // Establish database connection
        organizationDbConnection = knex(context.connection.OrganizationDb);

        // Input validation for formId - check if formId is not null and greater than 0
        if (!args.formId || args.formId <= 0) {
            inputValidationError['IVE0000'] = 'Please enter a valid Form ID.';
        }

        // Validate employeeCustomHeader array elements if provided
        if (args.employeeCustomHeader && Array.isArray(args.employeeCustomHeader)) {
            for (let i = 0; i < args.employeeCustomHeader.length; i++) {
                const headerValue = args.employeeCustomHeader[i];
                if (headerValue && typeof headerValue === 'string' && headerValue.trim().length === 0) {
                    inputValidationError['IVE000'+(i+1)] = 'Employee custom data table header values must not be empty.';
                }
            }
        }

        if(Object.keys(inputValidationError).length > 0){
            throw 'IVE0000';
        }

        // Check if record exists for upsert logic
        const existingRecord = await organizationDbConnection(ehrTables.customEmployeeTableHeaders)
            .select('Employee_Id')
            .where('Employee_Id', loginEmployeeId)
            .where('Form_Id', args.formId)
            .modify(queryBuilder => {
                // Add screenType condition if provided
                if (args.screenType) {
                    queryBuilder.where('Screen_Type', args.screenType);
                }
            })
            .first();

        // Convert array to JSON string for storage
        const employeeCustomHeaderJson = args.employeeCustomHeader && Array.isArray(args.employeeCustomHeader) ? JSON.stringify(args.employeeCustomHeader) : '[]';

        let isUpdate = false;
        let operationMessage = '';

        // Use knex transaction for data consistency
        await organizationDbConnection.transaction(async (trx) => {
            if (existingRecord) {
                // Update existing record
                await organizationDbConnection(ehrTables.customEmployeeTableHeaders)
                    .update({
                        Employee_Custom_Header: employeeCustomHeaderJson,
                        Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss')
                    })
                    .where('Employee_Id', loginEmployeeId)
                    .where('Form_Id', args.formId)
                    .modify(queryBuilder => {
                        if (args.screenType) {
                            queryBuilder.where('Screen_Type', args.screenType);
                        } else {
                            queryBuilder.whereNull('Screen_Type');
                        }
                    })
                    .transacting(trx);

                isUpdate = true;
                operationMessage = 'updated';
            } else {
                // Insert new record
                await organizationDbConnection(ehrTables.customEmployeeTableHeaders)
                    .insert({
                        Employee_Id: loginEmployeeId,
                        Form_Id: args.formId,
                        Screen_Type: args.screenType || null,
                        Employee_Custom_Header: employeeCustomHeaderJson,
                        Added_On: moment().utc().format('YYYY-MM-DD HH:mm:ss')
                    })
                    .transacting(trx);

                isUpdate = false;
                operationMessage = 'added';
            }
        });

        // Add System Log for CREATE/UPDATE operations
        let systemLogParams = {
            action: `${isUpdate ? systemLogs.roleUpdate : systemLogs.roleAdd}`,
            userIp: context.User_Ip,
            employeeId: loginEmployeeId,
            formId: args.formId,
            isEmployeeTimeZone: 0,
            uniqueId: loginEmployeeId,
            organizationDbConnection: organizationDbConnection,
            message: `Data table header values have been successfully updated.`,
            changedData: args
        };

        // Call function to add the system log
        await commonLib.func.createSystemLogActivities(systemLogParams);

        // Return success response
        return {
            errorCode: "",
            message: `Data table header values have been successfully updated.`
        };

    } catch (error) {
        console.log('Error in addUpdateEmployeeCustomTableHeader function:', error);
        
        // Handle validation errors
        if (error === 'IVE0000') {
            const errResult = commonLib.func.getError('', 'IVE0000');
            throw new UserInputError(errResult.message, { validationError: inputValidationError });
        }
        // Handle all other errors
        const errResult = commonLib.func.getError(error, 'CHR00118');
        throw new ApolloError(errResult.message, errResult.code);
        
    } finally {
        // Close database connection and cleanup
        if (organizationDbConnection) {
            organizationDbConnection.destroy();
        }
        // Reset variables to prevent memory leaks
        inputValidationError = {};
    }
};
