//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib
//Require knex to make DB connection
const knex = require('knex')
//Require apollo server to return error message
const { UserInputError, ApolloError } = require('apollo-server-lambda')
//Require table alias
const { ehrTables } = require('../../../common/tablealias')
//Require constants
const { formName, systemLogs } = require('../../../common/appconstants')
//Require validation functions
const { mobileNumberValidation } = require('../../../common/commonvalidation')
const {
  emailValidation
} = require('@cksiva09/hrapp-corelib/common/commonValidation')

//Update the user account details
module.exports.updateUserAccountDetails = async (
  parent,
  args,
  context,
  info
) => {
  console.log('Inside updateUserAccountDetails() function.')
  let organizationDbConnection
  let errResult
  let validationError = {}
  let systemLogParam
  try {
    let loginEmployeeId = context.Employee_Id
    let updateInvitationStatus = args.updateInvitationStatus
    organizationDbConnection = knex(context.connection.OrganizationDb)
    //Checking rights for the updation of user account details
    let checkRights = await commonLib.func.checkEmployeeAccessRights(
      organizationDbConnection,
      loginEmployeeId,
      formName.userAccounts,
      '',
      'UI'
    )
    let mobileNumber = args.mobileNumber.toString()
    //Check update rights exist or not
    if (Object.keys(checkRights).length > 0 && checkRights.Role_Update === 1) {
      //Validation for the user accounts
      if (mobileNumber && args.mobileNumberCountryCode) {
        let mobileValid = mobileNumberValidation(
          mobileNumber,
          args.mobileNumberCountryCode
        )
        if (!mobileValid) {
          validationError['IVE0296'] = commonLib.func.getError(
            '',
            'IVE0296'
          ).message
        }
      }
      if (args.email) {
        let emailValid = emailValidation(args.email)
        if (!emailValid) {
          validationError['IVE0052'] = commonLib.func.getError(
            '',
            'IVE0052'
          ).message
        }
      }

      //Check validation error exist or not
      if (Object.keys(validationError).length === 0) {
        return organizationDbConnection
          .transaction(async (trx) => {
            return organizationDbConnection(ehrTables.empJob)
              .count('EJ.Employee_Id as employeeIdCount')
              .leftJoin(
                ehrTables.contactDetails + ' as CD',
                'CD.Employee_Id',
                'EJ.Employee_Id'
              )
              .from(ehrTables.empJob + ' as EJ')
              .where(function () {
                if (args.email && args.type === "email") {
                  this.andWhere('EJ.Emp_Email', args.email)
                }
                if (mobileNumber && args.type === "mobile") {
                  this.andWhere('CD.Mobile_No', mobileNumber).andWhere(
                    'CD.Mobile_No_Country_Code',
                    args.mobileNumberCountryCode
                  )
                }
                this.andWhere('EJ.Emp_Status', 'Active')
              })
              .andWhereNot('EJ.Employee_Id', args.employeeId)
              .andWhereNot('CD.Employee_Id', args.employeeId)
              .transacting(trx)
              .then((data) => {
                if (data && data[0].employeeIdCount > 0) {
                  throw 'EBI00104'
                } else {
                  return organizationDbConnection(ehrTables.empJob)
                    .update('Emp_Email', args.email)
                    .where('Employee_Id', args.employeeId)
                    .transacting(trx)
                    .then((data) => {
                      if (data) {
                        //Update the phone number
                        return organizationDbConnection(
                          ehrTables.contactDetails
                        )
                          .update({
                            Mobile_No: mobileNumber,
                            Mobile_No_Country_Code: args.mobileNumberCountryCode
                          })
                          .where('Employee_Id', args.employeeId)
                          .transacting(trx)
                          .then(async (data) => {
                            if (data && updateInvitationStatus) {
                              await commonLib.firebase.revokeRefreshTokenAndUpdateEmpUser(organizationDbConnection, args.employeeId, context.partnerid, process.env.region, process.env.dbSecretName, trx)
                              systemLogParam = {
                                action: systemLogs.roleUpdate,
                                userIp: context.User_Ip,
                                employeeId: loginEmployeeId,
                                formName: formName.userAccounts,
                                trackingColumn: '',
                                organizationDbConnection:
                                  organizationDbConnection,
                                uniqueId: args.employeeId
                              }
                              //Return success response
                              return {
                                errorCode: '',
                                message:
                                  'User accounts email/phone updated successfully.'
                              }
                            } else if (data && !updateInvitationStatus) {
                              systemLogParam = {
                                action: systemLogs.roleUpdate,
                                userIp: context.User_Ip,
                                employeeId: loginEmployeeId,
                                formName: formName.userAccounts,
                                trackingColumn: '',
                                organizationDbConnection:
                                  organizationDbConnection,
                                uniqueId: args.employeeId
                              }
                              //Return success response
                              return {
                                errorCode: '',
                                message:
                                  'User accounts email/phone updated successfully.'
                              }
                            }
                          })
                      }
                    })
                }
              })
          })
          .then(async () => {
            //Call the function to add the system log
            await commonLib.func.createSystemLogActivities(systemLogParam)
            organizationDbConnection ? organizationDbConnection.destroy() : null
            return {
              errorCode: '',
              message: 'User account details updated successfully.'
            }
          })
          .catch((err) => {
            console.log(
              'Error in updateUserAccountsDetails() function .catch() block',
              err
            )
            errResult = commonLib.func.getError(err, 'EBI00102')
            //Destroy DB connection
            organizationDbConnection ? organizationDbConnection.destroy() : null
            throw new ApolloError(errResult.message, errResult.code) //return response
          })
      } else {
        throw 'IVE0000'
      }
    } else {
      console.log(
        'User does not have access to update User Accounts Email/Phone'
      )
      throw '_DB0102'
    }
  } catch (err) {
    console.log(
      'Error in the updateUserAccountDetails() function main catch block. ',
      err
    )
    //Destroy DB connection
    organizationDbConnection ? organizationDbConnection.destroy() : null
    //If validation error exist
    if (err === 'IVE0000') {
      console.log(
        'Validation error in the updateUserAccountDetails() function',
        validationError
      )
      errResult = commonLib.func.getError('', 'IVE0000')
      throw new UserInputError(errResult.message, {
        validationError: validationError
      }) //return response
    } else {
      errResult = commonLib.func.getError(err, 'EBI00003')
      throw new ApolloError(errResult.message, errResult.code) //return response
    }
  }
}
