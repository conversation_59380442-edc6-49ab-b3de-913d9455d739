// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../../common/tablealias');
//Require apollo server to return error message
const { ApolloError, UserInputError } = require('apollo-server-lambda');
// require common constant files
const { formIds, formName, systemLogs } = require('../../../../common/appconstants');
//require input validator
const { validateCommonRuleInput } = require('../../../../common/inputValidations');
//require moment
const moment = require('moment');

//function to add/update the rooms
let organizationDbConnection;
let inputValidationError = {}
module.exports.addUpdateRoom = async (parent, args, context, info) => {
    try {
        console.log("Inside addUpdateRoom function.")
        let employeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);

        // check their rights
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, employeeId, null, null, 'ui', null, formIds.room);
        if (Object.keys(checkRights).length > 0) {
            if (args.Room_Id && !checkRights.Role_Update) {
                throw '_DB0102';
            }
            else if (!args.Room_Id && !checkRights.Role_Add) {
                throw '_DB0101';
            }
        }

        //Input Validation
        let fieldValidations = {
            'Room_No': 'IVE0447',
            ...(args.Description && args.Description.length > 0 ? { 'Description': 'IVE0415' } : {})            
        }
        
        inputValidationError = await validateCommonRuleInput(args, fieldValidations);
        if (Object.keys(inputValidationError).length) {
            throw 'IVE0000'
        }

        //Already Exist Validation
        let alreadyExists = await organizationDbConnection(ehrTables.room)
            .select('Room_Id')
            .where('Room_No', args.Room_No)
            .whereNot('Room_Id', args.Room_Id || null)
            .first();

        if (alreadyExists) {
            throw 'CHR0138'
        }

        if (args.Room_Id) {
            await organizationDbConnection(ehrTables.room)
                .update({
                    'Room_No': args.Room_No,
                    'Description': args.Description,
                    'Updated_By': employeeId,
                    'Updated_On': moment().utc().format('YYYY-MM-DD HH:mm:ss')
                })
                .where('Room_Id', args.Room_Id);
        } else {
            await organizationDbConnection(ehrTables.room)
                .insert({
                    'Room_No': args.Room_No,
                    'Description': args.Description,
                    'Added_By': employeeId,
                    'Added_On': moment().utc().format('YYYY-MM-DD HH:mm:ss')
                });
        }

        // Add System Log
        let systemLogParams = {
            action: args.Room_Id ? systemLogs.roleUpdate : systemLogs.roleAdd,
            userIp: context.User_Ip,
            employeeId: employeeId,
            formName: formName.room,
            organizationDbConnection: organizationDbConnection,
            message: `Room was ${args.Room_Id ? 'updated' : 'added'} - ${args.Room_Id ? `Room Id - ${args.Room_Id}` : `Room No - ${args.Room_No}`}`
        };

        //Call function to add the system log
        await commonLib.func.createSystemLogActivities(systemLogParams);

        return { errorCode: "", message: `Room details ${args.Room_Id ? 'updated' : 'added'} successfully.` };

    }
    catch (e) {
        console.log('Error in addUpdateRoom function main catch block.', e);
        if (e === 'IVE0000') {
            let errResult = commonLib.func.getError('', 'IVE0000');
            console.log('Validation error in addUpdateRoom function - ', inputValidationError);
            throw new UserInputError(errResult.message, { validationError: inputValidationError });
        } else {
            let errResult = commonLib.func.getError(e, 'CHR00107');
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
    finally {
        //Destroy DB connection
        if (organizationDbConnection) {
            organizationDbConnection.destroy();
        }
    }
}
