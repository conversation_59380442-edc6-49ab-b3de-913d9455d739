# defining custom data type
scalar Date

type Query {
  listWorkScheduleDetails: listWorkScheduleDetailsResponse!
  listWeekDays: listWeekDaysResponse!
  retrieveWorkScheduleAndWeekOff(
    workScheduleId: Int!
  ): retrieveWSAndWeekOffResponse!
  getEmployeesDetailsBasedOnRole(
    formName: String!
    formId: Int
    serviceProviderId: Int
    customGroupId:Int
    flag:String
    isSalaryEdit: Boolean
    filterType:String
    attendanceMonth:String
    attendanceYear:String
    startDate:String
    endDate: String

  ): getEmployeesDetailsBasedOnRoleResponse!
  checkUserDefinedEmployeeExit(
    userDefinedEmpId: String!
    employeeId: Int
  ): commonResponse
  retrieveLeaveSettings(formName:String formId:Int): retrieveLeaveSettingsResponse
  retrieveRosterManagmentSetting(formName:String): retrieveRosterManagmentSettingResponse
  retrieveFormLevelCoverage(formName:String! Form_Id: Int!): formLevelCoverageResponse
  retrieveCompoffRules(formName:String): retrieveCompoffRulesResponse
  retrieveSpecialWages(formName:String): retrieveSpecialWagesResponse
  retrieveOvertimeConfiguration(formId: Int): retrieveOvertimeConfigurationResponse
  retrieveLeaveDetails(
    leaveId: Int!
  ): retrieveLeaveDetailsResponse!
  listCustomGroupHolidays(
    year: [String]!
    month: String!
  ):listCustomGroupHolidayResponse
  listHolidays: listHolidaysResponse
  listProjectDetails(formId: Int employeeId: Int): listProjectDetailsResponse
  initiateRefreshCustomEmpGroups(
    employeeId: [Int]
    logInEmpId: Int!
    isCustomGroupRefresh: Int
    orgCode: String
    updateOrganizationLeaveBalance: Int
    leaveEnforcementConfigValue: String
    isDOJUpdated: Boolean
    isProbationDateUpdated: Boolean
    isJobDetailsUpdated: Boolean
    refreshFromNonEmpAndLeaveForm: Boolean
  ): commonResponse!
  listDesignationDetails(offset:Int,limit:Int): listDesignationDetailsResponse
  listPreApprovalSettings: listPreApprovalSettingsResponse
  listPreApprovalRequests(formId:Int!): listPreApprovalRequestsResponse
  retrievePreApprovalSettings(employeeId:Int!, preApprovalType:String!, startDate:String, endDate:String, preApprovalId:Int,formId:Int!): retrievePreApprovalSettingsResponse
  retrieveHolidaySettings: retrieveHolidaySettingsResponse
  listWorkflowDetails(formId:Int, formName:String!): listWorkflowDetailsResponse
  listLocationHolidays(
    year: [String]!
    month: String!
  ):listLocationHolidayResponse
  listLeaveTypes: listLeaveTypesResponse
  listEmployeeDetails: listEmployeeDetailsResponse
  exportEmployeeDetails(
    typeOfEmployeeDetails: String!
    filterBy: String!
    location: [Int]!
    department: [Int]!
    designation: [Int]!
    sortBy: String 
  ):exportEmployeeDetailsResponse
  getAuthenticationMethods: getAuthenticationMethodsResponse
  getEffectiveDate(employeeIds: [String]): getEffectiveDateResponse
  retrieveShortTimeOffSettings: retrieveShortTimeOffSettingsResponse
  retrieveOnDutySettings: retrieveOnDutySettingsResponse
  validateWfhPreApproval(employeeId:Int!,source:String!,attendanceDate:Date,attendanceDateTime:String,punchInDateTime:String,punchOutDateTime:String,punchType:String!,checkInCheckoutWorkPlace:String!):validateWfhPreApprovalResponse
  getPreApprovalEmployeeList(formId:Int!): getPreApprovalEmployeeListResponse
  getEmployeeWeekOffAndHolidayDetails(month:Int, year:Int, employeeId:Int!,formId:Int!):getEmployeeWeekOffAndHolidayResponse
  retrieveTimesheetEmpLeaveWeekOff(weekendDate:String!,employeeId:Int!):retrieveTimesheetEmpLeaveWeekOffResponse
  listLanguages: listLanguagesResponse!
  listCountries: listCountriesResponse!
  listEmpProfession: listEmpProfessionResponse!
  listCourseDetails: listCourseDetailsResponse!
  listDocumentCategory: listDocumentCategoryResponse!
  listDocumentType: listDocumentTypeResponse!
  listDocumentSubType(isDefault:Boolean): listDocumentSubTypeResponse!
  retrieveAccreditationCategoryAndType: retrieveAccreditationCategoryAndTypeResponse!
  listBankDetails: listBankDetailsResponse!
  listAccountType: listAccountTypeResponse!
  listInsuranceType(employeeId:Int!, type: String!, screenType: String): listInsuranceTypeResponse!
  listBusinessUnit: listBusinessUnitResponse!
  listBusinessUnitInDropdown(action:String!): businessUnitDropDownResponse!
  retrieveLopRecoverySettingsDetails: retrieveLopRecoverySettingsDetailsResponse
  retrieveProjectActivities(projectId: Int!,formId : Int,weekendDate: String): retrieveProjectActivitiesResponse
  retrieveTimesheetSettings(formId: Int!):retrieveTimesheetSettingsResponse!
  listProjectActivities: listProjectActivitiesResponse
  retrieveTimeSheetProjectDetails(selfService: Int!,weekendDate : String!,employeeId:Int!,employeeView: Int!,requestId:Int,status: String):retrieveTimeSheetProjectDetailsResponse
  retrieveMinMaxDateForActivity(projectActivityId: Int!): retrieveMinMaxDateForActivityResponse
  getTimesheetCountForProject(projectId: Int!):getTimesheetCountForProjectResponse
  exportProjectDetailsAndActivities(projectId:[Int]!, offset: Int, limit: Int): exportProjectDetailsAndActivitiesResponse
  getTotalProjectAndActivityRecords(projectId:[Int]!): getTotalProjectAndActivityRecordsResponse
  listOrganizationGroup(formId:Int):listOrganizationGroupResponse
  listLeaveOverrideEmployeeDetails(employeeId: Int,formId:Int):leaveOverrideEmployeeDetailsResponse
  retrieveLeaveOverrideHistory(employeeId: Int!,leaveTypeId:Int!):leaveOverrideHistoryResponse
  listLocationDetails(limit:Int!, offset:Int!): listLocationDetailResponse
  retrieveLogoPath: retrieveLogoResponse
  listRooms: listRoomsResponse!
  listCompOffConfigHistory(configurationId:Int!): compOffConfigHistoryResponse
  exportEmployeeAllDetails(
    typeOfEmployeeDetails: [String!]!
    filterBy: [String!]!
    location: [Int]!
    department: [Int]!
    designation: [Int]!
    sortBy: String 
    fromDate: Date
    toDate: Date
    formId: Int!
    limit: Int
    offset: Int
  ):exportEmployeeDetailsResponse
  retrieveDynamicFormFields(formId: Int): retrieveDynamicFormFieldsResponse
  retrieveDynamicFieldValues(formId: Int!, primaryId: Int!): retrieveDynamicFieldValuesResponse
  listCustomEmailTemplates(templateId: Int,categoryId: Int,formId: Int,categoryTypeId:Int): ListCustomEmailTemplatesResponse!
  listEmailTemplatePlaceHolderValues(templateId: Int!, candidateId: Int,accessformId:Int!): ListEmailTemplatePlaceHolderValuesResponse
  retrieveAirTicketSettings(formId: Int!): RetrieveAirTicketSettingsResponse!
  retrieveAirTicketSettlementSummary(formId: Int!, employeeId:Int, month:Int, year:Int): RetrieveAirTicketSettlementSummaryResponse
  getEmployeeTravelSetting(formId: String!): EmployeeTravelSettingResponse
  listEmployeeIdPrefixSettings(formId: Int!, serviceProviderId: Int, status: String): listEmployeeIdPrefixSettingsResponse
  getLocationByDesignation(formId: Int!, designationId: Int!): getLocationByDesignationResponse
  listSalaryPayslip(formId: Int! employeeId: Int, year: Int isMonthly: Boolean): listSalaryPayslipResponse
  getCloudFrontUrl(fileName: String!): getCloudFronUrlResponse
}

type Mutation {
  addUpdateCustomFieldValues(
    Primary_Id: Int!
    Form_Id: Int!
    Field_Value: String!
  ): commonResponse
}


type retrieveDynamicFieldValuesResponse{
  errorCode: String
  message: String
  dynamicFieldValues: String
}

type retrieveDynamicFormFieldsResponse{
  errorCode: String
  message: String
  dynamicFields: String
}

type listRoomsResponse{
  errorCode: String
  message: String
  rooms: [room]
}

type room {
  Room_Id: Int
  Room_No: String
  Description: String
  Added_On: String
  Added_By: String
  Added_By_Name: String
  Updated_On: String
  Updated_By: String
  Updated_By_Name: String
}

type listInsuranceTypeResponse{
  errorCode: String
  message: String
  insuranceType: String
}
type listOrganizationGroupResponse{
errorCode: String
  message: String
  organizationGroupObject: organizationGroupObject
  }
  type organizationGroupObject{
  maxOrgCode: String
  organizationGroupList:[organizationGroupList]
  }
  type organizationGroupList{
  organizationGroupId: Int
   description: String
   status: String
   level: Int
   organizationGroupCode: String
   organizationGroup: String
   organizationGroupFullName: String
   addedOn: String
  addedByName: String
  updatedOn: String
  updatedByName: String
  }

type listAccountTypeResponse{
  errorCode: String
  message: String
  accountType: [accountType]
}

type accountType{
  Account_Type_Id: Int
  Account_Type: String
}

type listBankDetailsResponse{
  errorCode: String
  message: String
  bankDetails: [bankDetails]
}

type bankDetails{
  Bank_Id: Int
  Bank_Name: String
}

type retrieveAccreditationCategoryAndTypeResponse{
  errorCode: String
  message: String
  accreditationCategoryAndType: [accreditationCategoryAndType]
}

type accreditationCategoryAndType{
  Accreditation_Category_And_Type_Id: Int
  Accreditation_Category: String
  Accreditation_Type: String
  Mandatory: String
  Instruction: String
}

type listCourseDetailsResponse{
  errorCode: String
  message: String
  courseDetails: [courseDetails]
}

type courseDetails{
  Course_Id: Int
  Course_Name: String
  Course_Code: String
  Document_Sub_Type_Id: Int
}

type listDocumentCategoryResponse{
  errorCode: String
  message: String
  documentCategory: [documentCategory]
}

type documentCategory{
  Category_Id: Int
  Category_Fields: String
  Vendor_Based: Int
}

type listDocumentTypeResponse{
  errorCode: String
  message: String
  documentType: [documentType]
}

type documentType{
  Document_Type_Id: Int
  Category_Id: Int
  Document_Type: String
}

type listDocumentSubTypeResponse{
  errorCode: String
  message: String
  documentSubType: [documentSubType]
}

type documentSubType{
  Document_Sub_Type_Id: Int
  Document_Type_Id: Int
  Document_Sub_Type: String
  Mandatory: String
  Instruction: String
  Only_For_Email:String
  Is_Default : Boolean

}

type listEmpProfessionResponse{
  errorCode: String
  message: String
  professions: [profession]
}

type profession{
  Profession_Id: Int
  Profession_Name: String
}

type listCountriesResponse{
  errorCode: String
  message: String
  countries: [country]
}

type country{
  Country_Code: String
  Country_Name: String
}

type listLanguagesResponse{
  errorCode: String
  message: String
  languages: [language]
}

type language {
  Lang_Id: Int
  Language_Name: String
}

type retrieveOnDutySettingsResponse{
  errorCode: String
  message: String
  onDutySettings: onDutySettings
}

type onDutySettings{
  Coverage: String
  Custom_Group_Id: Int
  Updated_On: String
  updatedByName: String
}

type retrieveShortTimeOffSettingsResponse{
  errorCode: String
  message: String
  shortTimeOffSettings: shortTimeOffSettings
}

type shortTimeOffSettings{
  Maximum_Limit: Int
  Period: String
  Max_Short_Time_Per_Request: Int
  Min_Short_Time_Per_Request: Int
  Frequency: Int
  Gender: String
  Leave_Activation_Days: Int
  Limit_By: String
  Maximum_Duration: Int
  Total_Duration: Int
  Minimum_Duration: Int
  Enforce_Alternate_Person: String
  Coverage_For_Alternate_Person: String
  Updated_On: String
  updatedByName: String
}

type getEffectiveDateResponse{
  errorCode: String
  message: String
  getEffectiveDate: [effectiveDate]
}

type effectiveDate{
  Employee_Id: String
  Effective_Date: String
  Date_Of_Join: String
  Min_Date: String
  Max_Date: String
  Business_Unit_Id_End_Date: String
  Designation_Id_End_Date: String
  Department_Id_End_Date: String
  Location_Id_End_Date: String
  Work_Schedule_End_Date: String
  Manager_Id_End_Date: String
  EmpType_Id_End_Date: String
}

type getAuthenticationMethodsResponse{
  errorCode: String
  message: String
  authenticationMethods: [authenticationMethods]
}

type authenticationMethods{
  Authentication_Method_Id: Int
  Authentication_Method: String
  Description: String
}

type exportEmployeeDetailsResponse{
  errorCode: String
  message: String
  validationError: String
  exportEmployeeDetails: String
}

type listEmployeeDetailsResponse{
  errorCode: String
  message: String
  listEmployeeDetails: [listEmployeeDetails]
}

type listEmployeeDetails{
  Emp_First_Name: String
  Emp_Middle_Name: String
  Emp_Last_Name: String
  Employee_Id: Int
  User_Defined_EmpId: String
  Emp_Email: String,
  Emp_Status: String,
  Manager_Id: Int,
  Mobile_No: String,
}

type listLocationHolidayResponse{
  errorCode: String
  message: String
  listLocationHolidays: [listLocationHolidays]
}

type listLocationHolidays {
  Holiday_Assign_Id: Int
  Holiday_Id: Int
  Holiday_Date: String
  Mandatory: Int
  Personal_Choice: Int
  Holiday: Int
  Description: String
  Added_By: Int
  Added_Date: String
  Updated_By: String
  Modified_Date: String
  Holiday_Name: String
  Location_Id: Int
  Location_Name: String
  addedByName: String
  updatedByName: String
}

type retrieveHolidaySettingsResponse {
  errorCode: String
  message: String
  holidaySettings: String
  displayPersonalChoice: Int
}

type listProjectDetailsResponse {
  errorCode: String
  message: String
  projectCoverage: String
  projectDetails: [projectData]
}

type listDesignationDetailsResponse{
  errorCode: String
  message: String
  totalRecords: Int
  designationDetails: [designationData]
  gradeDetails: [gradeData]
}

type listPreApprovalSettingsResponse{
  errorCode: String
  message: String
  preApprovalSettings: [preApprovalSettingsData]
}

type preApprovalSettingsData {
  preApprovalConfigurationId: Int
  preApprovalType: String
  coverage: String
  customGroupId: Int
  customGroupName: String
  period: String
  noOfPreApprovalRequest: Int
  restrictSandwich: String
  restrictSandwichFor: String
  advanceNotificationDays: Int
  documentUpload: String
  maxDaysForDocumentUpload: String
  maxDaysAllowedPerRequest: Float
  workflowId: Int
  workflowName: String
  status: String
  typeOfDay: String
  addedOn: String
  updatedOn: String
  addedByName: String
  updatedByName: String
}

type listPreApprovalRequestsResponse {
  errorCode: String
  message: String
  preApprovalRequests: [preApprovalRequestsData]
}

type preApprovalRequestsData {
  preApprovalId: Int
  preApprovalType: String
  overTimeHours: Float
  userDefinedEmpId : String
  Work_Schedule: Int
Designation_Id: Int
Designation_Name: String
Department_Id: Int
EmpType_Id: Int
Employee_Type: String
Department_Name: String
Location_Name: String
Location_Id: Int
  employeeId: Int
  employeeName: String
  duration: String
  period: String
  startDate: String
  endDate: String
  totalDays: Float
  reason: String
  status: String
  fileName: String
  fileSize: String
  addedOn: String
  updatedOn: String
  addedByName: String
  updatedByName: String
  approvedByName:String
  approvedOn:String
}

type retrievePreApprovalSettingsResponse {
  errorCode: String
  message: String
  preApprovalSettings: preApprovalSettingsDetails
}

type preApprovalSettingsDetails {
  period: String
  noOfPreApprovalRequest: Int
  restrictSandwich: String
  restrictSandwichFor: String
  advanceNotificationDays: Int
  preApprovalsTaken: Float
  documentUpload: String
  maxDaysForDocumentUpload: Float
  workflowId: Int
  typeOfDay: String
  maxDaysAllowedPerRequest: Float
}

type listWorkflowDetailsResponse {
  errorCode: String
  message: String
  workflowDetails: [workflowDetails]
}

type workflowDetails {
  Workflow_Id: Int
  Workflow_Name: String
  Form_Id: Int
}

type projectData {
  projectId: Int
  projectName: String
  description: String
  clientName: String
  locationId: Int
  locationName: String
  managerId: Int
  addedOn: String
  updatedOn: String
  addedByName: String
  updatedByName: String
  managerName: String
  employeeId: String
  employeeName: String
  customGroupId: String
  customGroupName: String
  status: String
}

type gradeData {
  gradeId: Int
  grade: String
  level: Int
}

type designationData {
  designationId: Int
  designationName: String
  designationCode: String
  description: String
  level: Int
  probationDays: Int
  gradeId: Int
  employeeConfirmation: String
  attendanceEnforcedPayment: Int
  status: String
  attendanceEnforcedGeoLocation: Int
  grade: String
  noticePeriodDaysWithinProbation: Int
  noticePeriodDaysAfterProbation: Int
  noticePeriodPayByEmployer: String
  noticePeriodPayByEmployee: String
  addedOn : String
  updatedOn : String
  addedByName : String
  updatedByName : String
}

type listHolidaysResponse{
  errorCode: String
  message: String
  listHolidays: [holidayList]
}

type holidayList {
  Holiday_Id: Int
  Holiday_Name: String
  Description: String
  Added_On: String
  Updated_On: String
  addedByName: String
  updatedByName: String
}

type listCustomGroupHolidayResponse {
  errorCode: String
  message: String
  listCustomGroupHolidays: [listCustomGroupHolidays]
}

type listCustomGroupHolidays {
  Holiday_Assign_Id: Int
  Holiday_Id: Int
  Holiday_Date: String
  Mandatory: Int
  Personal_Choice: Int
  Holiday: Int
  Description: String
  Added_By: Int
  Added_Date: String
  Updated_By: String
  Modified_Date: String
  Holiday_Name: String
  Group_Id: Int
  Group_Name: String
  addedByName: String
  updatedByName: String
}
type retrieveRosterManagmentSettingResponse {
  errorCode: String
  message: String
  rosterManagmentSetting: rosterManagmentSetting
}
type retrieveCompoffRulesResponse {
  errorCode: String
  message: String
  CompoffRules: [CompoffRules]
}
type formLevelCoverageResponse {
  errorCode: String
  message: String
  formLevelCoverage: [formLevelCoverage]
}
type retrieveSpecialWagesResponse {
  errorCode: String
  message: String
  SpecialWagesConfiguration: [SpecialWagesConfiguration]
}
type SpecialWagesConfiguration{
   Custom_Group_Id: Int
  Configuration_Id: Int
  Attendance_Required: String
  Wage_Factor: Float
  Group_Name: String
  Salary_Type: String
  Special_Work_Days: String
  Status: String
  Added_On: String
  Added_By: String
  Updated_On: String
  Updated_By: String
}
type retrieveOvertimeConfigurationResponse {
  errorCode: String
  message: String
  OvertimeConfiguration: [OvertimeConfiguration]
}
type OvertimeConfiguration{
  Custom_Group_Id: Int
  Configuration_Id: Int
  Wage_Factor: Float
  Group_Name: String
  Salary_Type: String
  Special_Work_Days: String
  Status: String
  Overtime_Type: String
  Amount: Float
  Added_On: String
  Added_By: String
  Updated_On: String
  Updated_By: String
}
type CompoffRules {
  Configuration_Id: Int
  Custom_Group_Id: Int
  Group_Name: String
  Comp_Off_Balance_Approval: String
  Comp_Off: String
  compOffBalance: String
  Comp_Off_Expiry_Type: String
  Comp_Off_Expiry_Days: Int
  Comp_Off_Encashment: String
  Encashment_Mode: String
  Comp_Off_Threshold: String
  Allow_Half_Day_Comp_Off_Credit: String
  Fixed_Regular_Hours: Float
  Minimum_Hours_For_Half_Day_Comp_Off: Float
  Salary_Type: String
  Work_Day_Type: String
  Status: String
  Comp_Off_Balance_Id: Int
  Workflow_Approval: Int
  Comp_Off_Applicability_For_Overtime_Hours: String
  Minimum_OT_Hours_For_Full_Day_Comp_Off: Float
  Minimum_OT_Hours_For_Half_Day_Comp_Off: Float
  Added_On: String
  Added_By: String
  Updated_On: String
  Updated_By: String
}
type rosterManagmentSetting {
  Dynamic_Week_Off : Int
  Overlap_Shift_Schedule : Int
  Max_Swap_Requests_Per_Month:Int
  Enable_Shift_Swap_Restriction: String,
  Allow_Past_Shift_Swaps:String,
  Max_Shift_Swap_Days:Int,
  Updated_By: String,
  Updated_On: String
}
type formLevelCoverage {
  Coverage_Id: Int,
 Form_Id : Int,
 Coverage: String,
  Updated_On: String,
   Updated_By: String
}
type retrieveLopRecoverySettingsDetailsResponse {
  errorCode: String
  message: String
  lopRecoverySettingsDetails: [lopRecoverySettingsDetails]
}
type lopRecoverySettingsDetails{
  Lop_Settings_Id: String
  Auto_LOP_Applicable: String
  Coverage: String
  Custom_Group_Id: Int
  Group_Name: String
  Attendance_Shortage_Applicable: String
  Late_Attendance_Applicable: String
  Workflow_Id: Int
  Workflow_Name: String
  Configuration_Status: String
  Added_On: String
  Added_By: String
  Updated_On: String
  Updated_By: String
}
type retrieveLeaveSettingsResponse {
  errorCode: String
  message: String
  leaveSettings: leaveSettings
}
type leaveSettings {
  Allow_Upline_Managers_Approval: String
  Enable_CAMU_Scheduler: String
  Enable_Workflow: String
  Enforce_Comment_For_Approval: String
  Enforce_Comment_For_Leave: String
  Enforce_Alternate_Person_For_Leave: String
  Coverage_For_Alternate_Person: String
  Updated_On: String
  updatedByName: String
}

type listWorkScheduleDetailsResponse {
  errorCode: String
  message: String
  workScheduleDetails: [listWorkScheduleDetails]
}

type listWorkScheduleDetails {
  workScheduleId: Int
  workScheduleCode: String
  workSchedule: String
  businessHoursStartTime: String
  businessHoursEndTime: String
  breakScheduleStartTime: String
  breakScheduleEndTime: String
  targetEffortInHoursPerWeek: Float
  status: String
}

type listWeekDaysResponse {
  errorCode: String
  message: String
  weekDayDetails: [listWeekDaysDetails]
}

type listWeekDaysDetails {
  dayId: Int
  dayName: String
}

type retrieveWSAndWeekOffResponse {
  errorCode: String
  message: String
  workScheduleDetails: String
  weekOffDetails: [viewWeekOffDetails]
}

type viewWeekOffDetails {
  dayId: Int
  dayName: String
  weekNumberList: String
  durationList: String
  excludeLastWeek: String
  weekOffAddedBy: String
  weekOffAddedByEmployeeName: String
  weekOffAddedOn: String
  weekOffUpdatedBy: String
  weekOffUpdatedByEmployeeName: String
  weekOffUpdatedOn: String
}

type getEmployeesDetailsBasedOnRoleResponse {
  errorCode: String
  message: String
  employeeDetails: [employeeDetails]
}

type getPreApprovalEmployeeListResponse  {
  errorCode: String
  message: String
  employeeDetails: preApprovalEmployees
}

type preApprovalEmployees {
  workFromHome:[employeeDetails]
  workDuringWeekOff:[employeeDetails]
  workDuringHoliday:[employeeDetails]
  onDuty:[employeeDetails]
  overtimeWork:[employeeDetails]
}


type getEmployeeWeekOffAndHolidayResponse {
  errorCode: String
  message: String
  totalBusinessWorkingDays: Float
  weekOffAndHolidayDetails: [weekOffAndHolidayDetails]
  workScheduleToSend: [workScheduleToSend]
}
type workScheduleToSend{
    Work_Schedule_Date: String,
    Consideration_From: String,
    Consideration_To: String,
    Regular_From: String,
    Regular_To: String,
    Allow_Attendance_Outside_Regular_WorkHours: Int
}
type retrieveTimesheetEmpLeaveWeekOffResponse{
   errorCode: String
  message: String
  employeeDetails: String
}
type weekOffAndHolidayDetails {
  date: String
  isShiftScheduled: Int
  isWeekOffDay: Int
  weekOffDuration: Float
  isHoliday: Int
}

type employeeDetails {
  employeeId: Int!
  userDefinedEmpId: String
  employeeName: String
  designationId: Int
  designationName: String
  departmentId: Int
  Emp_Email: String
  departmentName: String
  locationId: Int                      
  locationName: String
  dateOfJoin: String 
  resignationDate:String    
  totalHours:Float             
  empStatus: String                      
  empTypeId: Int
  employeeType: String
  Enable_Work_Place:String                      
  managerId: Int                                                             
  probationDate: String
  workSchedule: Int 
  Service_Provider_Id:Int
  Service_Provider_Name: String
  isRecruiter: String
  Last_SalaryDate:String
  eligibleForPf:String
  eligibleForPension:String
  eligibleForESI:String
  eligibleForInsurance:String
  eligibleForNps:String
  eligibleForGratuity:String
  minimumWage:Float
}

type retrieveLeaveDetailsResponse {
  errorCode: String
  message: String
  leaveDetails: leaveDetails
}

type leaveDetails {
  Leave_Id: Int
  Reason: String
  Duration: String
  Start_Date: String
  End_Date: String
  Total_Days: Float
  Hours: Float
  Contact_Details: String
  Employee_Id: Int
  Approver_Id: Int
  LeaveType_Id: Int
  Alternate_Person: String
  Approval_Status: String
  Added_By: String
  Added_On: String
  Updated_By: String
  Updated_On: String
  Lock_Flag: Int
  Leave_Period: String
  Reason_Id: Int
  Late_Attendance: Int
  Workflow_Status: String
  Workflow_Instance_Id: String
  Attendance_Shortage: Int
  Approved_By: Int
  Approved_On: String
  Leave_Type: String
  Employee_Name: String
  ESIC_Reason: String
}

type listLeaveTypesResponse {
  errorCode: String
  message: String
  leaveTypeDetails: [leaveTypeDetails]
}

type leaveTypeDetails {
  leaveTypeId: Int!
  leaveName: String
  status: String
}

type validateWfhPreApprovalResponse{
  errorCode: String
  message: String
  preApprovalRequestType: String
  preApprovalRequestStatus: String
}

type listBusinessUnitResponse{
  errorCode: String
  message: String
  settings: [businessUnitSettings]
}

type businessUnitSettings {
  businessUnitId: Int!
  businessUnit: String
  status: String
  description: String
  level: Int
  addedOn: String
  addedByName: String
  updatedOn: String
  updatedByName: String
  businessUnitCode: String
  businessUnitParentId: Int
  businessUnitParentName: String
}

type businessUnitDropDownResponse{
  errorCode: String
  message: String
  settings: [businessUnitDropDownSettings]
}

type businessUnitDropDownSettings {
  businessUnitId: Int!
  businessUnit: String
}

type retrieveTimeSheetProjectDetailsResponse {
  errorCode: String
  message: String
  timesheetActivityDetails: String
}
type commonResponse {
  errorCode: String
  message: String
}

type retrieveProjectActivitiesResponse{
  errorCode: String
  message: String
  activityDetails: [activityDetails]
}
type retrieveTimesheetSettingsResponse{
  errorCode: String
  message: String
  timesheetSettingData: [timesheetSettingData]
}
type timesheetSettingData{
Timesheet_Setting_Id: Int,
	Timesheet_Submission_Before_Weekend_Date: String,
  Present_Time_Slot: String,
  Enforce_Note: String
}

type activityDetails{
  projectId:Int,
  projectActivityId:Int
  activityId: Int,
  activityName: String,
  description: String,
  isBillable: String,
  activityFrom: String,
  activityTo: String

}

type listProjectActivitiesResponse{
  errorCode: String
  message: String
  activities: [activities]
}

type activities{
  activityId: Int, 
  activityName: String, 
  isBillable: String, 
  description: String,
  addedOn: String,
  updatedOn: String,
  addedByName: String,
  updatedByName: String
}

type retrieveMinMaxDateForActivityResponse{
  errorCode: String
  message: String
  activityData: [activityData]
}

type activityData{
  minDate: String
  maxDate: String
}

type getTimesheetCountForProjectResponse{
  errorCode: String
  message: String
  timesheetCount: Int
}


type exportProjectDetailsAndActivitiesResponse{
  errorCode: String
  message: String
  projectCoverage:String
  projectDetailsAndActivities: String
}

type getTotalProjectAndActivityRecordsResponse{
  errorCode: String
  message: String
  projectAndActivityRecordsCount: Int
}

type leaveOverrideEmployeeDetailsResponse{
  errorCode: String
  message: String
  employeeEligibleLeaveDetails: [employeeElgibleLeaveDetail]
} 

type employeeElgibleLeaveDetail{
  employeeId: Int,
  userDefinedEmpId: String,
  employeeName: String,
  leaveTypeId: Int,
  leaveType: String,
  carryOver: String,
  carryOverAccumulationLimit: Float,
  eligibleLeaveId: Int,
  currentYearTotalEligibleDays: Float,
  totalCODays: Float,
  lastCOBalance: Float,
  leavesTaken: Float,
  encashedDays: Float,
  totalAppliedLeaveDays: Float
  encashmentProcessedClosurePending:String
  departmentName: String
  locationName: String
  designationName: String,
  calculatedLeaveBalance: Float
  leaveOverrideReason: String
}

type leaveOverrideHistoryResponse{
  errorCode: String
  message: String
  historyDetails: [leaveOverrideHistoryDetail]
}

type leaveOverrideHistoryDetail{
  userDefinedEmpId: String,
  employeeName: String,
  leaveType: String,
  currentYearTotalEligibleDays: Float,
  carryOver: String,
  totalCODays: Float,
  lastCOBalance: Float,
  leavesTaken: Float,
  updatedOn: String
  updatedBy: String
}

type listLocationDetailResponse {
  errorCode: String
  message: String
  location: [locationData]
}

type locationData {
  Location_Id: Int
  Location_Name: String
  Location_Code: String
  Location_Type: String
  Location_Status: String
  Street1: String
  Street2: String
  City_Id: Int
  City_Name: String
  State_Id: Int
  State_Name: String
  Country_Code: String
  Country_Name: String
  Pincode: String
  Phone: String
  Fax: String
  Notes: String
  Description: String
  Currency_Symbol: String
  Currency_Id: Int
  Currency_Name: String
  External_LocationId: Int
  Org_Id: Int
  TimeZone_Id: Int
  TimeZone_Name: String
  Added_On: Date
  Added_By: Int
  Added_By_Name: String
  Updated_On: Date
  Updated_By: Int
  Updated_By_Name: String
  Barangay: String
  Barangay_Id: Int
  Region: String
}

type retrieveLogoResponse{
  errorCode: String
  message: String
  logoPath: String
}

type compOffConfigHistoryResponse {
  errorCode: String
  message: String
  compOffHistory: [compOffHistoryDetails]
}

type compOffHistoryDetails {
  History_Id:Int!
  Custom_Group_Id: Int
  Group_Name: String
  Comp_Off_Balance_Approval: String
  Comp_Off: String
  compOffBalance: String
  Comp_Off_Expiry_Type: String
  Comp_Off_Expiry_Days: Int
  Comp_Off_Encashment: String
  Encashment_Mode: String
  Comp_Off_Threshold: String
  Allow_Half_Day_Comp_Off_Credit: String
  Fixed_Regular_Hours: Float
  Minimum_Hours_For_Half_Day_Comp_Off: Float
  Salary_Type: String
  Work_Day_Type: String
  Comp_Off_Balance_Id: Int
  Workflow_Approval: Int
  Comp_Off_Applicability_For_Overtime_Hours: String
  Minimum_OT_Hours_For_Full_Day_Comp_Off: Float
  Minimum_OT_Hours_For_Half_Day_Comp_Off: Float
  Added_On: String
  Added_By: Int
  Added_By_Name:String
  Status: String
}
type ListCustomEmailTemplatesResponse {
    errorCode: String
    message: String
    emailTemplates: [EmailTemplateDetails]
}

type EmailTemplateDetails {
    Template_Id: Int
    Template_Name: String
    Template_Content: String
    Template_Fields: String
    Subject_Content:String
    Subject_Fields:String
    Visibility: String
    Added_By_Name:String
    Updated_By_Name:String
    Category_Type_Name:String
    Category_Name:String
    Category_Id:Int
    Form_Name:String
    To_Emails: String
    CC_Emails: String
    Bcc_Emails: String
    Additional_Emails: String
    External_Emails: String
    Default_Template: String
    Attachments:String
    AttachmentsFileNames:String
    Added_On: String
    Added_By: Int
    Updated_On: String
    Updated_By: Int
    Form_Id:Int
    Category_Type_Id:Int
    Sender_Name: String
}
type JobCandidate {
  candidateFirstName: String
  candidateLastName: String
  candidateMiddleName: String
  initiatorFirstName: String
  initiatorMiddleName: String
  initiatorLastName: String
  initiatorMobileNumber: String
  initiatorMail: String
  jobTitle: String
  initiatorDesignation: String
  sourceOfApplication: String
  candidateStatus: String
  interviewStartDate:String
  interviewEndDate:String
  interviewDate: String
  archiveReason: String
  companyName: String
  street1: String
  street2: String
  city: String
  state: String
  country: String
  pincode: String
  region: String
  barangay: String
  dateOfJoin: String
  probationDate: String
  initiatorEmployeeId: String
  initiatorDesignationCode:String
  organizationGroup: String
  areaDepartment: String
  sectionBranch: String
  sectionBranchCode: String
  immediateManagerFirstName: String
  immediateManagerMiddleName: String
  immediateManagerLastName: String
  immediateManagerUserDefinedEmpId: String
  secondLineManagerFirstName: String
  secondLineManagerMiddleName: String
  secondLineManagerLastName: String
  onboardingPassword: String
  onboardingLink: String
  companyLogoUrl: String
  organizationGroupCode: String
  OrganizationUnitName: String
  organizationUnitCode: String
  immediateManagerId: String
  secondLineManagerId: String
  areaDepartmentCode: String
  areaDepartmentName: String
  designationCode: String
  designation:String
  divisionCode: String
  regionDivision: String
  divisionName: String
  groupCode: String
  groupName: String
  locationCode: String
  locationName: String
  employeeUserDefinedEmpId: String
  organizationName: String
  employeeFirstName: String
  employeeLastName: String
  employeeMiddleName: String
  experiencePortalUrl: String
}

type Emails {
  toEmails: [String]
  ccEmails: [String]
  bccEmails: [String]
  additionalEmails:[String]
}

type ListEmailTemplatePlaceHolderValuesResponse {
  errorCode: String
  message: String
  jobCandidate: JobCandidate
  emails: Emails
}
type RetrieveAirTicketSettingsResponse {
    errorCode: String
    message: String
    airTicketSettingData: [AirTicketSettingData]
}

type AirTicketSettingData {
    Air_Ticket_Setting_Id: Int
    Destination_City: String
    Destination_Country: String
    Air_Ticketing_Category: String
    Status: String
    Infant_Amount:Float
    Child_Amount:Float
    Adult_Amount:Float
    Added_On: String
    Added_By: String
    Updated_On: String
    Updated_By: String
}
type AirTicketSettlementSummary {
  Employee_Id: Int
  Employee_Name:String
  User_Defined_EmpId:String
  Air_Ticket_Settlement_Summary_Id: Int
  Destination_City: String
  Destination_Country: String
  Air_Ticketing_Category: String
  Effective_Date_Enable: String
  Effective_Date: String
  Availed_Date: String
  Eligibility_Of_Ticket_Claim_Months: Int
  Air_Ticket_To_Dependent: String
  Dependent_Relationship: String
  Settlement_Status:String
  Payroll_Month:String
  No_Of_Dependents: Int
  Infant_Dependents_Ticket: Int
  Child_Dependents_Ticket: Int
  Adult_Dependents_Ticket: Int
  Infant_Accrued_Tickets: Float
  Child_Accrued_Tickets: Float
  Adult_Accrued_Tickets: Float
  Total_Accrued_Tickets: Float
  Infant_Accrued_Amount: Float
  Child_Accrued_Amount: Float
  Adult_Accrued_Amount: Float
  Infant_Policy_Amount:Float
  Child_Policy_Amount:Float
  Adult_Policy_Amount:Float
  Settlement_Amount: Float
  Added_On: String
  Updated_On: String
  Added_By: String
  Updated_By: String
}

type EmployeeTravelSetting {
  Employee_Travel_Setting_Id: Int!
  Email_Recipients: String
  Additional_Recipients: String
  Updated_By: String
  Added_By: String
}

type EmployeeTravelSettingResponse {
  errorCode: String
  message: String
  travelSetting: EmployeeTravelSetting
}

type RetrieveAirTicketSettlementSummaryResponse {
  errorCode: String
  message: String!
  settlementSummary: [AirTicketSettlementSummary]
}

type listEmployeeIdPrefixSettingsResponse {
  errorCode: String
  message: String
  employeeIdPrefixSettings: [employeeIdPrefixSetting]
  config: employeeIdPrefixConfig
}

type employeeIdPrefixConfig {
  empPrefixConfigId: Int
  isEnabled: Boolean
  configLevel: String
  addedOn: String
  addedByName: String
  updatedOn: String
  updatedByName: String
}

type employeeIdPrefixSetting {
  empPrefixSettingId: Int
  serviceProviderId: Int
  serviceProviderName: String
  prefix: String
  suffix: String
  noOfDigits: Int
  nextNumber: Int
  status: String
  addedOn: String
  addedByName: String
  updatedOn: String
  updatedByName: String
}


type getLocationByDesignationResponse {
  errorCode: String
  message: String
  locationId: Int
}

type listSalaryPayslipResponse {
  errorCode: String
  message: String
  salaryPayslips: [salaryPayslip]
  isSyntrumEnabled: Boolean
  externalApiSyncDetail: externalApiSyncDetail
  monthList: [Int]
}

type salaryPayslip {
  Payslip_Id: ID
  Salary_Month: String
  Generated_On: String
  Basic_Salary: Float
  Total_Earning: Float
  Total_Deduction: Float
  Total_Salary: Float
  Payment_Status: String
  Formatted_Salary_Month: String
  Generated_By: String
  User_Defined_EmpId: String
  Employee_Name: String
  Service_Provider_Id: String
  Location_Id: String   
  Eligible_For_Contractor_Tds: String
  Pay_Period: String
  Template_Id: Int
  Department_Id: String
  Manager_Id: String
  Designation_Id: String
  Designation_Name: String
  Department_Name: String
  S3_FileName: String
  Location_Name: String
  Employee_Type: String
  EmpType_Id: String
  Organization_Unit_Name: String
  Incentive_Amount: Float
  Formatted_Total_Salary: String
}
type getCloudFronUrlResponse {
  errorCode:String
  message : String
  url: String
}

type externalApiSyncDetail {
  Integration_Type: String
  Sync_Direction: String
  Action: String
  Entity_Type: String
  Status: String
}

schema {
  query: Query
  mutation: Mutation
}
