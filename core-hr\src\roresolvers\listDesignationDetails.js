// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;

// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formName } = require('../../common/appconstants');

let organizationDbConnection;
module.exports.listDesignationDetails = async (parent, args, context, info) => {
    try {
        console.log("Inside listDesignationDetails function.")
        let employeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        // get the access rights of the login employee for the accreditation form
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, employeeId, formName.designationOrPosition, '', 'UI');
        if (Object.keys(checkRights).length > 0 && checkRights.Role_View === 1) {
            return (
                organizationDbConnection(ehrTables.designation)
                    .select('D.Designation_Id as designationId', 'D.Level as level', 'D.Designation_Name as designationName', 'D.Designation_Code as designationCode', 'D.Probation_Days as probationDays',
                        'D.Description as description', 'D.Grade_Id as gradeId', 'D.Employee_Confirmation as employeeConfirmation', 'D.Added_On as addedOn', 'D.Updated_On as updatedOn',
                        'D.Attendance_Enforced_Payment as attendanceEnforcedPayment', "D.Designation_Status as status", 'D.Attendance_Enforced_GeoLocation as attendanceEnforcedGeoLocation',
                        'G.Grade as grade', 'D.Notice_Period_Days_Within_Probation as noticePeriodDaysWithinProbation', 'D.Notice_Period_Days_After_Probation as noticePeriodDaysAfterProbation', 'D.Notice_Period_Pay_By_Employer as noticePeriodPayByEmployer', 'D.Notice_Period_Pay_By_Employee as noticePeriodPayByEmployee',
                        organizationDbConnection.raw("CONCAT_WS(' ',EPI.Emp_First_Name,EPI.Emp_Middle_Name, EPI.Emp_Last_Name) as addedByName"),
                        organizationDbConnection.raw("CONCAT_WS(' ',EPI2.Emp_First_Name,EPI2.Emp_Middle_Name, EPI2.Emp_Last_Name) as updatedByName"),
                    )
                    .from(ehrTables.designation + " as D")
                    .innerJoin(ehrTables.empGrade + " as G", "G.Grade_Id", "D.Grade_Id")
                    .leftJoin(ehrTables.empPersonalInfo + " as EPI", "EPI.Employee_Id", "D.Added_By")
                    .leftJoin(ehrTables.empPersonalInfo + " as EPI2", "EPI2.Employee_Id", "D.Updated_By")
                    .modify(function (queryBuilder) {
                      if (args.offset) {
                        queryBuilder.offset(args.offset);
                      }
                      if (args.limit) {
                        queryBuilder.limit(args.limit);
                      }
                    })
                    .then(async(designationDetails) => {
                        const totalRecordsResult = await organizationDbConnection(ehrTables.designation)
                                                  .count('* as totalRecords')
                                                  .first();
                        const totalRecords = totalRecordsResult.totalRecords;
                        return (
                            organizationDbConnection(ehrTables.empGrade)
                                .select('Grade_Id as gradeId', 'Level as level',
                                    organizationDbConnection.raw(`
                                    CASE
                                      WHEN Grade_Code IS NOT NULL AND TRIM(Grade_Code) != ''
                                      THEN CONCAT(Grade_Code, ' - ',Grade)
                                      ELSE Grade
                                    END AS grade
                                  `))
                                .then((gradeData) => {
                                    //destroy the connection
                                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                                    return { errorCode: "", message: "Designation details retrieved successfully.", designationDetails: designationDetails, gradeDetails: gradeData,totalRecords:totalRecords };
                                }).catch((err) => {
                                    //Destroy DB connection
                                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                                    console.log('Error in listDesignationDetails function .catch block.', err);
                                    let errResult = commonLib.func.getError(err, 'CHR0034');
                                    throw new ApolloError(errResult.message, errResult.code);
                                })

                        )
                    })
                    .catch((err) => {
                        //Destroy DB connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        console.log('Error in listDesignationDetails function .catch block.', err);
                        let errResult = commonLib.func.getError(err, 'CHR0019');
                        throw new ApolloError(errResult.message, errResult.code);
                    })
            )
        }
        else {
            console.log('Employee do not have view access rights');
            throw '_DB0100';
        }

    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in listDesignationDetails function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'CHR0019');
        throw new ApolloError(errResult.message, errResult.code);
    }
}
