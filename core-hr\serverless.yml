service: COREHR # service name

plugins:
  - serverless-domain-manager
  - serverless-prune-plugin # Plugin to maintain lambda versioning
  - serverless-offline # require plugins
  - serverless-step-functions
provider:
  name: aws
  runtime: nodejs18.x #nodejs run time
  stage: ${opt:stage} # get current stage name
  region: ${opt:region} #region in which to be deployed
  role: ${file(config.${self:provider.stage}.json):lambdaRole} # Assign role to the lambda functions
  vpc:
    securityGroupIds: ${file(./config.${self:provider.stage}.json):securityGroupIds}
    subnetIds: ${file(./config.${self:provider.stage}.json):subnetIds}
custom:
  customDomain:
    domainName: ${file(./config.${self:provider.stage}.json):customDomainName}
    basePath: 'coreHr'
    stage: ${self:provider.stage}
    createRoute53Record: true
    endpointType: 'edge'
  prune:
    automatic: true
    number: 3

# Lambda functions
functions:
  rographql:
    handler: src/rohandler.graphql
    timeout: 900 # Lambda timeout
    events:
      - http:
          path: rographql
          method: post
          cors:
            origin: '*'
            headers:
              - Content-Type
              - X-Amz-Date
              - X-Api-Key
              - X-Amz-Security-Token
              - X-Amz-User-Agent
              - authorization
              - org_code
              - user_ip
              - refresh_token
              - partnerid
              - additional_headers
            allowCredentials: false
          authorizer:
            arn: ${file(config.${self:provider.stage}.json):authorizerARN}
            resultTtlInSeconds: 0
            type: request
    environment: # environment variables
      stageName: ${self:provider.stage}
      domainName: ${file(config.${self:provider.stage}.json):domainName}
      source: BE
      region: ${self:provider.region}
      dbSecretName: ${file(config.${self:provider.stage}.json):dbSecretName}
      dbPrefix: ${file(config.${self:provider.stage}.json):dbPrefix}
      logoBucket: ${file(config.${self:provider.stage}.json):logoBucket}
      emailFrom: ${file(config.${self:provider.stage}.json):emailFrom}
      refreshCustomGroupStateMachineArn: ${file(config.${self:provider.stage}.json):refreshCustomGroup}
      commonStepFunction: ${file(config.${self:provider.stage}.json):commonStepFunction}
      experiencePortalUrl: ${file(config.${self:provider.stage}.json):experiencePortalUrl}
      leaveStatusDomainName: ${file(config.${self:provider.stage}.json):leaveStatusDomainName}
      documentsBucketCloudFront: ${file(config.${self:provider.stage}.json):documentsBucketCloudFront}

  wographql:
    handler: src/wohandler.graphql
    timeout: 900 # Lambda timeout
    events:
      - http:
          path: wographql
          method: post
          cors:
            origin: '*'
            headers:
              - Content-Type
              - X-Amz-Date
              - X-Api-Key
              - X-Amz-Security-Token
              - X-Amz-User-Agent
              - authorization
              - org_code
              - user_ip
              - refresh_token
              - partnerid
              - additional_headers
            allowCredentials: false
          authorizer:
            arn: ${file(config.${self:provider.stage}.json):authorizerARN}
            resultTtlInSeconds: 0
            type: request
    environment: # environment variables
      stageName: ${self:provider.stage}
      domainName: ${file(config.${self:provider.stage}.json):domainName}
      source: BE
      region: ${self:provider.region}
      dbSecretName: ${file(config.${self:provider.stage}.json):dbSecretName}
      dbPrefix: ${file(config.${self:provider.stage}.json):dbPrefix}
      emailTo: ${file(config.${self:provider.stage}.json):emailTo}
      webAddress: ${file(config.${self:provider.stage}.json):webAddress}
      sesRegion: ${file(config.${self:provider.stage}.json):sesRegion}
      emailFrom: ${file(config.${self:provider.stage}.json):emailFrom}
      startUpdateWeekOffDateStepFunction: ${file(config.${self:provider.stage}.json):startUpdateWeekOffDateStepFunction}
      leaveStatusDomainName: ${file(config.${self:provider.stage}.json):leaveStatusDomainName}
      bulkInviteEmployeesStepFunction: ${file(config.${self:provider.stage}.json):bulkInviteEmployeesStepFunction}
      customDomainName: ${file(config.${self:provider.stage}.json):customDomainName}
      commonStepFunction: ${file(config.${self:provider.stage}.json):commonStepFunction}
      documentsBucketCloudFront: ${file(config.${self:provider.stage}.json):documentsBucketCloudFront}

  external:
    handler: src/externalhandler.graphql
    timeout: 900 # Lambda timeout
    events:
      - http:
          path: external
          method: post
          cors:
            origin: '*'
            headers:
              - Content-Type
              - X-Amz-Date
              - X-Api-Key
              - X-Amz-Security-Token
              - X-Amz-User-Agent
              - org_code
              - user_ip
              - partnerid
              - additional_headers
            allowCredentials: false
    environment: # environment variables
      stageName: ${self:provider.stage}
      domainName: ${file(config.${self:provider.stage}.json):domainName}
      source: BE
      endPoint: external
      region: ${self:provider.region}
      dbSecretName: ${file(config.${self:provider.stage}.json):dbSecretName}
      dbPrefix: ${file(config.${self:provider.stage}.json):dbPrefix}
      logoBucket: ${file(config.${self:provider.stage}.json):logoBucket}
      experiencePortalUrl: ${file(config.${self:provider.stage}.json):experiencePortalUrl}
      leaveStatusDomainName: ${file(config.${self:provider.stage}.json):leaveStatusDomainName}
      documentsBucketCloudFront: ${file(config.${self:provider.stage}.json):documentsBucketCloudFront}

  refreshCustomEmpGroups:
    handler: src/stepFunction/refreshCustomEmpGroups.refreshCustomEmpGroups
    memorySize: 3008
    timeout: 900
    environment:
      stageName: ${self:provider.stage}
      domainName: ${file(config.${self:provider.stage}.json):domainName}
      source: BE
      region: ${self:provider.region}
      dbSecretName: ${file(config.${self:provider.stage}.json):dbSecretName}
      dbPrefix: ${file(config.${self:provider.stage}.json):dbPrefix}
      emailTo: ${file(config.${self:provider.stage}.json):emailTo}
      sesRegion: ${file(config.${self:provider.stage}.json):sesRegion}
      emailFrom: ${file(config.${self:provider.stage}.json):emailFrom}
      webAddress: ${file(config.${self:provider.stage}.json):webAddress}
      commonStepFunction: ${file(config.${self:provider.stage}.json):commonStepFunction}

  startUpdateWeekOffDate:
    handler: src/stepFunction/startUpdateWeekOffDate.startUpdateWeekOffDate
    memorySize: 3008
    timeout: 900
    environment:
      stageName: ${self:provider.stage}
      domainName: ${file(config.${self:provider.stage}.json):domainName}
      source: BE
      region: ${self:provider.region}
      dbSecretName: ${file(config.${self:provider.stage}.json):dbSecretName}
      dbPrefix: ${file(config.${self:provider.stage}.json):dbPrefix}
      emailTo: ${file(config.${self:provider.stage}.json):emailTo}
      sesRegion: ${file(config.${self:provider.stage}.json):sesRegion}
      emailFrom: ${file(config.${self:provider.stage}.json):emailFrom}

  bulkInviteEmployeesStepFunction:
    handler: src/stepFunction/bulkInviteEmployees.bulkInviteEmployees
    memorySize: 3008
    timeout: 900
    environment:
      stageName: ${self:provider.stage}
      domainName: ${file(config.${self:provider.stage}.json):domainName}
      source: BE
      region: ${self:provider.region}
      dbSecretName: ${file(config.${self:provider.stage}.json):dbSecretName}
      dbPrefix: ${file(config.${self:provider.stage}.json):dbPrefix}
      emailTo: ${file(config.${self:provider.stage}.json):emailTo}
      sesRegion: ${file(config.${self:provider.stage}.json):sesRegion}
      emailFrom: ${file(config.${self:provider.stage}.json):emailFrom}
      logoBucket: ${file(config.${self:provider.stage}.json):logoBucket}
      leaveStatusDomainName: ${file(config.${self:provider.stage}.json):leaveStatusDomainName}

  initiateLeaveClosureStepFunction:
    handler: src/stepFunction/initiateLeaveClosure.initiateLeaveClosure
    timeout: 900 # Lambda timeout
    events:
      - schedule:
          name: leaveClosure-appmanagerprep-${self:provider.stage}
          description: 'This rule is used to trigger the process to start the leave closure.'
          rate: cron(30 18 * * ? *) #12AM IST
          enabled: true
      - schedule:
          name: leaveClosure-continuous-trigger-${self:provider.stage}
          description: 'This rule is used to trigger the process to start the leave closure.'
          rate: cron(45/10 18-22 * * ? *) #12.10AM IST
          enabled: true
          input:
            status: Open
    environment:
      stateMachineArn: ${file(config.${self:provider.stage}.json):leaveClosureStepFunction}

  getActiveInstanceForLeaveClosure: #Step 1
    handler: src/stepFunction/getActiveInstanceForLeaveClosure.getActiveInstanceForLeaveClosure
    memorySize: 3008
    timeout: 900
    environment:
      stageName: ${self:provider.stage}
      domainName: ${file(config.${self:provider.stage}.json):domainName}
      source: BE
      region: ${self:provider.region}
      dbSecretName: ${file(config.${self:provider.stage}.json):dbSecretName}
      dbPrefix: ${file(config.${self:provider.stage}.json):dbPrefix}
      sesRegion: ${file(config.${self:provider.stage}.json):sesRegion}

  processLeaveClosure: #Step 2
    handler: src/stepFunction/processLeaveClosure.processLeaveClosure
    memorySize: 3008
    timeout: 900
    environment:
      stageName: ${self:provider.stage}
      domainName: ${file(config.${self:provider.stage}.json):domainName}
      source: BE
      region: ${self:provider.region}
      dbSecretName: ${file(config.${self:provider.stage}.json):dbSecretName}
      dbPrefix: ${file(config.${self:provider.stage}.json):dbPrefix}
      sesRegion: ${file(config.${self:provider.stage}.json):sesRegion}

  initiateSystemBatchStepFunction:
    handler: src/stepFunction/initiateSystemProcess.initiateSystemProcess
    timeout: 900 # Lambda timeout
    events:
      - schedule:
          name: systemProcess-appmanagerprep-${self:provider.stage}
          description: 'This rule is used to trigger the process to start the system process.'
          rate: cron(31 18 * * ? *) #12.1AM
          enabled: true
      - schedule:
          name: systemProcess-continuous-trigger-${self:provider.stage}
          description: 'This rule is used to trigger the process to start the system process.'
          rate: cron(10/10 18-22 * * ? *) #12.10AM
          enabled: true
          input:
            status: Open
    environment:
      stateMachineArn: ${file(config.${self:provider.stage}.json):systemProcessStepFunction}

  getActiveInstancesForSystemProcess: #Step 1
    handler: src/stepFunction/getActiveInstancesForSystemProcess.getActiveInstancesForSystemProcess
    memorySize: 3008
    timeout: 900
    environment:
      stageName: ${self:provider.stage}
      domainName: ${file(config.${self:provider.stage}.json):domainName}
      source: BE
      region: ${self:provider.region}
      dbSecretName: ${file(config.${self:provider.stage}.json):dbSecretName}
      dbPrefix: ${file(config.${self:provider.stage}.json):dbPrefix}
      sesRegion: ${file(config.${self:provider.stage}.json):sesRegion}

  processSystemProcess: #Step 2
    handler: src/stepFunction/processSystemProcess.processSystemProcess
    memorySize: 3008
    timeout: 900
    environment:
      stageName: ${self:provider.stage}
      domainName: ${file(config.${self:provider.stage}.json):domainName}
      source: BE
      region: ${self:provider.region}
      dbSecretName: ${file(config.${self:provider.stage}.json):dbSecretName}
      dbPrefix: ${file(config.${self:provider.stage}.json):dbPrefix}
      sesRegion: ${file(config.${self:provider.stage}.json):sesRegion}
      asyncSyntrumAPIStepFunction: ${file(config.${self:provider.stage}.json):asyncSyntrumAPIStepFunction}
      processAirTicketSummary: ${file(config.${self:provider.stage}.json):processAirTicketSummary}

  processAirTicketSummary:
    handler: src/stepFunction/processAirTicketSummary.processAirTicketSummary
    memorySize: 3008
    timeout: 900
    environment:
      stageName: ${self:provider.stage}
      domainName: ${file(config.${self:provider.stage}.json):domainName}
      region: ${self:provider.region}
      dbSecretName: ${file(config.${self:provider.stage}.json):dbSecretName}
      dbPrefix: ${file(config.${self:provider.stage}.json):dbPrefix}
      sesRegion: ${file(config.${self:provider.stage}.json):sesRegion}
      asyncSyntrumAPIStepFunction: ${file(config.${self:provider.stage}.json):asyncSyntrumAPIStepFunction}

stepFunctions:
  stateMachines:
    leaveClosureStepFunction:
      name: ${opt:stage}-leaveClosureStepFunction
      events:
        - http:
            path: leaveClosureStepFunction
            method: POST
            cors: true
            authorizer:
              arn: ${file(config.${self:provider.stage}.json):authorizerARN}
              resultTtlInSeconds: 0
              type: request
      definition:
        Comment: 'Initiate leave closure.'
        StartAt: getActiveInstanceForLeaveClosure
        States:
          getActiveInstanceForLeaveClosure:
            Type: Task
            Resource: ${file(config.${self:provider.stage}.json):resourceArnPrefix}-getActiveInstanceForLeaveClosure
            Next: FirstStepChoiceState
          FirstStepChoiceState:
            Type: Choice
            Choices:
              - Variable: '$.nextStep'
                StringEquals: Step2
                Next: processLeaveClosure
            Default: EndFunction
          processLeaveClosure:
            Type: Task
            Resource: ${file(config.${self:provider.stage}.json):resourceArnPrefix}-processLeaveClosure
            Next: EndFunction
          EndFunction:
            Type: Pass
            Result: 'Leave closure step function execution completed.'
            End: true

    refreshCustomEmpGroupsStepFunction:
      name: ${opt:stage}-refreshCustomEmpGroupsStepFunction
      events:
        - http:
            path: refreshCustomEmpGroupsStepFunction
            method: POST
            cors: true
            authorizer:
              arn: ${file(config.${self:provider.stage}.json):authorizerARN}
              resultTtlInSeconds: 0
              type: request
      definition:
        Comment: 'Initiate refresh custom employee group.'
        StartAt: processrefreshCustomEmpGroups
        States:
          processrefreshCustomEmpGroups:
            Type: Task
            Resource: ${file(config.${self:provider.stage}.json):resourceArnPrefix}-refreshCustomEmpGroups
            Next: EndFunction
          EndFunction:
            Type: Pass
            Result: 'Refresh custom group step function execution completed.'
            End: true

    startUpdateWeekOffDateStepFunction:
      name: ${opt:stage}-startUpdateWeekOffDateStepFunction
      events:
        - http:
            path: startUpdateWeekOffDateStepFunction
            method: POST
            cors: true
            authorizer:
              arn: ${file(config.${self:provider.stage}.json):authorizerARN}
              resultTtlInSeconds: 0
              type: request
      definition:
        Comment: 'Initiate update week off date.'
        StartAt: startUpdateWeekOffDate
        States:
          startUpdateWeekOffDate:
            Type: Task
            Resource: ${file(config.${self:provider.stage}.json):resourceArnPrefix}-startUpdateWeekOffDate
            Next: EndFunction
          EndFunction:
            Type: Pass
            Result: 'Start update week off update step function execution completed.'
            End: true

    bulkInviteEmployeesStepFunction:
      name: ${opt:stage}-bulkInviteEmployeesStepFunction
      events:
        - http:
            path: bulkInviteEmployeesStepFunction
            method: POST
            cors: true
            authorizer:
              arn: ${file(config.${self:provider.stage}.json):authorizerARN}
              resultTtlInSeconds: 0
              type: request
      definition:
        Comment: "Initiate bulk invite employees."
        StartAt: processbulkInviteEmployees
        States:
          processbulkInviteEmployees:
            Type: Task
            Resource: ${file(config.${self:provider.stage}.json):resourceArnPrefix}-bulkInviteEmployeesStepFunction
            Next: EndFunction
          EndFunction:
            Type: Pass
            Result: "Bulk Invite employees function execution completed."
            End: true
   
    systemProcessStepFunction:
      name: ${opt:stage}-systemProcessStepFunction
      events:
        - http:
            path: systemProcessStepFunction
            method: POST
            cors: true
            authorizer:
              arn: ${file(config.${self:provider.stage}.json):authorizerARN}
              resultTtlInSeconds: 0
              type: request
      definition:
        Comment: 'Initiate leave closure.'
        StartAt: getActiveInstancesForSystemProcess
        States:
          getActiveInstancesForSystemProcess:
            Type: Task
            Resource: ${file(config.${self:provider.stage}.json):resourceArnPrefix}-getActiveInstancesForSystemProcess
            Next: FirstStepChoiceState
          FirstStepChoiceState:
            Type: Choice
            Choices:
              - Variable: '$.nextStep'
                StringEquals: Step2
                Next: processSystemProcess
            Default: EndFunction
          processSystemProcess:
            Type: Task
            Resource: ${file(config.${self:provider.stage}.json):resourceArnPrefix}-processSystemProcess
            Next: EndFunction
          EndFunction:
            Type: Pass
            Result: 'System Process step function execution completed.'
            End: true

    processAirTicketSummary:
      name: ${opt:stage}-processAirTicketSummary
      events:
        - http:
            path: processAirTicketSummary
            method: POST
            cors: true
            authorizer:
              arn: ${file(config.${self:provider.stage}.json):authorizerARN}
              resultTtlInSeconds: 0
              type: request
      definition:
        Comment: "Initiate employees air ticket summary."
        StartAt: processAirTicketEmployeeSummary
        States:
          processAirTicketEmployeeSummary:
            Type: Task
            Resource: ${file(config.${self:provider.stage}.json):resourceArnPrefix}-processAirTicketSummary
            Next: EndFunction
          EndFunction:
            Type: Pass
            Result: "Air Ticket summary employees function execution completed."
            End: true

resources:
  Resources:
    ApiGatewayRestApi: # Map customized api gateway responses
      Type: AWS::ApiGateway::RestApi
      Properties:
        Name: ${self:service}-${self:provider.stage}

    GatewayResponse4XX: # statusCode 4XX series errorcode
      Type: 'AWS::ApiGateway::GatewayResponse'
      Properties:
        ResponseParameters: # Response header to be returned
          gatewayresponse.header.Access-Control-Allow-Origin: "'*'"
          gatewayresponse.header.Access-Control-Allow-Headers: "'*'"
        RestApiId:
          Ref: 'ApiGatewayRestApi'
        ResponseType: DEFAULT_4XX # Response Type (Assigned based on the errors Refer AWS API gateway response documentation)
        ResponseTemplates: # Map customized error response
          application/json: '{ "message": {"message": "Forbidden." } }'

    GatewayResponse401: # statusCode 401
      Type: 'AWS::ApiGateway::GatewayResponse'
      Properties:
        ResponseParameters: # Response header to be returned
          gatewayresponse.header.Access-Control-Allow-Origin: "'*'"
          gatewayresponse.header.Access-Control-Allow-Headers: "'*'"
        RestApiId:
          Ref: 'ApiGatewayRestApi'
        ResponseType: UNAUTHORIZED # Response Type (Assigned based on the errors Refer AWS API gateway response documentation)
        StatusCode: '401' # API gateway default errorcode
        ResponseTemplates: # Map customized error response
          application/json: '{ "message": {"message": "Unauthorized request." } }'

    GatewayResponse5XX: # statusCode 5XX series error code
      Type: 'AWS::ApiGateway::GatewayResponse'
      Properties:
        ResponseParameters: # Response header to be returned
          gatewayresponse.header.Access-Control-Allow-Origin: "'*'"
          gatewayresponse.header.Access-Control-Allow-Headers: "'*'"
        RestApiId:
          Ref: 'ApiGatewayRestApi'
        ResponseType: DEFAULT_5XX # Response Type (Assigned based on the errors Refer AWS API gateway response documentation)
        ResponseTemplates: # Map customized error response
          application/json: '{ "message": {"message": "API gateway timeout." } }'
