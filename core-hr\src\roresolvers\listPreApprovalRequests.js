// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;

// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formName, formIds } = require('../../common/appconstants');

let organizationDbConnection;
module.exports.listPreApprovalRequests = async (parent, args, context, info) => {
    try {
        console.log("Inside listPreApprovalRequests function.")
        let employeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        // get the access rights of the login employee for the accreditation form
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, employeeId, formName.preApprovalRequests, '', 'UI', false,args.formId);
        if (Object.keys(checkRights).length > 0 && checkRights.Role_View === 1) {
            
            let employeeIdArray = [];
            if(args.formId === formIds.preApprovalSelfService){
                employeeIdArray.push(employeeId);
            } else {
                if(checkRights.Employee_Role.toLowerCase() !== 'admin'){
                    if(checkRights.Is_Manager === 1){
                        employeeIdArray = await commonLib.func.getManagerHierarchy(organizationDbConnection, employeeId, 0);
                    } else {
                        employeeIdArray.push(employeeId);
                    }
                }
            }

            return (
                organizationDbConnection(ehrTables.preApprovalRequests)
                    .select('A.Pre_Approval_Id as preApprovalId', 'A.Pre_Approval_Type as preApprovalType', 'A.Employee_Id as employeeId',
                    'A.Duration as duration', 'A.Period as period','A.Over_Time_Hours as overTimeHours', 'A.Start_Date as startDate', 'A.End_Date as endDate', 'A.Approved_On as approvedOn',
                    'A.Total_Days as totalDays', 'A.Status as status', "A.Reason as reason", 'A.Added_On as addedOn','A.Updated_On as updatedOn',
                    'EJ.Work_Schedule','EJ.Designation_Id', 'EJ.Department_Id', 'EJ.Location_Id', 'EJ.EmpType_Id','ET.Employee_Type',
                    'DES.Designation_Name','DEP.Department_Name','L.Location_Name','PD.File_Name as fileName','PD.File_Size as fileSize',
                    organizationDbConnection.raw("(CASE WHEN EJ.User_Defined_EmpId IS NOT NULL THEN EJ.User_Defined_EmpId ELSE A.Employee_Id END) as userDefinedEmpId"),
                    organizationDbConnection.raw("CONCAT_WS(' ',EP.Emp_First_Name, EP.Emp_Middle_Name, EP.Emp_Last_Name) as employeeName"),
                    organizationDbConnection.raw("CONCAT_WS(' ',EPI.Emp_First_Name, EPI.Emp_Middle_Name, EPI.Emp_Last_Name) as addedByName"),
                    organizationDbConnection.raw("CONCAT_WS(' ',EPI3.Emp_First_Name, EPI3.Emp_Middle_Name, EPI3.Emp_Last_Name) as approvedByName"),
                    organizationDbConnection.raw("CONCAT_WS(' ',EPI2.Emp_First_Name, EPI2.Emp_Middle_Name, EPI2.Emp_Last_Name) as updatedByName"),)
                    .from(ehrTables.preApprovalRequests+" as A")
                    .leftJoin(ehrTables.preApprovalDocuments + " as PD","PD.Pre_Approval_Id","A.Pre_Approval_Id")
                    .innerJoin(ehrTables.empJob + " as EJ","EJ.Employee_Id","A.Employee_Id")
                    .innerJoin(ehrTables.empPersonalInfo + " as EP", "EP.Employee_Id", "A.Employee_Id")
                    .leftJoin(ehrTables.empPersonalInfo + " as EPI", "EPI.Employee_Id", "A.Added_By")
                    .leftJoin(ehrTables.empPersonalInfo + " as EPI2", "EPI2.Employee_Id", "A.Updated_By")
                    .leftJoin(ehrTables.empPersonalInfo + " as EPI3", "EPI3.Employee_Id", "A.Approved_By")
                    .leftJoin(ehrTables.designation + " as DES", "DES.Designation_Id", "EJ.Designation_Id")
                    .leftJoin(ehrTables.employeeType + " as ET", "ET.EmpType_Id", "EJ.EmpType_Id")
                    .leftJoin(ehrTables.department + " as DEP", "DEP.Department_Id", "EJ.Department_Id")
                    .leftJoin(ehrTables.location + " as L", "L.Location_Id", "EJ.Location_Id")
                    .where(function () {
                        if (employeeIdArray.length > 0) {
                            this.whereIn('A.Employee_Id', employeeIdArray)
                        }
                    })
                    .then(async (preApprovalRequests) => {
                        //destroy the connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        return { errorCode: "", message: "Pre-approval requests retrieved successfully.", preApprovalRequests: preApprovalRequests };
                    })
                    .catch((err)=>{
                        //Destroy DB connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        console.log('Error in listPreApprovalRequests function .catch block.', err);
                        let errResult = commonLib.func.getError(err, 'CHR0036');
                        throw new ApolloError(errResult.message, errResult.code);
                    })
            )
        }
        else {
            console.log('Employee do not have view access rights');
            throw '_DB0100';
        }

    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in listPreApprovalRequests function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'CHR0036');
        throw new ApolloError(errResult.message, errResult.code);
    }
}
