//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { UserInputError, ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../../common/tablealias');
const { getAttendanceExist, getCompensatoryOffExist, getLeaveExist, getPaySlipExist } = require('../../common/commonfunctions');
const { formName, formIds } = require('../../common/appconstants');
const { validateHolidayInputs } = require('../../common/holidayInputValidation')

module.exports.updateCustomGroupHoliday = async (parent, args, context, info) => {
    console.log('Inside updateCustomGroupHoliday function');
    let organizationDbConnection;
    let validationError = {};
    let loginEmployeeId = context.Employee_Id;
    let orgCode = context.Org_Code
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);

        //Form Access check for updating custom group holiday
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, formName.holidays, '', 'UI');
        if (Object.keys(checkRights).length > 0 && checkRights.Role_Update === 1) {
            validationError = await validateHolidayInputs(args);
            if (Object.keys(validationError).length == 0) {
                let loginEmployeeCurrentDateTime = await commonLib.func.getEmployeeTimeZone(loginEmployeeId, organizationDbConnection, 1);
                return (
                    organizationDbConnection
                        .transaction(async (trx) => {
                            let updateDetails = args
                            let customGroupId = args.Custom_Group_Id
                            //fetch the employeeIds with the customGroupId
                            return (
                                organizationDbConnection(ehrTables.customGroupEmployees)
                                    .select("Employee_Id")
                                    .transacting(trx)
                                    .where('Group_Id', customGroupId)
                                    .then(async (data) => {
                                        //Merge the employeeId with the updateDetails
                                        let result = data.map(a => a.Employee_Id);
                                        updateDetails.Employee_Id = result
                                        updateDetails.Updated_By = loginEmployeeId
                                        updateDetails.Modified_Date = loginEmployeeCurrentDateTime

                                        //validation to check if date is in payslip
                                        let paySlipResult = await getPaySlipExist(updateDetails, organizationDbConnection, orgCode, 1)
                                        if (!paySlipResult) {
                                            throw 'CGH0116'
                                        }
                                        if (paySlipResult.length) {
                                            console.log('Payslip contains date')
                                            throw 'CGH0117'
                                        }

                                        //validation to check if date is in emp_attendance table
                                        let attendanceResult = await getAttendanceExist(updateDetails, organizationDbConnection, 1);
                                        if (!attendanceResult) {
                                            throw 'CGH0105'
                                        }
                                        if (attendanceResult.length) {
                                            console.log('Attendance contains the existing date')
                                            throw 'CGH0103'
                                        }

                                        //validation to check if date is in compensatory table
                                        let compensatoryResult = await getCompensatoryOffExist(updateDetails, organizationDbConnection, 1)
                                        if (!compensatoryResult) {
                                            throw 'CGH0106'
                                        }
                                        if (compensatoryResult.length) {
                                            console.log('Compensatory contains the existing date')
                                            throw 'CGH0103'
                                        }

                                        //validation to check if date is in leave table
                                        let employeeLeaveResult = await getLeaveExist(updateDetails, organizationDbConnection, 1)
                                        if (!employeeLeaveResult) {
                                            throw 'CGH0107'
                                        }
                                        if (employeeLeaveResult.length) {
                                            console.log('Leave contains the existing date')
                                            throw 'CGH0103'
                                        }
                                        
                                        let Holiday_Assign_Id = args.Holiday_Assign_Id
                                        //removing Holiday_Assign_Id, EMployee_ID and Custom_Group_Id
                                        delete updateDetails.Holiday_Assign_Id
                                        delete updateDetails.Custom_Group_Id
                                        delete updateDetails.Employee_Id
                                        delete updateDetails.Old_Date
                                        return (
                                            organizationDbConnection(ehrTables.holidayCustomGroup)
                                                .update(updateDetails)
                                                .where('Holiday_Assign_Id', Holiday_Assign_Id)
                                                .transacting(trx)
                                                .then(() => {
                                                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                                                    return { errorCode: "", message: "Custom group holiday have been updated successfully." };
                                                })
                                        )
                                    })
                            )
                        })
                        .catch((catchError) => {
                            console.log('Error in updateCustomGroupHoliday .catch() block', catchError);
                            let errResult = commonLib.func.getError(catchError, 'CGH0113');
                            //Destroy DB connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            //Return error response
                            throw new ApolloError(errResult.message, errResult.code);
                        })
                );
            }
            else {
                throw 'IVE0000';
            }
        } else {
            console.log('No rights to update custom group holiday');
            throw '_DB0102';
        }
    } catch (mainCatchError) {
        console.log('Error in updateCustomGroupHoliday function main block', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        if (mainCatchError === 'IVE0000') {
            let errResult = commonLib.func.getError('', 'IVE0000');
            console.log('Validation error in updateCustomGroupHoliday function - ', validationError);
            // return response
            throw new UserInputError(errResult.message, { validationError: validationError });
        } else {
            let errResult = commonLib.func.getError(mainCatchError, 'CGH0005');
            // return response
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
}



