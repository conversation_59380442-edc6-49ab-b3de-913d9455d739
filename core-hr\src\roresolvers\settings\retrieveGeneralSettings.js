// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formName, formIds } = require('../../../common/appconstants');

let organizationDbConnection;
module.exports.retrieveGeneralSettings = async (parent, args, context, info) => {
    try {
        console.log("Inside retrieveGeneralSettings function.")
        let employeeId = context.Employee_Id;
        let accessFormId = args.formId ? args.formId : formIds.generalSettings;
        organizationDbConnection = knex(context.connection.OrganizationDb);

        if (!process.env.endPoint || process.env.endPoint?.toLowerCase() !== 'external') {
            let checkRights = await commonLib.func.checkEmployeeAccessRights(
                organizationDbConnection,
                employeeId,
                null,
                '',
                'UI',
                false,
                accessFormId
            );
            if (Object.keys(checkRights).length === 0 || checkRights.Role_View !== 1 || checkRights.Employee_Role?.toLowerCase() !== 'admin') {
                if (!checkRights || checkRights?.Role_View !== 1) {
                    throw '_DB0100';
                }
                else {
                    throw '_DB0109';
                }
            }
        }

        // Query the general_settings table
        let generalSettingsData = await organizationDbConnection(ehrTables.generalSettings + " as GS")
            .select(
                'GS.General_Setting_Id',
                'GS.Page_Title',
                'GS.Favicon_Filename',
                'OD.Report_LogoPath as Company_Logo',
                'GS.Use_Company_Logo_As_Product_Logo',
                'GS.Primary_Color',
                'GS.Secondary_Color',
                'GS.Hover_Color',
                'GS.Table_Header_Color',
                'GS.Table_Header_Text_Color',
                'GS.Career_Banner_Image',
                'GS.Career_Headline_Text',
                'GS.Career_Sub_Headline_Text',
                'GS.Career_Text_Horizontal_Position',
                'GS.Career_Text_Vertical_Position',
                'GS.Career_Banner_Opacity',
                'GS.Career_Headline_Font_Family',
                'GS.Career_Heading_Font_Size',
                'GS.Career_Headline_Font_Color',
                'GS.Career_Sub_Headline_Font_Family',
                'GS.Career_Sub_Headline_Font_Size',
                'GS.Career_Sub_Headline_Font_Color',
                'GS.Career_Logo_Path',
                'GS.Added_On',
                organizationDbConnection.raw("CONCAT_WS(' ', EPI_Added.Emp_First_Name, EPI_Added.Emp_Middle_Name, EPI_Added.Emp_Last_Name) as Added_By"),
                'GS.Updated_On',
                organizationDbConnection.raw("CONCAT_WS(' ', EPI_Updated.Emp_First_Name, EPI_Updated.Emp_Middle_Name, EPI_Updated.Emp_Last_Name) as Updated_By")
            )
            .leftJoin(ehrTables.empPersonalInfo + " as EPI_Added", "EPI_Added.Employee_Id", "GS.Added_By")
            .leftJoin(ehrTables.empPersonalInfo + " as EPI_Updated", "EPI_Updated.Employee_Id", "GS.Updated_By")
            .join(ehrTables.orgDetails + " as OD")
            .first();

        // Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;

        // Return the response
        return {
            errorCode: "",
            message: "General settings retrieved successfully.",
            generalSettings: generalSettingsData || null
        };

    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in retrieveGeneralSettings function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'GS0001');
        throw new ApolloError(errResult.message, errResult.code);
    }
}
