
// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formName,formIds } = require('../../../common/appconstants');

module.exports.retrieveTimeSheetProjectDetails = async (parent, args, context, info) => {
    let organizationDbConnection;
    try {
        let reportees = [];
        console.log("Inside retrieveTimeSheetProjectDetails function.")
        let employeeId = context.Employee_Id;
        let accessFormName = formName.timeSheet;
        let accessFormId= args.selfService===1 ? formIds.timeSheet : formIds.timeSheetMyTeam;
         let individualEmployeeId= args.selfService===1? employeeId: args.employeeId
        organizationDbConnection = knex(context.connection.OrganizationDb);
        // check their rights
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, employeeId, accessFormName, '', 'UI',false,accessFormId);
        if (Object.keys(checkRights).length > 0 && ((checkRights.Role_View === 1 && args.selfService === 1)||(args.selfService===0 && (checkRights.Is_Manager === 1 || checkRights.Employee_Role.toLowerCase() === 'admin')))) {
                if (checkRights.Is_Manager === 1 && checkRights.Employee_Role.toLowerCase() !== 'admin' && args.selfService===0) {
                    let getManagerAccessDetails = await commonLib.func.getManagerHierarchy(organizationDbConnection, employeeId);
                    reportees = reportees.concat(getManagerAccessDetails);
                }
        let [timesheetDetails, activityHoursDetails] = await Promise.all([
            organizationDbConnection(ehrTables.empTimesheet + " as ETS")
                .from(ehrTables.empTimesheet + " as ETS")
                .leftJoin(ehrTables.timesheetHoursTracking + " as THR", "THR.Request_Id", "ETS.Request_Id")
                .leftJoin(ehrTables.projectDetails+" as P", "P.Project_Id", "THR.Project_Id")
                .leftJoin(ehrTables.projectActivities + " as PA", "PA.Project_Activity_Id", "THR.Project_Activity_Id")
                .leftJoin(ehrTables.activitiesMaster + " as AM", "AM.Activity_Id", "PA.Activity_Id")
                .where('ETS.Week_Ending_Date',args.weekendDate)
                .modify(async function (queryBuilder) {
                    if(!args.employeeView){
                        queryBuilder.select(
                    'ETS.*',
                    'THR.Timesheet_Id','THR.Project_Id','THR.Timesheet_Type','THR.Project_Activity_Id','THR.Day1','THR.Day2','THR.Day3','THR.Day4','THR.Day5','THR.Day6','THR.Day7',
                    'THR.Description','THR.Added_Date','THR.Updated_On',
                    'P.Project_Name',
                    'AM.Activity_Name',
                    'AM.Is_Billable',
                    organizationDbConnection.raw("CONCAT_WS(' ', EPI.Emp_First_Name, EPI.Emp_Middle_Name, EPI.Emp_Last_Name) as Added_By"),
                    organizationDbConnection.raw("CONCAT_WS(' ', EP.Emp_First_Name, EP.Emp_Middle_Name, EP.Emp_Last_Name) as Employee_Name"),
                    organizationDbConnection.raw("CONCAT_WS(' ', EPI3.Emp_First_Name, EPI3.Emp_Middle_Name, EPI3.Emp_Last_Name) as Approved_By_Name"),
                    organizationDbConnection.raw("CONCAT_WS(' ', EPI2.Emp_First_Name, EPI2.Emp_Middle_Name, EPI2.Emp_Last_Name) as Updated_By"),
                    )
                    queryBuilder.innerJoin(ehrTables.empPersonalInfo + " as EP", "EP.Employee_Id", "ETS.Employee_Id")
                    queryBuilder.leftJoin(ehrTables.empPersonalInfo + " as EPI", "EPI.Employee_Id", "THR.Added_By")
                    queryBuilder.leftJoin(ehrTables.empPersonalInfo + " as EPI2", "EPI2.Employee_Id", "THR.Updated_By")
                    queryBuilder.leftJoin(ehrTables.empPersonalInfo + " as EPI3", "EPI3.Employee_Id", "ETS.Approver_Id")
                    queryBuilder.andWhere("ETS.Employee_Id", individualEmployeeId)
                    if(args.requestId){
                        queryBuilder.where("ETS.Request_Id", args.requestId)
                    }
                    if(args.status && args.status!==''){
                        queryBuilder.where("ETS.Approval_Status", args.status)
                    }
                       
                }
                else if(args.employeeView) {
                    queryBuilder.select(
                        'ETS.Employee_Id',
                        'EJ.Work_Schedule',
                        'ETS.Approval_Status',
                        'ETS.Request_Id',
                        'EJ.Designation_Id', 'EJ.Department_Id', 'EJ.Location_Id', 'EJ.EmpType_Id','EJ.Service_Provider_Id',
                        organizationDbConnection.raw("(CASE WHEN EJ.User_Defined_EmpId IS NOT NULL THEN EJ.User_Defined_EmpId ELSE ETS.Employee_Id END) as userDefinedEmpId"),
                        organizationDbConnection.raw('SUM(THR.Day1) as Day1'),
                        organizationDbConnection.raw('SUM(THR.Day2) as Day2'),
                        organizationDbConnection.raw('SUM(THR.Day3) as Day3'),
                        organizationDbConnection.raw('SUM(THR.Day4) as Day4'),
                        organizationDbConnection.raw('SUM(THR.Day5) as Day5'),
                        organizationDbConnection.raw('SUM(THR.Day6) as Day6'),
                        organizationDbConnection.raw('SUM(THR.Day7) as Day7'),
                        organizationDbConnection.raw('GROUP_CONCAT(AM.Is_Billable) as Is_Billable'),
                        organizationDbConnection.raw("CONCAT_WS(' ', EP.Emp_First_Name, EP.Emp_Middle_Name, EP.Emp_Last_Name) as Employee_Name"),
                    )
                    queryBuilder.innerJoin(ehrTables.empPersonalInfo + " as EP", "EP.Employee_Id", "ETS.Employee_Id")
                    queryBuilder.leftJoin(ehrTables.empJob + " as EJ", "EJ.Employee_Id", "ETS.Employee_Id")
                    queryBuilder.leftJoin(ehrTables.designation + " as DES", "DES.Designation_Id", "EJ.Designation_Id")
                    queryBuilder.leftJoin(ehrTables.employeeType + " as ET", "ET.EmpType_Id", "EJ.EmpType_Id")
                    queryBuilder.leftJoin(ehrTables.department + " as DEP", "DEP.Department_Id", "EJ.Department_Id")
                    queryBuilder.leftJoin(ehrTables.location + " as L", "L.Location_Id", "EJ.Location_Id")
                    .groupBy('ETS.Request_Id');
                    if(args.selfService===1){
                        queryBuilder.where("ETS.Employee_Id", individualEmployeeId);
                    }
            if (reportees && reportees.length>0) {
                if (checkRights.Is_Manager === 1 && checkRights.Employee_Role === '') {
                   queryBuilder.andWhereNot("ETS.Employee_Id", employeeId);
                   queryBuilder.whereIn("ETS.Employee_Id", reportees)
                   }
               }
                }
                }),
            organizationDbConnection(ehrTables.activityDetailsBytime + " as ADT")
            .select(
                'ADT.Day',
                'ADT.Timesheet_Id',
                organizationDbConnection.raw('GROUP_CONCAT(ADT.Room_Id) as roomIds'),
                organizationDbConnection.raw('GROUP_CONCAT(ADT.Details_Bytime_Id) as detailsBytimeId'),
                organizationDbConnection.raw(
                    "GROUP_CONCAT(CONCAT(ADT.Notes, '^')) as Notes"
                ),
                organizationDbConnection.raw('GROUP_CONCAT(ADT.Start_Time) as Start_Time'),
                organizationDbConnection.raw('GROUP_CONCAT(ADT.End_Time) as End_Time'),
                organizationDbConnection.raw('GROUP_CONCAT(ADT.Total_Hours) as totalhoursIndividual')
              )
              .groupBy('ADT.Day', 'ADT.Timesheet_Id')
        ]).catch((err) => {
            organizationDbConnection ? organizationDbConnection.destroy() : null;
            console.log('Error in retrieveTimeSheetProjectDetails .catch() block', err);
            let errResult = commonLib.func.getError(err, 'CHR0116');
            throw new ApolloError(errResult.message, errResult.code);
        })
        let transformedData;
        if(!args.employeeView && timesheetDetails &&timesheetDetails.length ){
        transformedData=formatTimeSheetData(timesheetDetails,activityHoursDetails);
        }
        else if(!args.employeeView && (timesheetDetails ||timesheetDetails.length<=0 )){
            transformedData=[]
        }
        if (transformedData || args.employeeView) {
            organizationDbConnection ? organizationDbConnection.destroy() : null;
            let data = args.employeeView ? timesheetDetails:transformedData;
     
            return { errorCode: "", message: "timesheet activity details retrieved successfully.", timesheetActivityDetails: JSON.stringify(data) };
        }
         else {
            console.log('Time sheet Activity data not found')
            throw 'CHR0081'
        }
    }
    else{
        if (Object.keys(checkRights).length > 0 && args.selfService === 0) {
            console.log("The employee does not have admin or manager access.");
            throw '_DB0114';
        }
        throw '_DB0100'
    }
    } catch (e) {
          //Destroy DB connection
          organizationDbConnection ? organizationDbConnection.destroy() : null;
          console.log('Error in retrieveTimeSheetProjectDetails function main catch block.', e);
          let errResult = commonLib.func.getError(e, 'CHR0080');
          throw new ApolloError(errResult.message, errResult.code);
    }
}



function formatTimeSheetData(data,data2){
    return (
        data.map((val,index) => {
        if(val.Timesheet_Id===null && val.Request_Id){
        return {
            Request_Id:val.Request_Id,
            Employee_Id:val.Employee_Id,
            Week_Ending_Date:val.Week_Ending_Date,
            Approver_Id:val.Approver_Id,
            Approval_Status:val.Approval_Status,
            Returned_Comment:val.Returned_Comment
        }
    }
        let dayTimeDetails=data2.filter((data2val)=>{
         return data2val.Timesheet_Id===val.Timesheet_Id
        })
         return {
             ...val,
             Day1: {
                 total: val.Day1,
                 details: dayTimeDetails && dayTimeDetails.length? sendDetail(dayTimeDetails,1)[0]:null

             },
             Day2: {
                 total: val.Day2,
                 details: dayTimeDetails && dayTimeDetails.length? sendDetail(dayTimeDetails,2)[0]:null

             },
             Day3: {
                 total: val.Day3,
                 details: dayTimeDetails && dayTimeDetails.length? sendDetail(dayTimeDetails,3)[0]:null

             },
             Day4: {
                 total: val.Day4,
                 details: dayTimeDetails && dayTimeDetails.length? sendDetail(dayTimeDetails,4)[0]:null

             },
             Day5: {
                 total: val.Day5,
                 details: dayTimeDetails && dayTimeDetails.length? sendDetail(dayTimeDetails,5)[0]:null

             },
             Day6: {
                 total: val.Day6,
                 details: dayTimeDetails && dayTimeDetails.length? sendDetail(dayTimeDetails,6)[0]:null

             },
             Day7: {
                 total: val.Day7,
                 details:dayTimeDetails && dayTimeDetails.length? sendDetail(dayTimeDetails,7)[0]:null

             },
         
        }
     })
    )
}
function sendDetail(dayTimeDetails,day){
    return (dayTimeDetails.filter((data)=>(data.Day===day)));
}