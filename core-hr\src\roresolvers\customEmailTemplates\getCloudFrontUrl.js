// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
const { formIds } = require('../../../common/appconstants')


module.exports.getCloudFrontUrl = async (parent, args, context, info) => {
    let organizationDbConnection;
    try {
        console.log("Inside getCloudFrontUrl api");
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let loginEmployeeId = context.Employee_Id;
        let checkRightsForForm = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, null, '', "UI", false,  formIds.customEmailTemplate,
        );
        if(Object.keys(checkRightsForForm).length > 0  && checkRightsForForm.Role_View === 1){
            const formedFileName = `${process.env.domainName}/${context.Org_Code}/Email Template Image Upload/${args.fileName}`;
            let encodedFileName = encodeURIComponent(formedFileName);
            const path = await commonLib.func.getSignedUrl(encodedFileName, process.env.documentsBucketCloudFront);
            //Destroy DB connection
            organizationDbConnection ? organizationDbConnection.destroy() : null;
            return{ message: "Cloudfront url retrieved successfully.", url: path}
        }
        else {
            console.log('This employee do not have view access rights');
            throw '_DB0100';
        }
    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in getCloudFrontUrl function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'SET0022');
        throw new ApolloError(errResult.message, errResult.code);
    }
}