//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { UserInputError,ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../../../common/tablealias');
//Require constants
const { formName } = require('../../../common/appconstants');
//Require validation function
const { numberValidation } = require('../../../common/commonvalidation');

//Retrieve the work schedule and the week off details based on the work schedule id
module.exports.retrieveWorkScheduleAndWeekOff = async (parent, args, context, info) => {
    console.log('Inside retrieveWorkScheduleAndWeekOff() function.');
    let organizationDbConnection;
    let errResult;
    let validationError={};
    try{
        organizationDbConnection = knex(context.connection.OrganizationDb);
        //Get the login employee - work schedule form access
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection,context.Employee_Id,formName.workSchedule,'','UI');
        //Check view rights exist or not                 
        if(Object.keys(checkRights).length>0 && checkRights.Role_View === 1) {
            if(!(args.workScheduleId || numberValidation(args.workScheduleId)) || args.workScheduleId < 1){
                validationError['IVE0150'] = commonLib.func.getError('', 'IVE0150').message;
            }
            //Check validation error exist or not
            if(Object.keys(validationError).length ===0){
                let workScheduleId = args.workScheduleId;
                //Retrieve the work schedule details based on the work schedule id
                return(
                    organizationDbConnection(ehrTables.workSchedule+' as WS')
                    .select('WS.WorkSchedule_Id as workScheduleId','WS.WorkSchedule_Code as workScheduleCode','WS.Title as workSchedule','WS.WorkSchedule_Status as status',
                    'WS.Twodays_Flag as overlappingDays','WS.Check_In_Consideration_Time as shiftMarginStartTimeInMinutes',
                    'WS.Check_Out_Consideration_Time as shiftMarginEndTimeInMinutes',
                    organizationDbConnection.raw(
                    `TIME_FORMAT(WS.Regular_Work_Start_Time, '%H:%i') as 'businessHoursStartTime',`
                    +`TIME_FORMAT(WS.Regular_Work_End_Time, '%H:%i') as 'businessHoursEndTime',`
                    +`TIME_FORMAT(WS.Break_Schedule_Start_Time, '%H:%i') as 'breakScheduleStartTime',`
                    +`TIME_FORMAT(WS.Break_Schedule_End_Time, '%H:%i') as 'breakScheduleEndTime',`
                    +`CONCAT_WS(' ',EPI1.Emp_First_Name,EPI1.Emp_Middle_Name, EPI1.Emp_Last_Name) as 'workScheduleAddedByEmployeeName',`
                    +`CONCAT_WS(' ',EPI2.Emp_First_Name,EPI2.Emp_Middle_Name, EPI2.Emp_Last_Name) as 'workScheduleUpdatedByEmployeeName',`
                    +`(CASE WHEN EJ1.User_Defined_EmpId IS NOT NULL THEN EJ1.User_Defined_EmpId ELSE EPI1.Employee_Id END) as workScheduleAddedBy,`
                    +`(CASE WHEN EJ2.User_Defined_EmpId IS NOT NULL THEN EJ2.User_Defined_EmpId ELSE EPI2.Employee_Id END) as workScheduleUpdatedBy`),
                    'WS.Zone_Id as timeZoneId','TZ.TimeZone_Id as timeZone', 'TZ.Offset_Time as timeZoneOffsetTime',
                    'WS.Target_Effort_In_Hours_Per_Week as targetEffortInHoursPerWeek', 
                    'WS.Regular_Hours_Per_Day as regularHoursPerDay','WS.Overtime_Hours_Per_Day as overtimeHoursPerDay',
                    'WS.Overtime_Threshold_Per_Day as thresholdOverTimeHoursPerDay',
                    'WS.OverTime_Cooling_Period as overtimeCoolingPeriod','WS.Allow_Attendance_Outside_Regular_WorkHours as allowAttendanceOutsideRegularHours',
                    'WS.Early_Check_In_Override as earlyCheckInOverride','WS.Grace_Time_Flag as enableGraceTime',
                    'WS.Check_In_Grace_Time as checkInGraceTime','WS.Check_Out_Time_Buffer as checkOutTimeBuffer',
                    'WS.Description as description', 'WS.Added_On as workScheduleAddedOn', 'WS.Updated_On as workScheduleUpdatedOn')
                    .leftJoin(ehrTables.timezone+' as TZ','WS.Zone_Id','TZ.Zone_Id')
                    .leftJoin(ehrTables.empPersonalInfo+' as EPI1','WS.Added_By','EPI1.Employee_Id')
                    .leftJoin(ehrTables.empJob+' as EJ1','EPI1.Employee_Id','EJ1.Employee_Id')
                    .leftJoin(ehrTables.empPersonalInfo+' as EPI2','WS.Updated_By','EPI2.Employee_Id')
                    .leftJoin(ehrTables.empJob+' as EJ2','EPI2.Employee_Id','EJ2.Employee_Id')
                    .where('WS.WorkSchedule_Id',workScheduleId)
                    .then(workScheduleDetails=>{
                        if(workScheduleDetails.length > 0){
                            //Retrieve the week off details based on the work schedule id
                            return(
                            organizationDbConnection(ehrTables.workscheduleWeekoff+' as WO')
                            .select('WO.Day_Id as dayId','WD.Day_Name as dayName',
                            'WO.Exclude_Last_Week as excludeLastWeek','WO.Added_On as weekOffAddedOn',
                            'WO.Updated_On as weekOffUpdatedOn', organizationDbConnection.raw(
                            `CONCAT_WS(' ',EPI1.Emp_First_Name,EPI1.Emp_Middle_Name, EPI1.Emp_Last_Name) as 'weekOffAddedByEmployeeName',`
                            +`CONCAT_WS(' ',EPI2.Emp_First_Name,EPI2.Emp_Middle_Name, EPI2.Emp_Last_Name) as 'weekOffUpdatedByEmployeeName',`
                            +`GROUP_CONCAT(WO.Week_Number) as 'weekNumberList', GROUP_CONCAT(WO.Duration) as 'durationList',`
                            +`(CASE WHEN EJ1.User_Defined_EmpId IS NOT NULL THEN EJ1.User_Defined_EmpId ELSE EPI1.Employee_Id END) as weekOffAddedBy,`
                            +`(CASE WHEN EJ2.User_Defined_EmpId IS NOT NULL THEN EJ2.User_Defined_EmpId ELSE EPI2.Employee_Id END) as weekOffUpdatedBy`
                            )
                            )
                            .leftJoin(ehrTables.weekdays+' as WD','WO.Day_Id','WD.Day_Id')
                            .leftJoin(ehrTables.empPersonalInfo+' as EPI1','WO.Added_By','EPI1.Employee_Id')
                            .leftJoin(ehrTables.empJob+' as EJ1','EPI1.Employee_Id','EJ1.Employee_Id')
                            .leftJoin(ehrTables.empPersonalInfo+' as EPI2','WO.Updated_By','EPI2.Employee_Id')
                            .leftJoin(ehrTables.empJob+' as EJ2','EPI2.Employee_Id','EJ2.Employee_Id')
                            .where('WO.WorkSchedule_Id',workScheduleId)
                            .groupBy('WO.Day_Id')
                            .then((weekOffDetails)=>{
                                let workScheduleResponse = JSON.stringify(workScheduleDetails[0]);
                                let workScheduleWeekOffResponse = (weekOffDetails.length>0)? weekOffDetails : [];
                                //Destroy DB connection
                                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                //Return success response
                                return { errorCode:'',message:'Work schedule details retrieved successfully.',workScheduleDetails:workScheduleResponse,weekOffDetails:workScheduleWeekOffResponse};
                            })
                            )
                        }else{
                            console.log('Work schedule details do not exist for the work schedule id,',workScheduleId);
                            throw 'CWS0008';
                        }
                    })
                    .catch(catchError => {
                        console.log('Error in retrieveWorkScheduleAndWeekOff() function .catch block',catchError);
                        errResult = commonLib.func.getError(catchError, 'CWS0104');
                        //Destroy DB connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        //Return error response
                        throw new ApolloError(errResult.message,errResult.code);
                    })
                );
            }else{
                throw 'IVE0000';
            }
        }else{
            console.log('Login employee id does not have view access to work schedule form.');
            throw '_DB0100';
        }
    }catch(viewWSWeekOffMainCatchErr) {
        console.log('Error in the retrieveWorkScheduleAndWeekOff() function main catch block. ',viewWSWeekOffMainCatchErr);
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        //If validation error exist
        if (viewWSWeekOffMainCatchErr === 'IVE0000') {
            console.log('Validation error in the retrieveWorkScheduleAndWeekOff() function',validationError);
            errResult = commonLib.func.getError('', 'IVE0000');
            //Return error response
            throw new UserInputError(errResult.message, { validationError: validationError });
        }else{
            errResult = commonLib.func.getError(viewWSWeekOffMainCatchErr, 'CWS0007');
            //Return error response
            throw new ApolloError(errResult.message,errResult.code);
        }
    }
};