// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;

// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');

let organizationDbConnection;
module.exports.listWorkflowDetails = async (parent, args, context, info) => {
    try {
        console.log("Inside listWorkflowDetails function.")
        let employeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        const { formName, formId } = args;
        
        // get the access rights of the login employee for the accreditation form
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, employeeId, formName, '', 'UI');
        if (Object.keys(checkRights).length > 0 && checkRights.Role_View === 1) {
            return (
                organizationDbConnection(ehrTables.workflows)
                    .select('W.Workflow_Id', 'W.Workflow_Name', 'WM.Form_Id')
                    .from(ehrTables.workflows+" as W")
                    .innerJoin(ehrTables.workflowModule + " as WM","W.Workflow_Module_Id","WM.Workflow_Module_Id")
                    .where(function () {
                        if (formId) {
                            this.where('WM.Form_Id', formId)
                        }
                    })
                    .then(async (workflowDetails) => {
                        //destroy the connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        return { errorCode: "", message: "Workflow details retrieved successfully.", workflowDetails: workflowDetails };
                    })
                    .catch((err)=>{
                        //Destroy DB connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        console.log('Error in listWorkflowDetails function .catch block.', err);
                        let errResult = commonLib.func.getError(err, 'CHR0037');
                        throw new ApolloError(errResult.message, errResult.code);
                    })
            )
        }
        else {
            console.log('Employee do not have view access rights');
            throw '_DB0100';
        }

    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in listWorkflowDetails function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'CHR0037');
        throw new ApolloError(errResult.message, errResult.code);
    }
}
