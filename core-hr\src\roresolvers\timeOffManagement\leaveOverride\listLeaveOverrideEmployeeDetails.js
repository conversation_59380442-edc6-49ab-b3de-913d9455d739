//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { UserInputError,ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../../../../common/tablealias');
//Require constants
const { formName,formIds } = require('../../../../common/appconstants');
//Require moment
const moment = require('moment-timezone');
//Require common function
const {checkPartialLeaveClosureExist,getDetailsFromLeaveTable} = require('../../../../common/leaveOverrideCommonFunction');

//Validate the inputs
function validateInputs(args){
    try{
        let validationError = {};
        if(!args.employeeId){
            validationError['IVE0083'] = commonLib.func.getError('', 'IVE0083').message;
        }
        return validationError;
    }catch(error){
        console.log('Error in validateInputs() function main catch block.',error);
        throw error;
    }
}

//Function to get the encashement processed closure pending or not value
function getEncashementProcessedValue(partialLeaveClosureDetailsExist,organizePartialClosureDetails,employeeId,leaveTypeId){
    try{
        let encashmentProcessedResult;
        if(partialLeaveClosureDetailsExist){
            let pendingClosureDetails = organizePartialClosureDetails[`${employeeId}|${leaveTypeId}`];
            encashmentProcessedResult = (pendingClosureDetails && pendingClosureDetails.length ) ? 'Yes' : 'No';
        }else{
            encashmentProcessedResult = 'No'       
        }
        return encashmentProcessedResult;
    }catch(error){
        console.log('Error in getEncashementProcessedValue() function main catch block.',error);
        throw error;
    }
}
//Resolver function to list the leave override employee details
module.exports.listLeaveOverrideEmployeeDetails = async (parent, args, context, info) => {
    console.log('Inside listLeaveOverrideEmployeeDetails function');
    let organizationDbConnection;
    let validationError;
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let orgCode = context.Org_Code;
        let loginEmployeeId = context.Employee_Id;
        let hasAccess = false;

        if (args.formId && args.formId === 333) {
            hasAccess = true;
        } else {
            //Get the leave override access for the login employee id
            let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, formName.leaveOverride, '', 'UI',false,formIds.leaveOverride);
            //If the login employee have the view access
            hasAccess = (Object.keys(checkRights).length > 0 && checkRights.Role_View === 1 && checkRights.Employee_Role.toLowerCase() === 'admin');
        }

        if(hasAccess) {
            validationError = validateInputs(args);
            if(Object.keys(validationError).length === 0){
                let employeeIdToSearch = [args.employeeId];
                return (organizationDbConnection
                .transaction(function (trx) {
                    //Get all the employees from employee eligible leaves table
                    return (
                    organizationDbConnection(ehrTables.empEligibleLeave)
                    .select('Employee_Id as employee_id')
                    .distinct()
                    .whereIn('Employee_Id',employeeIdToSearch)
                    .transacting(trx)
                    .then(async(employeeDetails) => {
                        if(employeeDetails && employeeDetails.length > 0){
                            let currentMonthYear = moment.utc().format("MM,YYYY");
                            let currentMonthArgs = {
                                filterMonthYear : currentMonthYear
                            };
                            let leaveOverrideApplicableEmployees = await commonLib.func.listInactiveEmployeeTillFinalSettlement(organizationDbConnection,orgCode,currentMonthArgs,employeeDetails,"listleaveoverrideemployees");
                            //Get the active employees for the current month
                            if(leaveOverrideApplicableEmployees && leaveOverrideApplicableEmployees.length > 0){
                                let eligibleEmployeeIds = [];
                                //Get the employee ids in an array
                                leaveOverrideApplicableEmployees.map((empDetail => {
                                    eligibleEmployeeIds.push(empDetail.employee_id);
                                }));

                                //Get the leave override form list details
                                return( organizationDbConnection(ehrTables.empEligibleLeave+' as EEL')
                                .select('Eligible_Leave_Id as eligibleLeaveId','EEL.Eligible_Days as currentYearTotalEligibleDays','EEL.No_Of_Days as totalCODays','EEL.Last_CO_Balance as lastCOBalance','EEL.Leave_Override_Reason as leaveOverrideReason',
                                'EEL.Leaves_Taken as leavesTaken','EEL.Leave_Balance as leaveBalance', organizationDbConnection.raw("(IFNULL(EL.Encashed_Days,0)) as encashedDays"),
                                organizationDbConnection.raw("((EEL.Eligible_Days + EEL.No_Of_Days) - (EEL.Leaves_Taken + IFNULL(EL.Encashed_Days, 0))) as calculatedLeaveBalance"),
                                'EEL.LeaveType_Id as leaveTypeId','LT.Leave_Name as leaveType','LT.Carry_Over as carryOver','LT.Carry_Over_Accumulation_Limit as carryOverAccumulationLimit',
                                'DEP.Department_Name as departmentName','LOC.Location_Name as locationName','DES.Designation_Name as designationName','EJ.Employee_Id as employeeId',
                                'EEL.Leave_Closure_Start_Date as leaveClosureStartDate','EEL.Leave_Closure_End_Date as leaveClosureEndDate', organizationDbConnection.raw("(CONCAT_WS(' ',EPI.Emp_First_Name,EPI.Emp_Middle_Name, EPI.Emp_Last_Name)) as employeeName"),
                                organizationDbConnection.raw("(CASE WHEN EJ.User_Defined_EmpId IS NOT NULL THEN EJ.User_Defined_EmpId ELSE EPI.Employee_Id END) as userDefinedEmpId"))
                                .innerJoin(ehrTables.leaveType+' as LT','EEL.LeaveType_Id','=','LT.LeaveType_Id')
                                .leftJoin(ehrTables.encashedLeaves+' as EL',function(){
                                this.on('EEL.Employee_Id','=','EL.Employee_Id')
                                this.on('EEL.LeaveType_Id','=','EL.LeaveType_Id')
                                this.on('EEL.CO_Year','=','EL.EN_Year')
                                })
                                .innerJoin(ehrTables.empPersonalInfo+' as EPI','EEL.Employee_Id','EPI.Employee_Id')
                                .innerJoin(ehrTables.empJob+' as EJ','EPI.Employee_Id', 'EJ.Employee_Id')
                                .innerJoin(ehrTables.designation+' as DES','EJ.Designation_Id','DES.Designation_Id')
                                .innerJoin(ehrTables.department+' as DEP','EJ.Department_Id','DEP.Department_Id')
                                .innerJoin(ehrTables.location+' as LOC','EJ.Location_Id','LOC.Location_Id')
                                .where('LT.Leave_Status','Active')
                                .whereIn('EEL.Employee_Id',eligibleEmployeeIds)
                                .transacting(trx)
                                .then(async(employeeEligibleLeaveResult)=>{
                                    //If the employee details exist
                                    if(employeeEligibleLeaveResult && employeeEligibleLeaveResult.length > 0){
                                        let employeeLeaveDetails = await getDetailsFromLeaveTable(organizationDbConnection,eligibleEmployeeIds);
                                        let partialLeaveClosureEmpDetails = await checkPartialLeaveClosureExist(organizationDbConnection,eligibleEmployeeIds);

                                        let organizePartialClosureDetails = {};
                                        if(partialLeaveClosureEmpDetails.length){
                                            organizePartialClosureDetails = commonLib.func.organizeData(partialLeaveClosureEmpDetails,'Employee_Id','LeaveType_Id');
                                        }
                                        let partialLeaveClosureDetailsExist = (organizePartialClosureDetails && Object.keys(organizePartialClosureDetails).length) ? 1 : 0;

                                        //if there is no leaves in the emp_leaves table
                                        if(employeeLeaveDetails && employeeLeaveDetails.length){
                                            let totalDays = 0;
                                            //Iterate the leave override list details
                                            for(let i=0;i<employeeEligibleLeaveResult.length;i++){
                                                totalDays = 0;
                                                let {employeeId,leaveTypeId,leaveClosureStartDate,leaveClosureEndDate} = employeeEligibleLeaveResult[i];
                                                //Filter the leave detail based on the employee id, leave type id and leave closure date range
                                                const filteredLeaveData = employeeLeaveDetails.filter(leave => {
                                                    return leave.Employee_Id === employeeId &&
                                                        leave.LeaveType_Id === leaveTypeId &&
                                                        leave.Start_Date >= leaveClosureStartDate &&
                                                        leave.End_Date <= leaveClosureEndDate;
                                                });
                                                if(filteredLeaveData && filteredLeaveData.length){
                                                    //Sum the total days
                                                    totalDays = filteredLeaveData.reduce((sum, leave) => sum + leave.Total_Days, 0);
                                                }
                                                employeeEligibleLeaveResult[i].totalAppliedLeaveDays = totalDays;
                                                employeeEligibleLeaveResult[i].encashmentProcessedClosurePending = getEncashementProcessedValue(partialLeaveClosureDetailsExist,organizePartialClosureDetails,employeeId,leaveTypeId);
                                            }
                                            leaveOverrideResult = employeeEligibleLeaveResult;
                                        }else{
                                            leaveOverrideResult = employeeEligibleLeaveResult.map(leave => {
                                                let encashmentProcessedClosurePending = getEncashementProcessedValue(partialLeaveClosureDetailsExist,organizePartialClosureDetails,leave.employeeId,leave.leaveTypeId);
                                            
                                                return {
                                                    ...leave,
                                                    encashmentProcessedClosurePending,
                                                    totalAppliedLeaveDays: 0
                                                };
                                            });
                                        }
                                        return { errorCode: '', message: 'Employee list has been retrieved successfully.', employeeEligibleLeaveDetails: leaveOverrideResult };//Return the response
                                    }else{
                                        return { errorCode: '', message: 'No employee(s) found.', employeeEligibleLeaveDetails: [] };//Return the response
                                    }
                                }))
                            }else{
                                return { errorCode: '', message: 'The employee(s) are resigned. Hence there is no eligible leave to override.', employeeEligibleLeaveDetails: [] };//Return the response
                            }
                        }else{
                            return { errorCode: '', message: 'There is no eligible leaves for the employee(s).', employeeEligibleLeaveDetails: [] };//Return the response
                        }
                    }))
                })
                .then(function (result) {
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    return result;
                })
                .catch(function (catchError) {
                    console.log('Error in listLeaveOverrideEmployeeDetails .catch() block', catchError);
                    let errResult = commonLib.func.getError(catchError, 'CTL0101');
                    //Destroy DB connection
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    throw new ApolloError(errResult.message, errResult.code);
                })
                )
            }else{
                console.log("Invalid input",args);
                throw 'IVE0000';
            }
        }else{
            console.log('Login employee does not have admin access rights');
            throw '_DB0109';
        }
    }catch(error){
        console.log('Error in the listLeaveOverrideEmployeeDetails() function in the main catch block.',error);
        let errResult;
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        if(error==='IVE0000'){
            console.log('Validation error in the listLeaveOverrideEmployeeDetails() function.',validationError);
            errResult = commonLib.func.getError('',error);
            throw new UserInputError(errResult.message,{validationError: validationError});
        }else{
            errResult = commonLib.func.getError(error, 'CTL0001');
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
}