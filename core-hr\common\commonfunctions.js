const { ehrTables } = require('./tablealias');
const moment = require('moment');
const { formIds } = require('./appconstants');
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const AWS = require('aws-sdk');
const axios = require('axios');

async function updateAllLeaveSettings(args, organizationDbConnection, loginEmployeeId) {
    try {
        return (
            organizationDbConnection(ehrTables.leaveSettings)
                .update({
                    'Enable_CAMU_Scheduler': args.Enable_CAMU_Scheduler,
                    'Enable_Workflow': args.Enable_Workflow,
                    'Allow_Upline_Managers_Approval': args.Allow_Upline_Managers_Approval == "Yes" ? 1 : 0,
                    'Enforce_Comment_For_Approval': args.Enforce_Comment_For_Approval,
                    'Enforce_Comment_For_Leave': args.Enforce_Comment_For_Leave,
                    'Enforce_Alternate_Person_For_Leave': args.Enforce_Alternate_Person_For_Leave,
                    'Coverage_For_Alternate_Person': args.Coverage_For_Alternate_Person,
                    'Updated_On': moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                    'Updated_By': loginEmployeeId
                })
                .then((update) => {
                    if (update) {
                        return true
                    } else {
                        throw 'EM0265';
                    }
                })
                .catch((catchError) => {
                    console.log('Error in updateLeaveSettings .catch() block', catchError);
                    throw 'EM0265'
                })
        )
    }
    catch (catchError) {
        console.log('Error in updateLeaveSettings .catch() block', catchError);
        throw 'EM0265'
    }

}

async function fromAttendenceApprovalHtmlDesign(employeeDetails, url) {
    try {
        let htmlTableDesign = '';
        let attendenceApprovalRowDesign = "";
        for (let i = 0; i < employeeDetails.length; i++) {
            let employeeId = employeeDetails[i]['employeeId'];
            let employeeName = employeeDetails[i]['employeeName']
            let noOfOustandingApprovals = employeeDetails[i]['noOfOustandingApprovals'];
            attendenceApprovalRowDesign += `
            <tr style="border-top:1px solid #e2e7ee">
                <td style="font-weight: bold;">${employeeId}</td>
                <td style="font-weight: bold;">${employeeName}</td>
                <td style="font-weight: bold;"><a href=${url}>${noOfOustandingApprovals}</a></td>
            </tr>
            `;
        }
        //Form final html for table design
        htmlTableDesign = `
        <table cellpadding="10" cellspacing="0" style="border-collapse:collapse;color:#260029;font-size:12px" width="95%">
        <tbody>
            <tr>
                <td></td>
            </tr>
            <tr align="left" style="border-top:1px solid #e2e7ee">
                <th style="color: #595C68;">Employee ID</th>
                <th style="color: #595C68;">Employee Name</th>
                <th style="color: #595C68;">No Of Pending Approvals</th>
            </tr>
            ${attendenceApprovalRowDesign}
        </tbody>
        </table>
        `;
        return htmlTableDesign;
    }
    catch (e) {
        console.log('Error in the fromAttendenceApprovalHtmlDesign() function.', e);
        return "";
    }
}

async function getAttendanceExist(holidayData, organizationDbConnection, isEdit) {
    try {
        console.log('Inside getAttendanceExist')
        //from the holidayData array of Object, get the array of holiday dates seperately
        //then check if any of the dates are in the table
        let dateArray = []
        let employeeIdArray = []
        if (isEdit) {
            dateArray.push(holidayData.Old_Date)
            dateArray.push(holidayData.Holiday_Date)
            employeeIdArray = holidayData.Employee_Id
        } else {
            for (let i = 0; i < holidayData.length; i++) {
                dateArray.push(holidayData[i].Holiday_Date)
                if (!employeeIdArray.includes(holidayData[i].Employee_Id)) {
                    employeeIdArray = employeeIdArray.concat(holidayData[i].Employee_Id)
                }
            }
        }
        //get only the employee id attendance and status !== rejected
        return (
            organizationDbConnection(ehrTables.empAttendance)
                .select('Attendance_Date', 'Employee_Id')
                .whereIn('Attendance_Date', dateArray)
                .whereIn('Employee_Id', employeeIdArray)
                .groupBy('Attendance_Date')
                .from(ehrTables.empAttendance)
                .where('Approval_Status', '!=', 'Rejected')
                .then((result) => {
                    return result
                })
                .catch((err) => {
                    console.log('Error while validating attendanceDate', err)
                    return false
                })
        )
    } catch (err) {
        console.log('Error while validating attendanceDate', err)
        return false
    }
}

async function getCompensatoryOffExist(holidayData, organizationDbConnection, isEdit) {
    try {
        console.log('Inside getCompensatoryOffExist')
        //from the holidayData array of Object, get the array of holiday dates seperately
        //then check if any of the dates are in the table
        let dateArray = []
        let employeeIdArray = []
        if (isEdit) {
            dateArray.push(holidayData.Old_Date)
            dateArray.push(holidayData.Holiday_Date)
            employeeIdArray = holidayData.Employee_Id
        } else {
            for (let i = 0; i < holidayData.length; i++) {
                dateArray.push(holidayData[i].Holiday_Date)
                if (!employeeIdArray.includes(holidayData[i].Employee_Id)) {
                    employeeIdArray = employeeIdArray.concat(holidayData[i].Employee_Id)
                }
            }
        }
        return (
            organizationDbConnection(ehrTables.compensatoryOff)
                .select('Compensatory_Date', 'Employee_Id')
                .whereIn('Compensatory_Date', dateArray)
                .whereIn('Employee_Id', employeeIdArray)
                .groupBy('Compensatory_Date')
                .from(ehrTables.compensatoryOff)
                .whereNotIn('Approval_Status', ['Rejected', 'Cancelled'])
                .then((result) => {
                    return result
                })
                .catch((err) => {
                    console.log('Error while validating compensatoryOffDate', err)
                    return false
                })
        )
    } catch (err) {
        console.log('Error while validating compensatoryOffDate', err)
        return false
    }
}


async function getLeaveExist(holidayData, organizationDbConnection, isEdit) {
    try {
        console.log('Inside getLeaveExist')
        //from the holidayData array of Object, get the array of holiday dates seperately
        //then check if any of the dates are in the table
        let dateArray = []
        let employeeIdArray = []
        if (isEdit) {
            dateArray.push(holidayData.Old_Date)
            dateArray.push(holidayData.Holiday_Date)
            employeeIdArray = holidayData.Employee_Id
        } else {
            for (let i = 0; i < holidayData.length; i++) {
                dateArray.push(holidayData[i].Holiday_Date)
                if (!employeeIdArray.includes(holidayData[i].Employee_Id)) {
                    employeeIdArray = employeeIdArray.concat(holidayData[i].Employee_Id)
                }
            }
        }
        let employeeLeaves = []
        let leaveQuery = organizationDbConnection(ehrTables.empLeaves)
            .select('EL.Start_Date', 'EL.End_Date', 'EL.Employee_Id')
            .leftJoin(ehrTables.leaveType + ' as LT', 'LT.LeaveType_Id', 'EL.LeaveType_Id')
            .whereIn('EL.Employee_Id', employeeIdArray)
            .whereNotIn('EL.Approval_Status', ['Rejected', 'Cancelled'])
            .where('LT.Leave_Calculation_Days', 0)
            .from(ehrTables.empLeaves + ' as EL');

        let whereCondition = ''
        for (let i = 0; i < dateArray.length; i++) {
            if (i != 0) {
                whereCondition = whereCondition + " or "
            }
            whereCondition = whereCondition + "'" + dateArray[i] + "' between `Start_Date` and `End_Date`";
        }
        leaveQuery = leaveQuery.where(organizationDbConnection.raw("(" + whereCondition + ")"));
        return (
            await leaveQuery
                .then((result) => {
                    if (result.length) {
                        //get the dates between the two dates

                        let betweenDates = []
                        for (let i = 0; i < result.length; i++) {
                            while (new Date(result[i].Start_Date) <= new Date(result[i].End_Date)) {
                                betweenDates.push(result[i].Start_Date)
                                result[i].Start_Date = moment(result[i].Start_Date).add(1, 'days').format("YYYY-MM-DD");
                            }
                        }
                        //compare betweenDates with dateArray and pass the matching one
                        employeeLeaves = betweenDates.filter(function (obj) {
                            return dateArray.indexOf(obj) !== -1;
                        });
                        return employeeLeaves
                    } else {
                        return result
                    }
                })
                .catch((err) => {
                    console.log('Error while validating employee leaves', err)
                    return false
                })
        )
    } catch (err) {
        console.log('Error while validating employee leaves', err)
        return false
    }
}

async function getShortTimeOffExist(holidayData, organizationDbConnection, isEdit) {
    try {
        console.log('Inside getShortTimeOffExist')
        //from the holidayData array of Object, get the array of holiday dates seperately
        //then check if any of the dates are in the table
        let dateArray = []
        let employeeIdArray = []
        if (isEdit) {
            dateArray.push(holidayData.Old_Date)
            dateArray.push(holidayData.Holiday_Date)
            employeeIdArray = holidayData.Employee_Id
        } else {
            dateArray = holidayData.map((data) => {
                return data.Holiday_Date
            })
            for (let i = 0; i < holidayData.length; i++) {
                if (!employeeIdArray.includes(holidayData[i].Employee_Id)) {
                    employeeIdArray = employeeIdArray.concat(holidayData[i].Employee_Id)
                }
            }
        }
        return (
            organizationDbConnection(ehrTables.shortTimeOff)
                .select('Start_Date_Time')
                .whereIn(organizationDbConnection.raw("LEFT(Start_Date_Time, 10)"), dateArray)
                .whereIn('Employee_Id', employeeIdArray)
                .groupBy('Start_Date_Time')
                .from(ehrTables.shortTimeOff)
                .then((result) => {
                    return result
                })
                .catch((err) => {
                    console.log('Error while validating shorttimeoff', err)
                    return false
                })
        )
    } catch (err) {
        console.log('Error while validating short shorttimeoff', err)
        return false
    }
}

async function getPaySlipExist(holidayData, organizationDbConnection, orgCode, isEdit) {
    try {
        console.log('Inside getPaySlipExist')
        //from the holidayData array of Object, get the array of holiday dates seperately
        //then check if any of the dates are in the table
        let dateArray = []
        let employeeIdArray = []
        if (isEdit) {
            dateArray.push(holidayData.Old_Date)
            dateArray.push(holidayData.Holiday_Date)
            employeeIdArray = holidayData.Employee_Id
        } else {
            for (let i = 0; i < holidayData.length; i++) {
                dateArray.push(holidayData[i].Holiday_Date)
                if (!employeeIdArray.includes(holidayData[i].Employee_Id)) {
                    employeeIdArray = employeeIdArray.concat(holidayData[i].Employee_Id)
                }
            }
        }
        return (
            organizationDbConnection(ehrTables.salaryPayslip)
                .select(organizationDbConnection.raw("DATE_FORMAT(MAX(STR_TO_DATE(Salary_Month, '%m,%Y')), '%m,%Y') AS Max_Date"))
                .whereIn('Employee_Id', employeeIdArray)
                .then(async (data) => {
                    if (data && data.length && data[0].Max_Date) {
                        const arrDates = dateArray.map(str => new Date(str));
                        const minDate = new Date(Math.min(...arrDates));
                        let { Last_SalaryDate } = await retrieveSalaryDates(orgCode, data[0].Max_Date, formIds.holidays)
                        if (Last_SalaryDate) {
                            if (new Date(minDate) > new Date(Last_SalaryDate)) {
                                //if min date from holiday dates is greater than last salary date return empty as it is poosible
                                return []
                            } else {
                                //if no return the min date and handle in main function
                                return [minDate]
                            }
                        } else {
                            return []
                        }
                    } else {
                        return []
                    }
                })
        );
    } catch (err) {
        console.log('Error while validating getPaySlipExist', err)
        return false
    }
}

async function retrieveSalaryDates(orgCode, salaryMonthYear, formId = 0, inputDate) {
    try {
        //Require axios to call an ajax api from nodejs
        const axios = require('axios');
        return new Promise(async (resolve, reject) => {
            try {
                let url = 'https://' + orgCode + '.' + process.env.domainName + process.env.webAddress + '/payroll/salary-payslip/get-salary-day';

                //call API to retrieve the details
                const requestData = {
                    salaryMonthYear: salaryMonthYear,
                    formId: formId,
                    inputDate: inputDate
                }
                const config = {
                    method: 'post',
                    url: url,
                    data: requestData,
                    maxBodyLength: Infinity
                };
                //call API to retrieve the details
                await axios.request(config)
                    .then(async function (successResponse) {
                        // check if response is success or failure
                        if (successResponse && successResponse.data && successResponse.data.salaryDates) {
                            let allSalaryDates = successResponse.data.salaryDates;
                            if (Object.keys(allSalaryDates).length > 0) {
                                resolve(allSalaryDates);
                            } else {
                                reject('PFF0109');
                            }
                        } else {
                            reject('PFF0109');
                        }
                    })
                    .catch(function (errorResponse) {
                        console.log('Error in retrieveSalaryDates() function .catch block', errorResponse);
                        reject('PFF0109');
                    });
            } catch (mainError) {
                console.log('Error in retrieveSalaryDates() function main catch block', mainError);
                reject('PFF0109');
            }
        })
    } catch (error) {
        console.log('Error in retrieveSalaryDates() function main catch block', error);
        reject('PFF0109');
    }
}


async function checkSameHolidayExists(holidayData, organizationDbConnection, isEdit) {
    try {
        console.log('Inside checkSameHolidayExists')
        //from the holidayData array of Object, get the array of holiday dates seperately
        //then check if any of the dates are in the table
        let dateArray = []
        let employeeIdArray = []
        let holidayIdArray = []
        let customGroupIdArray = []
        for (let i = 0; i < holidayData.length; i++) {
            dateArray.push(holidayData[i].Holiday_Date)
            holidayIdArray.push(holidayData[i].Holiday_Id)
            customGroupIdArray.push(holidayData[i].Custom_Group_Id)
            if (!employeeIdArray.includes(holidayData[i].Employee_Id)) {
                employeeIdArray = employeeIdArray.concat(holidayData[i].Employee_Id)
            }
        }

        return (
            organizationDbConnection(ehrTables.holidayCustomGroup)
                .select('HCG.Holiday_Date as Holiday_Date', 'HCG.Holiday_Id as Holiday_Id', 'CGA.Custom_Group_Id as Custom_Group_Id')
                .leftJoin(ehrTables.customGroupAssociated + ' as CGA', 'CGA.Parent_Id', 'HCG.Holiday_Assign_Id')
                .whereIn('HCG.Holiday_Date', dateArray)
                .whereIn('HCG.Holiday_Id', holidayIdArray)
                .whereIn('CGA.Custom_Group_Id', customGroupIdArray)
                .where('CGA.Form_Id', formIds.holidays)
                .from(ehrTables.holidayCustomGroup + ' as HCG')
                .then((result) => {
                    return result
                })
                .catch((err) => {
                    console.log('Error while validating checkSameHolidayExists', err)
                    return false
                })
        )
    } catch (err) {
        console.log('Error while validating checkSameHolidayExists', err)
        return false
    }
}

async function getProjectCoverage(organizationDbConnection) {
    try {
        console.log('Inside getProjectCoverage function.');
        return new Promise(function (resolve, reject) {
            organizationDbConnection(ehrTables.projectSettings)
                .select('Project_Coverage')
                // .from(ehrTables.projectDetails+" as P")
                .then((projectCoverageData) => {
                    //The default project coverage is Organization. So return it when there is no data in the table
                    resolve(projectCoverageData.length > 0 ? (projectCoverageData[0]['Project_Coverage']) : "Organization");
                })
                .catch((err) => {
                    console.log('Error in getProjectCoverage function .catch block.', err);
                    reject('CHR0007');
                })
        })
    } catch (err) {
        console.log('Error in getProjectCoverage function main catch block.', err);
        throw ('CHR0007');
    }
}

async function deleteProjectAdditionalDetails(organizationDbConnection, trx, projectId) {
    try {
        console.log("Inside deleteProjectAdditionalDetails function");
        return (
            organizationDbConnection(ehrTables.empProject)
                .delete()
                .where('Project_Id', projectId)
                .transacting(trx)
                .then(() => {
                    return (
                        organizationDbConnection(ehrTables.customGroupAssociated)
                            .delete()
                            .where('Parent_Id', projectId)
                            .where('Form_Id', formIds.projects)
                            .transacting(trx)
                            .then(() => {
                                return (
                                    organizationDbConnection(ehrTables.projectAccreditationCategoryTypeMapping)
                                        .delete()
                                        .where('Project_Id', projectId)
                                        .transacting(trx)
                                        .then(() => {
                                            return 'success';
                                        })
                                )
                            })
                    )
                })
        )
    } catch (deleteError) {
        console.log('Error in deleteProjectAdditionalDetails function main catch block.', deleteError);
        throw ('CHR0016');
    }
}

async function insertDataInTable(organizationDbConnection, trx, insertData, tableName) {
    try {
        console.log('Inside insertDataInTable function');
        return (
            organizationDbConnection(tableName)
                .insert(insertData)
                .transacting(trx)
                .then(async (result) => {
                    return true;
                }).catch((e) => {
                    console.log('Error in insertDataInTable .catch block', e);
                    throw ('CHR0015');
                })
        )

    } catch (insertError) {
        console.log('Error in insertDataInTable main catch block', insertError);
        throw ('CHR0015');

    }
}


async function checkSameHolidayLocationExists(holidayData, organizationDbConnection, isEdit) {
    try {
        console.log('Inside checkSameHolidayLocationExists')
        //from the holidayData array of Object, get the array of holiday dates seperately
        //then check if any of the dates are in the table
        let dateArray = []
        let holidayIdArray = []
        let locationIdArray = []
        if (isEdit) {
            dateArray.push(holidayData.Holiday_Date)
            holidayIdArray.push(holidayData.Holiday_Id)
            locationIdArray.push(holidayData.Location_Id)
        } else {
            for (let i = 0; i < holidayData.length; i++) {
                dateArray.push(holidayData[i].Holiday_Date)
                holidayIdArray.push(holidayData[i].Holiday_Id)
                locationIdArray.push(holidayData[i].Location_Id)
            }
        }

        return (
            organizationDbConnection(ehrTables.holidayAssignment)
                .select('Holiday_Date', 'Holiday_Id', 'Location_Id')
                .whereIn('Holiday_Date', dateArray)
                .whereIn('Holiday_Id', holidayIdArray)
                .whereIn('Location_Id', locationIdArray)
                .then((result) => {
                    return result
                })
                .catch((err) => {
                    console.log('Error while validating checkSameHolidayLocationExists', err)
                    return false
                })
        )
    } catch (err) {
        console.log('Error while validating checkSameHolidayLocationExists', err)
        return false
    }
}

async function sendNotificationToEmployees(organizationDbConnection, partnerId, employeeData, orgDetails, orgCode, loginEmployeeCurrentDateTime) {
    try {
        let emailData = [];
        let responses = {
            failedEmployeeIdArray: [],
            invitedEmployeeIdArray: []
        }
        let updateUserSignIn = await updateAllowUserSignIn(organizationDbConnection, employeeData)
        if (updateUserSignIn) {
            //loop through the employee data received
            for await (let employee of employeeData) {
                //generate email link if the employee's requestType is Email
                if (employee.requestType === 'Email' || employee.requestType === 'Google' || employee.requestType === 'Microsoft') {

                    //Common Variables
                    let isSigninLinkGenerated = false
                    let signInLink = ''

                    if (employee.requestType === 'Email') {

                        let pageUrl = "https://" + orgCode + "." + process.env.leaveStatusDomainName + "/"
                        const encryptedEmail = Buffer.from(employee.email).toString('base64');
                        let redirectionURL = pageUrl + 'auth?signinEmailLink=1&value=' + encryptedEmail;

                        // generate and send firebase invite link
                        let actionCodeSettings = {
                            url: redirectionURL,
                            // This must be true.
                            handleCodeInApp: true
                        };

                        AWS.config.update({
                            region: process.env.region
                        });

                        const lambda = new AWS.Lambda();

                        // Call firebase signout lambda function
                        const params = {
                            FunctionName: 'FIREBASEADMIN-' + process.env.stageName + '-generateSignInWithEmailLink', // Replace with the name of the target Lambda function
                            InvocationType: 'RequestResponse', // Use 'Event' for asynchronous invocation, 'RequestResponse' for synchronous
                            // Payload: JSON.stringify({ secretKeys,"partnerId":""}) // Payload to send to the target function
                            Payload: JSON.stringify({ partnerId, region: process.env.region, dbSecretName: process.env.dbSecretName, "email": employee.email, "actionCodeSettings": actionCodeSettings }) // Payload to send to the target function
                        };

                        try {
                            const response = await lambda.invoke(params).promise();
                            if (response && response.Payload && JSON.parse(response.Payload) && JSON.parse(response.Payload).errorMessage) {
                                isSigninLinkGenerated = false;
                                // Some error occurred while generating link
                                console.log('Inside sendNotificationToEmployees() Error while generating the signin link for the email ', employee.email, JSON.parse(response.Payload).errorMessage);
                                //error.errorInfo.message
                                //Link is not generated
                                responses.failedEmployeeIdArray.push({
                                    'Employee_Id': employee.employeeId,
                                    'Message': JSON.parse(response.Payload).errorMessage ? JSON.parse(response.Payload).errorMessage : 'Link was not generated',
                                    'Invitation_Status': 'Pending',
                                    'Invited_Time': loginEmployeeCurrentDateTime
                                })
                            } else if (response && response.Payload && JSON.parse(response.Payload) && JSON.parse(response.Payload)) {
                                signInLink = JSON.parse(response.Payload);
                                isSigninLinkGenerated = true;
                            }
                        } catch (error) {
                            // Some error occurred while generating link
                            console.log('Inside sendNotificationToEmployees catch while generating the signin link for the email ', employee.email, error);

                            //Link is not generated
                            responses.failedEmployeeIdArray.push({
                                'Employee_Id': employee.employeeId,
                                'Message': 'Link was not generated',
                                'Invitation_Status': 'Pending',
                                'Invited_Time': loginEmployeeCurrentDateTime
                            })
                        }
                    } else if (employee.requestType === 'Google') {
                        let pageUrl = "https://" + orgCode + "." + process.env.leaveStatusDomainName + "/"
                        signInLink = pageUrl + 'auth?signinGoogleLink=1';
                        isSigninLinkGenerated = true;
                    }
                    else {
                        let pageUrl = "https://" + orgCode + "." + process.env.leaveStatusDomainName + "/"
                        signInLink = pageUrl + 'auth?signinMicrosoftLink=1';
                        isSigninLinkGenerated = true;
                    }

                    // if the link is generated form template
                    if (isSigninLinkGenerated) {
                        emailData.push({
                            'Source': process.env.emailFrom,
                            'Template': 'ActivityTrackerInvite',
                            'Destination': {
                                'ToAddresses': [employee.email]
                            },
                            'TemplateData': JSON.stringify({
                                orgLogo: orgDetails.logoPath ? orgDetails.logoPath : '',
                                employeeName: (employee.lastName) ? employee.firstName + ' ' + employee.lastName : employee.firstName,
                                acceptInviteAPI: signInLink,
                                hrappSupportEmail: orgDetails.hrAdminEmailAddress ? orgDetails.hrAdminEmailAddress : '',
                                emailSubject: 'Invitation to ' + process.env.domainName.toUpperCase(),
                                isFlowTrack: false,
                            }),
                            //This Employee_Id key is for temporary, will be removed while sending
                            'Employee_Id': employee.employeeId
                        });
                    }
                } else {
                    responses.invitedEmployeeIdArray.push({
                        'Employee_Id': employee.employeeId,
                        'Message': 'Allow user sign updated'
                    })
                }
            }
            return { emailData: emailData, responses: responses }
        } else {
            console.log('Error while updation of allow user sign in')
            return false
        }
    } catch (err) {
        console.log('Error in sendNotificationToEmployees function', err)
        return false
    }
}

async function updateAllowUserSignIn(organizationDbConnection, allEmployees) {
    try {
        //Get the mobile employee id
        let mobileEmployeeIds = []
        let emailEmployeeIds = []

        for (let i = 0; i < allEmployees.length; i++) {
            if (allEmployees[i].requestType === "Email" || allEmployees[i].requestType === "Google" || allEmployees[i].requestType === "Microsoft") {
                emailEmployeeIds.push(allEmployees[i].employeeId)
            } else {
                mobileEmployeeIds.push(allEmployees[i].employeeId)
            }
        }

        if (emailEmployeeIds.length) {
            await organizationDbConnection(ehrTables.empPersonalInfo)
                .update({
                    'Allow_User_Signin': 1,
                    'Enable_Sign_In_With_Mobile_No': 0
                })
                .whereIn('Employee_Id', emailEmployeeIds)
                .then((data) => {
                    if (data) {
                        console.log('Allow User Sign In was updated for email based invites')
                    } else {
                        console.log('Something went wrong while updating for email based invites')
                        return false
                    }
                })
                .catch((err) => {
                    console.log('Error in updateAllowUserSignIn .catch() function', err)
                    return false
                })

        }
        if (mobileEmployeeIds.length) {
            return (
                await organizationDbConnection(ehrTables.empPersonalInfo)
                    .update({
                        'Allow_User_Signin': 1,
                        'Enable_Sign_In_With_Mobile_No': 1
                    })
                    .whereIn('Employee_Id', mobileEmployeeIds)
                    .then((data) => {
                        if (data) {
                            return true
                        } else {
                            return false
                        }
                    })
            )
        } else {
            return true
        }
    }
    catch (err) {
        console.log('Error in updateAllowUserSignIn main catch function', err)
        return false
    }
}

async function sendEmailNotifications(params) {
    try {
        AWS.config.update({
            region: process.env.sesRegion
        });
        const ses = new AWS.SES({
            apiVersion: "2010-12-01"
        });

        return (
            await ses.sendTemplatedEmail(params).promise().then(function (data) {
                return { success: true, message: 'Email sent successfully' };
            })
                .catch(function (err) {
                    console.log("Error while sending mail", err.message);
                    return { success: false, message: err.message };
                })
        )
    }
    catch (err) {
        console.log("Error while sending mail", err);
        return { success: false, message: err };
    }
}

async function initializeSdk(secretKeys, partnerId) {
    try {
        let serviceAccountkeys = {};
        if (partnerId && partnerId === 'trulead') {
            serviceAccountkeys = {
                type: secretKeys.trulead_type,
                project_id: secretKeys.trulead_firebase_project_id,
                private_key_id: secretKeys.trulead_firebase_private_key_id,
                private_key: secretKeys.trulead_firebase_privatekey,
                client_email: secretKeys.trulead_firebase_client_email,
                client_id: secretKeys.trulead_firebase_client_id
            };
        } else {
            serviceAccountkeys = {
                type: secretKeys.type,
                project_id: secretKeys.firebase_project_id,
                private_key_id: secretKeys.firebase_private_key_id,
                private_key: secretKeys.firebase_privatekey,
                client_email: secretKeys.firebase_client_email,
                client_id: secretKeys.firebase_client_id
            };
        }
        //Remove /n from the privatekey.Because /n is consider as string here (\\n to \n)
        serviceAccountkeys.private_key = (serviceAccountkeys.private_key).replace(/\\n/g, "\n");
        //Check if Firebase Admin SDK is already initialized, if not, then do it
        if (admin.apps.length == 0) {
            admin.initializeApp({
                credential: admin.credential.cert(serviceAccountkeys)
            });
        }
    } catch (initializeSdkError) {
        console.log('Error in firebase common function - initializeSdk() function main catch block.', initializeSdkError);
        return '';
    }
}

async function revokeRefreshToken(organizationDbConnection, employeeId, context) {
    try {
        return (
            organizationDbConnection(ehrTables.empUser)
                .select('Firebase_Uid')
                .where('Employee_Id', employeeId)
                .then(async (data) => {
                    if (data && data[0].Firebase_Uid) {
                        // Revoke refresh token
                        await commonLib.firebase.revokeRefreshToken(context.partnerid, process.env.region, process.env.dbSecretName, data[0].Firebase_Uid)
                        return true
                    } else {
                        return true
                    }
                })
                .catch((err) => {
                    console.log('Error in revokeRefreshToken .catch() function', err)
                    return false
                })
        )
    } catch (err) {
        console.log('Error in revokeRefreshToken main common function', err)
        return false
    }
}

function getStartAndEndDate(currentDate, period) {
    let startDate, endDate;
    period = period.toLowerCase();
    console.log(currentDate, period)
    try {
        switch (period) {
            case 'weekly':
                const currentDayOfWeek = currentDate.getDay(); // 0 = Sunday, 1 = Monday, ..., 6 = Saturday
                startDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate() - currentDayOfWeek);
                endDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate() + (6 - currentDayOfWeek));
                break;
            case 'monthly':
                startDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
                endDate = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);
                break;
            case 'quarterly':
                const quarterStartMonth = Math.floor(currentDate.getMonth() / 3) * 3;
                startDate = new Date(currentDate.getFullYear(), quarterStartMonth, 1);
                endDate = new Date(currentDate.getFullYear(), quarterStartMonth + 3, 0);
                break;
            case 'half yearly':
                if (currentDate.getMonth() < 6) {
                    startDate = new Date(currentDate.getFullYear(), 0, 1);
                    endDate = new Date(currentDate.getFullYear(), 5, 30);
                } else {
                    startDate = new Date(currentDate.getFullYear(), 6, 1);
                    endDate = new Date(currentDate.getFullYear(), 11, 31);
                }
                break;
            case 'yearly':
            default:
                startDate = new Date(currentDate.getFullYear(), 0, 1);
                endDate = new Date(currentDate.getFullYear(), 11, 31);
                break;
        }
        console.log(moment(startDate).format("YYYY-MM-DD"), moment(endDate).format("YYYY-MM-DD"))
        return { start: moment(startDate).format("YYYY-MM-DD"), end: moment(endDate).format("YYYY-MM-DD") };
    } catch (err) {
        console.log('Error in getPreApprovalsTakenCount catch block', err);
        throw ("CHR0043");
    }


}

async function getPreApprovalsTakenCount(organizationDbConnection, employeeId, preApprovalType, startDate, endDate, preApprovalId = 0, typeOfDay) {
    try {
        typeOfDay = JSON.parse(typeOfDay);
        return (
            organizationDbConnection(ehrTables.preApprovalRequests)
                .select(organizationDbConnection.raw('SUM(A.Total_Days) as preApprovalsTaken'), 'A.Pre_Approval_Type', 'A.Type_Of_Day')
                .from(ehrTables.preApprovalRequests + " as A")
                .where(function () {
                    typeOfDay.forEach((day) => {
                        this.orWhere('A.Type_Of_Day', 'LIKE', `%${day}%`);
                    });
                })
                .where('A.Employee_Id', employeeId)
                .where(function () {
                    this.orWhereBetween('Start_Date', [startDate, endDate])
                    this.orWhereBetween('End_Date', [startDate, endDate])
                })
                .whereNot('Pre_Approval_Id', preApprovalId)
                .whereIn('Status', ['Applied', 'Approved', 'Cancel Applied'])
                .then(async (preApprovalsTaken) => {
                    return preApprovalsTaken.length && preApprovalsTaken[0].preApprovalsTaken && preApprovalsTaken[0].preApprovalsTaken > 0 ? preApprovalsTaken[0].preApprovalsTaken : 0;
                }).catch(err => {
                    console.log('Error in while getting the employee type in addEmployees() function .catch block', err);
                    // return response
                    throw ("CHR0043");
                })
        )

    } catch (err) {
        console.log('Error in getPreApprovalsTakenCount catch block', err);
        throw ("CHR0043");
    }
}
async function getOrganizationCoveragePreApprovalSettings(organizationDbConnection, preApprovalType) {
    try {
        return (
            organizationDbConnection(ehrTables.preApprovalSettings + "as A")
                .select('A.Period as period', 'A.Type_Of_Day as typeOfDay', 'A.No_Of_Pre_Approval_Request as noOfPreApprovalRequest', 'A.Coverage as coverage',
                    'A.Restrict_Sandwich as restrictSandwich', 'A.Restrict_Sandwich_For as restrictSandwichFor', 'A.Workflow_Id as workflowId',
                    'A.Advance_Notification_Days as advanceNotificationDays', 'A.Document_Upload as documentUpload', 'A.Max_Days_For_Document_Upload as maxDaysForDocumentUpload',
                    'A.Max_Days_Allowed_Per_Request as maxDaysAllowedPerRequest')
                .from(ehrTables.preApprovalSettings + " as A")
                .where("A.Pre_Approval_Type", preApprovalType)
                .where('A.Coverage', 'Organization')
                .where('Status', 'Active')
                .then(async (orgSettings) => {
                    return orgSettings.length > 0 ? orgSettings[0] : {};
                })
                .catch((err) => {
                    console.log("Error in getOrganizationCoveragePreApprovalSettings .catch block", err);
                    throw ('CHR0043');
                })
        )
    } catch (err) {
        console.log('Error in getOrganizationCoveragePreApprovalSettings catch block', err);
        throw ('CHR0043');
    }
}

async function getCustomGroupCoveragePreApprovalSettings(organizationDbConnection, preApprovalType, employeeId) {
    try {
        return (
            organizationDbConnection(ehrTables.preApprovalSettings + "as A")
                .select('A.Period as period', 'A.Type_Of_Day as typeOfDay', 'A.No_Of_Pre_Approval_Request as noOfPreApprovalRequest', 'A.Coverage as coverage',
                    'A.Restrict_Sandwich as restrictSandwich', 'A.Restrict_Sandwich_For as restrictSandwichFor', 'A.Workflow_Id as workflowId',
                    'A.Advance_Notification_Days as advanceNotificationDays', 'A.Document_Upload as documentUpload', 'A.Max_Days_For_Document_Upload as maxDaysForDocumentUpload',
                    'A.Max_Days_Allowed_Per_Request as maxDaysAllowedPerRequest')
                .from(ehrTables.preApprovalSettings + " as A")
                .innerJoin(ehrTables.customGroupEmployees + " as CGE", "A.Custom_Group_Id", "CGE.Group_Id")
                .where("CGE.Employee_Id", employeeId)
                .where("A.Pre_Approval_Type", preApprovalType)
                .whereIn("CGE.Type", ['Default', 'AdditionalInclusion'])
                .where('Status', 'Active')
                .then(async (customGroupSettings) => {
                    return customGroupSettings.length > 0 ? customGroupSettings[0] : {};
                })
                .catch((err) => {
                    console.log("Error in getCustomGroupCoveragePreApprovalSettings .catch block", err);
                    throw ('CHR0043');
                })
        )
    } catch (err) {
        console.log('Error in getCustomGroupCoveragePreApprovalSettings catch block', err);
        throw ('CHR0043');
    }
}

async function customGroupRefresh(employeeId, loginEmployeeId, context) {
    try {
        let coreHrRoBaseUrl = "https://" + process.env.customDomainName + '/coreHr/rographql'
        let url = coreHrRoBaseUrl
        let requestBody = {
            "variables": {
                "employeeId": employeeId,
                "logInEmpId": loginEmployeeId,
                "isCustomGroupRefresh": 1,
                "orgCode": context.Org_Code
            },
            "query": "query CommentQuery($employeeId:[Int],$logInEmpId:Int!,$isCustomGroupRefresh:Int,$orgCode:String!) { initiateRefreshCustomEmpGroups (employeeId:$employeeId,logInEmpId:$logInEmpId,isCustomGroupRefresh:$isCustomGroupRefresh,orgCode:$orgCode) { errorCode message }}"
        }
        const apiHeaders = {
            org_code: context.Org_Code,
            Authorization: context.Auth_Token,
            refresh_token: context.refreshToken,
            partnerid: context.partnerid,
        };
        const config = {
            method: 'post',
            url: url,
            maxBodyLength: Infinity,
            data: requestBody,
            headers: apiHeaders
        };
        console.log("Inside callingSunfishesAPI request ", config);
        const response = await axios.request(config);
        if (response.status !== 200) {
            return false
        }
        return true
    }
    catch (error) {
        console.log('Error in customGroupRefresh() function', error)
        return false
    }
}

async function updateDataSetupDashboard(organizationDbConnection, trx, formId, status) {
    try {
        return (
            organizationDbConnection(ehrTables.dataSetupDashboard)
                .update('Status', status)
                .where('Form_Id', formId)
                .modify(function () {
                    if (trx) {
                        this.transacting(trx)
                    }
                })
        )
    } catch (err) {
        console.log('Error in updateDataSetupDashboard', err)
        throw err;
    }
}

async function getCommonFunction(organizationDbConnection, table, whereCondition, selectData) {
    try {
        return (
            organizationDbConnection(table)
                .modify(function () {
                    if (selectData) {
                        this.select(selectData)
                    } else {
                        this.select("*")
                    }
                    if (whereCondition) {
                        this.where(whereCondition)
                    }
                })
                .then((data) => {
                    return data
                })
                .catch((err) => {
                    console.log('Error in getCommonFunction .catch()', err);
                    throw err
                })
        )
    } catch (err) {
        console.log('Error in getCommonFunction main catch()', err);
        throw err
    }
}
async function getWorkingHours(organizationDbConnection, employeeId) {
    try {
        const totalHours = await organizationDbConnection(ehrTables.empJob + " as EJ").select('EJ.Work_Schedule', 'WS.Regular_Hours_Per_Day', 'WS.Overtime_Hours_Per_Day')
            .innerJoin(ehrTables.workSchedule + " as WS", "WS.WorkSchedule_Id", "EJ.Work_Schedule")
            .where('EJ.Employee_Id', employeeId)

        return totalHours;
    } catch (error) {
        console.log('Error fetching leaves:', error);
        throw error;
    }
}
async function getExistingTimesheetData(organizationDbConnection, trx, args) {
    try {
        let data = await organizationDbConnection(ehrTables.empTimesheet)
            .select('Request_Id', 'Approval_Status', 'Process_Instance_Id')
            .transacting(trx)
            .where("Request_Id", args.requestId)
            .where("Approval_Status", 'Applied').catch((err) => {
                console.log(err, "error while getting existing records")
                throw err;
            })
        return data;
    } catch (error) {
        console.log("Error in getExistingTimesheetData function .catch block.", error)
        throw error;
    }
}

async function getTimesheetInstanceData(organizationDbConnection, args, trx) {
    try {
        let data = await organizationDbConnection(ehrTables.timesheetHoursTracking + " as THR")
            .select(organizationDbConnection.raw('SUM(THR.Day1) as Day1'),
                organizationDbConnection.raw('SUM(THR.Day2) as Day2'),
                organizationDbConnection.raw('SUM(THR.Day3) as Day3'),
                organizationDbConnection.raw('SUM(THR.Day4) as Day4'),
                organizationDbConnection.raw('SUM(THR.Day5) as Day5'),
                organizationDbConnection.raw('SUM(THR.Day6) as Day6'),
                organizationDbConnection.raw('SUM(THR.Day7) as Day7'),
                'ETS.*'
            )
            .transacting(trx)
            .leftJoin(ehrTables.empTimesheet + " as ETS", "ETS.Request_Id", "THR.Request_Id")
            .where("THR.Request_Id", args.requestId)
            .groupBy('THR.Request_Id');
        let row = {

        }

        if (data && data.length) {

            row = {
                ...row,
                Request_Id: data[0].Request_Id,
                Employee_Id: data[0].Employee_Id,
                Week_Ending_Date: data[0].Week_Ending_Date,
                Approval_Status: data[0].Approval_Status,
                Process_Instance_Id: data[0].Process_Instance_Id
            };
            row.totalEffort = (data[0].Day1 || 0) + (data[0].Day2 || 0) + (data[0].Day3 || 0) + (data[0].Day4 || 0) + (data[0].Day5 || 0) + (data[0].Day6 || 0) + (data[0].Day7 || 0);
        }
        return row;
    } catch (error) {
        console.log(error, "There was an error inside the catch block of the getTimesheetInstanceData function.")
        throw error;
    }
}
async function getTimeSheetEventId(organizationDbConnection) {
    return (
        await organizationDbConnection(ehrTables.workflows)
            .pluck("WF.Event_Id")
            .where('WFM.Form_Id', 23)
            .innerJoin(ehrTables.workflowModule + ' as WFM', 'WFM.Workflow_Module_Id', 'WF.Workflow_Module_Id ')
            .from(ehrTables.workflows + ' as WF')
            .andWhere("WF.Default_Workflow", 1)
            .then((workflowId) => {

                let val = workflowId[0];
                if (workflowId) return val;
                else return '';

            })
            .catch((catchError) => {
                throw catchError;
            })
    )
}

async function isLocationUsed(organizationDbConnection, locationId, source) {

    const empJobCondition = source === 'edit' ? { Emp_Status: 'Active' } : null;

    const [exInProject, exInSalary, exInSalaryHistory, exInEmpTransfer, exInOrgPolicies,
        exInEmpjob, exInRecruitment, exInAllowance, exInCandidate, exInHoliday, orgDetails, exInCustomGroup,
        jobPostLocation, candidateUrl, serviceProvider, archiveEmpAttendance, archiveEmpAttendanceImport, backupHolidayAssignment,
        branchEmailAddress, employeeHistoryDetails, hourlyWages, invitedVendors, orgPolicyMultiLocDept, professionalTax] =
        await Promise.all([
            countLocationUsage(organizationDbConnection, locationId, ehrTables.projectDetails), countLocationUsage(organizationDbConnection, locationId, ehrTables.salaryDetails),
            countLocationUsage(organizationDbConnection, locationId, ehrTables.auditSalary), countLocationUsage(organizationDbConnection, locationId, ehrTables.empTransfer),
            countLocationUsage(organizationDbConnection, locationId, ehrTables.orgpolicyLocDept), countLocationUsage(organizationDbConnection, locationId, ehrTables.empJob, empJobCondition),
            countLocationUsage(organizationDbConnection, locationId, ehrTables.recruitment), countLocationUsage(organizationDbConnection, locationId, ehrTables.allowances),
            countLocationUsage(organizationDbConnection, locationId, ehrTables.candidateJob, empJobCondition), countLocationUsage(organizationDbConnection, locationId, ehrTables.holidayAssignment),
            organizationDbConnection(ehrTables.orgDetails).select('Location_Id').first(),
            keyExistInCustomGroup(organizationDbConnection, 'Location_Id', locationId),
            countLocationUsage(organizationDbConnection, locationId, ehrTables.jobPostLocation),
            countLocationUsage(organizationDbConnection, locationId, ehrTables.candidateUrl),
            countLocationUsage(organizationDbConnection, locationId, ehrTables.serviceProvider),
            countLocationUsage(organizationDbConnection, locationId, ehrTables.archiveEmpAttendance),
            countLocationUsage(organizationDbConnection, locationId, ehrTables.archiveEmpAttendanceImport),
            countLocationUsage(organizationDbConnection, locationId, ehrTables.backupHolidayAssignment),
            countLocationUsage(organizationDbConnection, locationId, ehrTables.branchEmailAddress),
            countLocationUsage(organizationDbConnection, locationId, ehrTables.employeeHistoryDetails),
            countLocationUsage(organizationDbConnection, locationId, ehrTables.hourlyWages),
            countLocationUsage(organizationDbConnection, locationId, ehrTables.invitedVendors),
            countLocationUsage(organizationDbConnection, locationId, ehrTables.orgPolicyMultiLocDept),
            countLocationUsage(organizationDbConnection, locationId, ehrTables.professionalTax),
        ]);

    if (!exInProject && !exInSalary && !exInSalaryHistory && !exInEmpTransfer && !exInOrgPolicies && !exInEmpjob
        && !exInRecruitment && !exInAllowance && !exInCandidate && !exInCustomGroup && !exInHoliday
        && (!orgDetails.Location_Id || orgDetails.Location_Id != locationId) && !jobPostLocation && !candidateUrl && !serviceProvider
        && !archiveEmpAttendance && !archiveEmpAttendanceImport && !backupHolidayAssignment && !branchEmailAddress && !employeeHistoryDetails
        && !hourlyWages && !invitedVendors && !orgPolicyMultiLocDept && !professionalTax) {
        return 0;
    } else {
        return 1;
    }
}


// Define a helper function to count records in a table by Location_Id
async function countLocationUsage(organizationDbConnection, locationId, tableName, additionalCondition = null) {
    let query = organizationDbConnection(tableName).count('Location_Id as count').where('Location_Id', locationId);
    if (additionalCondition) {
        query = query.andWhere(additionalCondition);
    }
    const result = await query.first();
    return result.count;
}

async function keyExistInCustomGroup(organizationDbConnection, key, value) {
    let exists = 0;
    const getCGRules = await organizationDbConnection(ehrTables.cusEmpGroup).select('Filter_Vue');

    if (getCGRules && getCGRules.length > 0) {
        for (const rule of getCGRules) {
            if (rule) {
                let childrenNode = rule.Filter_Vue;
                if (childrenNode && typeof childrenNode === 'string') {
                    childrenNode = JSON.parse(childrenNode);
                }
                if (childrenNode.children && childrenNode.children.length > 0) {
                    const firstChild = childrenNode.children[0];
                    if (firstChild.type === 'rule') {
                        const query = firstChild.query;
                        if (query && query.operand === key && query.value && query.value[0] === value) {
                            exists += 1;
                        }
                    }
                }
            }
        }
    }
    return exists;
}

async function getCustomForms(appmanagerDbConnection, formId, orgCode) {

    let orgCodeLevelCustomFormDetails = await getCustomFormDetails(appmanagerDbConnection, formId, orgCode);
    if (!orgCodeLevelCustomFormDetails) {
        orgCodeLevelCustomFormDetails = await getCustomFormDetails(appmanagerDbConnection, formId);
    }
    return orgCodeLevelCustomFormDetails;
}


async function getCustomFormDetails(appmanagerDbConnection, formId, orgCode = null) {

    let customFormQuery = appmanagerDbConnection('customization_forms as CF')
        .select('CF.Form_Id', 'CF.Enable', 'CF.New_Form_Name', 'F.Form_Name')
        .leftJoin('forms as F', 'F.Form_Id', 'CF.Form_Id')
        .where('F.Form_Id', formId);
    if (orgCode) {
        customFormQuery = customFormQuery
            .andWhere('CF.Org_Code', orgCode)
            .andWhere('CF.Customization_Applicable_For', 'Specific Organization');
    } else {
        customFormQuery = customFormQuery
            .andWhere('CF.Customization_Applicable_For', 'All Organization');
    }
    const customFormDetails = await customFormQuery.first();
    return customFormDetails;
}

/**
 * Returns the pre-approval form ID based on the given pre-approval type.
 * 
 * @param {string} preApprovalType - The type of pre-approval request (e.g., 'work from home', 'work during week off', 'on duty').
 * @returns {number} The corresponding form ID for the specified pre-approval type. 
 *                   Defaults to 246 if the type is not recognized.
 * @throws Will log an error and rethrow it if any exception occurs.
 */
function getPreApprovalFormId(preApprovalType) {
    try {
        preApprovalType = preApprovalType.toLowerCase();
        let approvalFormId = 0;
        switch (preApprovalType) {
            case 'work from home':
                approvalFormId = 244;
                break;
            case 'work during week off':
                approvalFormId = 245;
                break;
            case 'on duty':
                approvalFormId = 301;
                break;
            default:
                approvalFormId = 246;
                break;
        }
        return approvalFormId;
    } catch (err) {
        console.log('Error in getPreApprovalFormId', err);
        throw err;
    }
}

module.exports = {
    fromAttendenceApprovalHtmlDesign,
    getAttendanceExist,
    getCompensatoryOffExist,
    getLeaveExist,
    getShortTimeOffExist,
    getPaySlipExist,
    checkSameHolidayExists,
    updateAllLeaveSettings,
    getProjectCoverage,
    deleteProjectAdditionalDetails,
    insertDataInTable,
    checkSameHolidayLocationExists,
    sendNotificationToEmployees,
    initializeSdk,
    sendEmailNotifications,
    updateAllowUserSignIn,
    revokeRefreshToken,
    getStartAndEndDate,
    getPreApprovalsTakenCount,
    getOrganizationCoveragePreApprovalSettings,
    getCustomGroupCoveragePreApprovalSettings,
    customGroupRefresh,
    updateDataSetupDashboard,
    retrieveSalaryDates,
    getCommonFunction,
    getWorkingHours,
    getTimesheetInstanceData,
    getTimeSheetEventId,
    getExistingTimesheetData,
    isLocationUsed,
    getCustomForms,
    getPreApprovalFormId
}