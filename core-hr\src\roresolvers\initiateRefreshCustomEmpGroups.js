'use strict';
//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;

// Function to initiate initiateRefreshCustomEmpGroups step function
module.exports.initiateRefreshCustomEmpGroups = async (parent, args, context) => {
    try {
        console.log('Inside initiateRefreshCustomEmpGroups function', args);
        // We will be triggering the step function to refresh the custom group in background process.

        let inputParams = {
            orgCode: args.orgCode,
            partnerid: context.partnerid ? context.partnerid : '',
            employeeId: args.employeeId,
            logInEmpId: args.logInEmpId,
            isCustomGroupRefresh: args.isCustomGroupRefresh,
            updateOrganizationLeaveBalance: args.updateOrganizationLeaveBalance,
            leaveEnforcementConfigValue: args.leaveEnforcementConfigValue,
            isDOJUpdated: args.isDOJUpdated,
            isProbationDateUpdated: args.isProbationDateUpdated,
            isJobDetailsUpdated: args.isJobDetailsUpdated !== undefined ? args.isJobDetailsUpdated : true,
            refreshFromNonEmpAndLeaveForm: args.refreshFromNonEmpAndLeaveForm ? args.refreshFromNonEmpAndLeaveForm : false //send 1 if the endpoint is called other than employee and leave form.because for both forms, it is handled separately
        }
        let triggerRefreshCustomEmpGroups = await commonLib.stepFunctions.triggerStepFunction(process.env.refreshCustomGroupStateMachineArn, 'refreshCustomGroup', '', inputParams);
        console.log('Response after triggering initiateRefreshCustomEmpGroups step function', triggerRefreshCustomEmpGroups);


        return { errorCode: '', message: 'RefreshCustomEmpGroups initiated successfully.' };
    }
    catch (mainCatchError) {
        console.log('Error in initiateRefreshCustomEmpGroups function main catch block.', mainCatchError);
        let errResult = await commonLib.func.getError(mainCatchError, 'EM00118');
        return { errorCode: errResult.code, message: errResult.message };
    }
};