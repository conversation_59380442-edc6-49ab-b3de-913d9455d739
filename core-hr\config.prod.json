{"securityGroupIds": ["sg-0d3854ad69d6e7e09", "sg-0a9a621d864c23783"], "subnetIds": ["subnet-075680669427eff9d", "subnet-0e2510550b4a177a5"], "dbSecretName": "prod/hrapp/pgaccess", "region": "ap-south-1", "lambdaRole": "arn:aws:iam::484056187456:role/LambdaMicroservice", "dbPrefix": "hrapp_", "domainName": "hrapp", "authorizerARN": "arn:aws:lambda:ap-south-1:484056187456:function:ATS-prod-firebaseauthorizer", "customDomainName": "api.hrapp.co", "emailFrom": "<EMAIL>", "emailTo": "<EMAIL>", "sesRegion": "us-west-2", "leaveStatusDomainName": "hrapp.co", "documentsBucketCloudFront": "documents.hrapp.co", "logoBucket": "s3.logos.hrapp.co", "mailNotificationEngine": "arn:aws:states:ap-south-1:484056187456:stateMachine:prod-coreHrMailNotificationEngineStepFunction", "refreshCustomGroup": "arn:aws:states:ap-south-1:484056187456:stateMachine:prod-refreshCustomEmpGroupsStepFunction", "startUpdateWeekOffDateStepFunction": "arn:aws:states:ap-south-1:484056187456:stateMachine:prod-startUpdateWeekOffDateStepFunction", "bulkInviteEmployeesStepFunction": "arn:aws:states:ap-south-1:484056187456:stateMachine:prod-bulkInviteEmployeesStepFunction", "leaveClosureStepFunction": "arn:aws:states:ap-south-1:484056187456:stateMachine:prod-leaveClosureStepFunction", "webAddress": ".co", "systemProcessStepFunction": "arn:aws:states:ap-south-1:484056187456:stateMachine:prod-systemProcessStepFunction", "supportEmail": "<EMAIL>", "resourceArnPrefix": "arn:aws:lambda:ap-south-1:484056187456:function:COREHR-prod", "commonStepFunction": "arn:aws:states:ap-south-1:484056187456:stateMachine:prod-commonStepFunction", "asyncSyntrumAPIStepFunction": "arn:aws:states:ap-south-1:484056187456:stateMachine:prod-asyncSyntrumAPIFunction", "processAirTicketSummary": "arn:aws:states:ap-south-1:484056187456:stateMachine:prod-processAirTicketSummary", "experiencePortalUrl": "https://#REPLACE_ORG_CODE_DOMAIN_NAME#/v3/candidate-portal"}