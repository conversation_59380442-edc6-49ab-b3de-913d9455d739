// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formIds } = require('../../common/appconstants');

module.exports.retrieveOvertimeConfiguration = async (parent, args, context, info) => {
    let organizationDbConnection;
    try {
        console.log("Inside retrieveOvertimeConfiguration function.");
        let employeeId = context.Employee_Id;
        let formId = args.formId || formIds.overTime;
        
        organizationDbConnection = knex(context.connection.OrganizationDb);
        
        // check their rights
        let checkRights = await commonLib.func.checkEmployeeAccessRights(
            organizationDbConnection,
            employeeId,
            '',
            '',
            'UI',
            false,
            formId
        );
        
        if (Object.keys(checkRights).length > 0 && checkRights.Role_View === 1) {
            const data = await organizationDbConnection(ehrTables.overtimeConfiguration + " as OC")
                .select(
                    'OC.*', 
                    'CGA.Custom_Group_Id', 
                    'CEG.Group_Name',
                    organizationDbConnection.raw("CONCAT_WS(' ',EPI.Emp_First_Name, EPI.Emp_Middle_Name, EPI.Emp_Last_Name) as Updated_By"),
                    organizationDbConnection.raw("CONCAT_WS(' ',EPI2.Emp_First_Name,EPI2.Emp_Middle_Name, EPI2.Emp_Last_Name) as Added_By")
                )
                .leftJoin(ehrTables.empPersonalInfo + " as EPI", "EPI.Employee_Id", "OC.Updated_By")
                .leftJoin(ehrTables.empPersonalInfo + " as EPI2", "EPI2.Employee_Id", "OC.Added_By")
                .leftJoin(ehrTables.customGroupAssociated + " as CGA", function() {
                    this.on("OC.Configuration_Id", "=", "CGA.Parent_Id")
                        .andOn("CGA.Form_Id", "=", formId);
                })
                .leftJoin(ehrTables.cusEmpGroup + " as CEG", "CEG.Group_Id", "CGA.Custom_Group_Id");
                
            if (data) {
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                return { 
                    errorCode: "", 
                    message: "Overtime configuration retrieved successfully.", 
                    OvertimeConfiguration: data 
                };
            } else {
                console.log('Overtime configuration data not found');
                throw 'SOT0101';
            }
        } else {
            throw '_DB0100';
        }
    } catch (e) {
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in retrieveOvertimeConfiguration function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'SOT0001');
        throw new ApolloError(errResult.message, errResult.code);
    }
}
