//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib
//Require knex to make DB connection
const knex = require('knex')
//Require apollo server to return error message
const { UserInputError, ApolloError } = require('apollo-server-lambda')
//Require table alias
const { ehrTables } = require('../../../common/tablealias')
const {
  getAttendanceExist,
  getCompensatoryOffExist,
  getLeaveExist,
  checkSameHolidayLocationExists,
  getPaySlipExist,
  updateDataSetupDashboard,
} = require('../../../common/commonfunctions')
const { formName, formIds } = require('../../../common/appconstants')
const {
  validateHolidayInputs
} = require('../../../common/holidayInputValidation')
const moment = require('moment')

module.exports.addLocationHolidays = async (parent, args, context, info) => {
  console.log('Inside addLocationHolidays function')
  let organizationDbConnection
  let validationError = {}
  let loginEmployeeId = context.Employee_Id
  let orgCode = context.Org_Code
  let typeOfValidation = null;
  try {
    organizationDbConnection = knex(context.connection.OrganizationDb)

    //Form Access check for adding location holidays
    let checkRights = await commonLib.func.checkEmployeeAccessRights(
      organizationDbConnection,
      loginEmployeeId,
      formName.holidays,
      '',
      'UI'
    )
    if (Object.keys(checkRights).length > 0 && checkRights.Role_Add === 1) {
      validationError = await validateHolidayInputs(args, 1)
      if (Object.keys(validationError).length == 0) {
        let loginEmployeeCurrentDateTime =
          await commonLib.func.getEmployeeTimeZone(
            loginEmployeeId,
            organizationDbConnection,
            1
          )
        return organizationDbConnection
          .transaction(async (trx) => {
            let updateDetails = args.holidayDataLocation
            let locationIds = []
            //With the updateDetails form the updatable data
            let formedData = updateDetails.map((el) => {
              let form = []
              //Get dates between Start_Date and End_Date
              while (new Date(el.Start_Date) <= new Date(el.End_Date)) {
                form.push({
                  Holiday_Id: el.Holiday_Id,
                  Holiday_Date: el.Start_Date,
                  Mandatory: el.Mandatory,
                  Holiday: el.Holiday,
                  Personal_Choice: el.Personal_Choice,
                  Description: el.Description,
                  Location_Id: el.Location_Id,
                  Added_By: loginEmployeeId,
                  Added_Date: loginEmployeeCurrentDateTime
                })
                el.Start_Date = moment(el.Start_Date)
                  .add(1, 'days')
                  .format('YYYY-MM-DD')
              }
              //get the locationIds to get the employeeIds
              if (!locationIds.includes(el.Location_Id)) {
                locationIds.push(el.Location_Id)
              }
              return form
            })
            //Getting all the multi dimensional array of objects to single array of object
            formedData = formedData.flat()
            //fetch the employeeIds with the location ids
            return organizationDbConnection(ehrTables.empJob)
              .select('Employee_Id')
              .transacting(trx)
              .whereIn('Location_Id', locationIds)
              .then(async (data) => {
                if (data && data.length) {
                  //Merge the employeeId with the formedData
                  let result = data.map((a) => a.Employee_Id)
                  for (let i = 0; i < formedData.length; i++) {
                    formedData[i].Employee_Id = result
                  }

                  //validation to check if same date is added for the location
                  let holidayCheckResult = await checkSameHolidayLocationExists(
                    formedData,
                    organizationDbConnection
                  )
                  if (!holidayCheckResult) {
                    throw 'CGH0114'
                  }
                  if (holidayCheckResult.length) {
                    // It contains same date
                    throw 'CGH0115'
                  }

                  //validation to check if date is in payslip
                  let paySlipResult = await getPaySlipExist(
                    formedData,
                    organizationDbConnection,
                    orgCode
                  )
                  if (!paySlipResult) {
                    throw 'CGH0116'
                  }
                  if (paySlipResult.length) {
                    console.log('Payslip contains date')
                    throw 'CGH0117'
                  }

                  //validation to check if date is in emp_attendance table
                  let attendanceResult = await getAttendanceExist(
                    formedData,
                    organizationDbConnection
                  )
                  if (!attendanceResult) {
                    throw 'CGH0105'
                  }
                  if (attendanceResult.length) {
                    typeOfValidation = "attendance"
                    //It contains data
                    throw 'CGH0103'
                  }

                  //validation to check if date is in compensatory table
                  let compensatoryResult = await getCompensatoryOffExist(
                    formedData,
                    organizationDbConnection
                  )
                  if (!compensatoryResult) {
                    throw 'CGH0106'
                  }
                  if (compensatoryResult.length) {
                    typeOfValidation = "compensatory"
                    throw 'CGH0103'
                  }

                  //validation to check if date is in leave table
                  let employeeLeaveResult = await getLeaveExist(
                    formedData,
                    organizationDbConnection
                  )
                  if (!employeeLeaveResult) {
                    throw 'CGH0107'
                  }
                  if (employeeLeaveResult.length) {
                    typeOfValidation = "leave"
                    throw 'CGH0103'
                  }
                }
                //getting deep copy of holidayAssignedData
                let holidayAssignedData = JSON.parse(JSON.stringify(formedData))
                //Remove Employee_Id and Location_Id
                for (let i = 0; i < holidayAssignedData.length; i++) {
                  delete holidayAssignedData[i].Employee_Id
                }
                let insertResult = await insertLocationHolidays(
                  organizationDbConnection,
                  holidayAssignedData,
                  trx
                )
                if (insertResult) {
                  organizationDbConnection
                    ? organizationDbConnection.destroy()
                    : null
                  return {
                    errorCode: '',
                    message:
                      'Location holidays have been inserted successfully.'
                  }
                } else {
                  console.log(
                    'Error while inserting data to location holidays.'
                  )
                  throw 'CGH0012'
                }
              })
          })
          .catch((catchError) => {
            console.log(
              'Error in addLocationHolidays .catch() block',
              catchError
            )
            let errResult = commonLib.func.getError(catchError, 'CGH0012')
            //Destroy DB connection
            organizationDbConnection ? organizationDbConnection.destroy() : null
            //Return error response
            throw new ApolloError(errResult.message, errResult.code,  {typeOfValidation})
          })
      } else {
        throw 'IVE0000'
      }
    } else {
      console.log('No rights to add location holidays')
      throw '_DB0101'
    }
  } catch (mainCatchError) {
    console.log(
      'Error in addLocationHolidays function main block',
      mainCatchError
    )
    organizationDbConnection ? organizationDbConnection.destroy() : null
    if (mainCatchError === 'IVE0000') {
      let errResult = commonLib.func.getError('', 'IVE0000')
      console.log(
        'Validation error in addLocationHolidays function - ',
        validationError
      )
      // return response
      throw new UserInputError(errResult.message, {
        validationError: validationError
      })
    } else {
      let errResult = commonLib.func.getError(mainCatchError, 'CGH0011')
      // return response
      throw new ApolloError(errResult.message, errResult.code, {typeOfValidation})
    }
  }
}

async function insertLocationHolidays(
  organizationDbConnection,
  holidayAssignedData,
  trx
) {
  return organizationDbConnection(ehrTables.holidayAssignment)
    .insert(holidayAssignedData)
    .transacting(trx)
    .then(async(data) => {
      if (!data) {
        return false
      } else {
        //Update the datasetup_dashboard status
        await updateDataSetupDashboard(organizationDbConnection, trx, formIds.holidays, 'Completed')
        return true
      }
    })
}
