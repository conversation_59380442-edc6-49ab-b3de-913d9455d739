const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const knex = require('knex');
const { ApolloError } = require('apollo-server-lambda');
const { ehrTables } = require('../../../common/tablealias');
const { formName } = require('../../../common/appconstants');
const moment = require('moment');

module.exports.updateEmployeeIdPrefixConfig = async (_, args, context) => {
    let organizationDbConnection;
    let errResult;
    let validationError = {};
    try {

        let loginEmployeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);

        // Check access rights
        let checkRights = await commonLib.func.checkEmployeeAccessRights(
            organizationDbConnection,
            loginEmployeeId,
            formName.employeeIdPrefix,
            '',
            'UI',
            false,
            args.formId
        );
        // Check if user has update rights and is an admin
        if (Object.keys(checkRights).length > 0 && checkRights.Role_Update === 1) {
            // Check if user is admin
            if(checkRights && checkRights.Employee_Role && checkRights.Employee_Role.toLowerCase() !== 'admin'){
                throw '_DB0109';
            }
            if (!['Organization', 'ServiceProvider'].includes(args.configLevel)) {
                validationError['IVE0594'] = commonLib.func.getError('IVE0594', '').message;
                throw 'IVE0000';
            }

            const currentConfig = await organizationDbConnection(ehrTables.empPrefixConfig)
                .where('Emp_Prefix_Config_Id', args.empPrefixConfigId)
                .first();

            if (currentConfig && currentConfig.Config_Level !== args.configLevel) {


                let conflictingRecords;

                if (args.configLevel === 'Organization') {
                    conflictingRecords = await organizationDbConnection(ehrTables.empPrefixSettings)
                        .where('Status', 'Active')
                        .whereNotNull('Service_Provider_Id')
                        .first();

                    if (conflictingRecords) {
                        throw 'EMP0008';
                    }
                } else {

                    conflictingRecords = await organizationDbConnection(ehrTables.empPrefixSettings)
                        .where('Status', 'Active')
                        .whereNull('Service_Provider_Id')
                        .first();

                    if (conflictingRecords) {
                        throw 'EMP0009';
                    }
                }
            }
            const currentTimestamp = moment().utc().format('YYYY-MM-DD HH:mm:ss');

            const configData = {
                Is_Enabled: args.isEnabled,
                Config_Level: args.configLevel,
                Updated_By: loginEmployeeId,
                Updated_On: currentTimestamp
            };
            let systemLogParams = {
                userIp: context.User_Ip,
                employeeId: loginEmployeeId,
                organizationDbConnection,
                message: 'Employee ID prefix configuration updated successfully'
            };

            return (
                await organizationDbConnection.transaction(async (trx) => {
                    const updateResult = await organizationDbConnection(ehrTables.empPrefixConfig)
                        .where('Emp_Prefix_Config_Id', args.empPrefixConfigId)
                        .update(configData)
                        .transacting(trx);

                    if (!updateResult) {
                        throw 'EMP0006';
                    }
                    await commonLib.func.createSystemLogActivities(systemLogParams);
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    return {
                        errorCode: '',
                        message: 'Employee ID prefix configuration updated successfully.'
                    };
                })
            );
        } else {

            throw ('_DB0102');
        }
    } catch (error) {
        if (error === 'IVE0000') {
            errResult = commonLib.func.getError('', 'IVE0000');
            console.log(
                'Validation error in updateEmployeeIdPrefixConfig function - ',
                validationError
            );
            // return response
            throw new UserInputError(errResult.message, {
                validationError: validationError
            });
        }
        else{
        errResult = commonLib.func.getError(error, 'EMP0007');
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        throw new ApolloError(errResult.message, errResult.code);
        }
    }
};
