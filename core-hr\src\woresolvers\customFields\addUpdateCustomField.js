// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../common/tablealias');
//Require apollo server to return error message
const { ApolloError, UserInputError } = require('apollo-server-lambda');
// require common constant files
const { formIds, formName, systemLogs } = require('../../../common/appconstants');
//require input validator
const { validateCommonRuleInput } = require('../../../common/inputValidations');
//require moment
const moment = require('moment');

//function to add/update the custom field
let organizationDbConnection;
let inputValidationError = {}
module.exports.addUpdateCustomField = async (parent, args, context, info) => {
    try {
        console.log("Inside addUpdateCustomField function.")
        let employeeId = context?.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);

        // check their access rights
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, employeeId, null, null, 'ui', null, formIds.customFields);
        if (checkRights && Object.keys(checkRights).length > 0) {
            //If not admin
            if (checkRights.Employee_Role?.toLowerCase() !== 'admin') {
                throw '_DB0109'
            }
            //If update, but no update access
            if (args.Custom_Field_Id && !checkRights.Role_Update) {
                throw '_DB0102';
            }
            //If add, but no add access
            else if (!args.Custom_Field_Id && !checkRights.Role_Add) {
                throw '_DB0101';
            }
        } else {
            // No access
            throw '_DB0111'
        }

        // Input validation
        inputValidationError = await performInputValidations(organizationDbConnection, args);
        if (inputValidationError && Object.keys(inputValidationError)?.length) {
            throw 'IVE0000'
        }

        await organizationDbConnection.transaction(async (trx) => {
            const currentTimestamp = moment().utc().format('YYYY-MM-DD HH:mm:ss');

            const prepareCustomFieldData = (isUpdate = false) => ({
                Custom_Field_Name: args.Custom_Field_Name,
                Custom_Field_Type: args.Custom_Field_Type,
                Min_Validation: args.Min_Validation,
                Max_Validation: args.Max_Validation,
                Validation_Id: args.Validation_Id ? args.Validation_Id : null,
                Url_Link: args.Url_Link?.length ? args.Url_Link : null,
                Dropdown_Values: args.Dropdown_Values?.length ? JSON.stringify(args.Dropdown_Values) : null,
                Roles_Id: args.Roles_Id?.length ? JSON.stringify(args.Roles_Id) : null,
                Visibility_Condition: args.Visibility_Condition ? JSON.stringify(args.Visibility_Condition) : null,
                ...(isUpdate
                    ? { Updated_By: employeeId, Updated_On: currentTimestamp }
                    : { Added_By: employeeId, Added_On: currentTimestamp }),
            });

            const insertCustomFieldAssociatedForms = async (customFieldId) => {
                const associatedForms = args.Form_Id_Associated.map((form) => ({
                    Custom_Field_Id: customFieldId,
                    Form_Id: form.Form_Id,
                    Mandatory: form.Mandatory,
                    Integration_Mapping_Key: form.Integration_Mapping_Key ? form.Integration_Mapping_Key : null,
                }));

                await organizationDbConnection(ehrTables.customFieldAssociatedForms)
                    .insert(associatedForms)
                    .transacting(trx);
            };

            if (args.Custom_Field_Id) {
                // Update custom field
                await organizationDbConnection(ehrTables.customFields)
                    .update(prepareCustomFieldData(true))
                    .where('Custom_Field_Id', args.Custom_Field_Id)
                    .transacting(trx);

                // Remove and re-insert associated forms
                await organizationDbConnection(ehrTables.customFieldAssociatedForms)
                    .where('Custom_Field_Id', args.Custom_Field_Id)
                    .delete()
                    .transacting(trx);

                await insertCustomFieldAssociatedForms(args.Custom_Field_Id);
            } else {
                // Insert new custom field
                const [customFieldId] = await organizationDbConnection(ehrTables.customFields)
                    .insert(prepareCustomFieldData())
                    .transacting(trx)

                await insertCustomFieldAssociatedForms(customFieldId);
            }
        });

        // Add System Log
        let systemLogParams = {
            action: args.Custom_Field_Id ? systemLogs.roleUpdate : systemLogs.roleAdd,
            userIp: context.User_Ip,
            employeeId: employeeId,
            formName: formName?.customFields,
            organizationDbConnection: organizationDbConnection,
            message: `${formName?.customFields} was ${args.Custom_Field_Id ? 'updated' : 'added'} - ${args.Custom_Field_Id ? `Custom Field Id - ${args.Custom_Field_Id}` : `Custom Field Name - ${args.Custom_Field_Name}`}`
        };

        //Call function to add the system log
        await commonLib.func.createSystemLogActivities(systemLogParams);

        return { errorCode: "", message: `Custom Field details ${args.Custom_Field_Id ? 'updated' : 'added'} successfully.` };

    }
    catch (e) {
        console.log('Error in addUpdateCustomField function main catch block.', e);
        if (e === 'IVE0000') {
            let errResult = commonLib.func.getError('', 'IVE0000');
            console.log('Validation error in addUpdateCustomField function - ', inputValidationError);
            throw new UserInputError(errResult.message, { validationError: inputValidationError });
        } else {
            let errResult = commonLib.func.getError(e, 'CF00002');
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
    finally {
        //Destroy DB connection
        if (organizationDbConnection) {
            organizationDbConnection.destroy();
        }
    }
}

/**
 * Validates input arguments for custom field operations.
 * 
 * This function performs basic, conditional, and association-related validations
 * on the provided arguments for custom fields. It checks for required fields,
 * enforces rules based on the custom field type, and ensures no duplicate field
 * names exist in the database.
 * 
 * @param {Object} organizationDbConnection - The database connection object for the organization.
 * @param {Object} args - The input arguments containing custom field data.
 * 
 * @returns {Object} - An object containing any validation errors found.
 * 
 * @throws Will throw an error if the validation process encounters an issue.
 */

const performInputValidations = async (organizationDbConnection, args) => {
    try {
        let errors = {};
        // Basic Field Validation
        const fieldRules = {
            'Custom_Field_Name': 'IVE0515',
            ...(args.Min_Validation && { 'Min_Validation': 'IVE0517' }),
            ...(args.Max_Validation && { 'Max_Validation': 'IVE0518' }),
            ...(args.Url_Link?.length && { 'Url_Link': 'IVE0521' }),
        };
        errors = await validateCommonRuleInput(args, fieldRules);

        // Conditional Validations
        if (['text field', 'number', 'text area'].includes(args.Custom_Field_Type?.toLowerCase())) {
            if (args.Min_Validation == null || args.Min_Validation == undefined) errors['IVE0517'] = 'Minimum Length is required';
            if (args.Max_Validation == null || args.Max_Validation == undefined) errors['IVE0518'] = 'Maximum Length is required';
            if (!args.Validation_Id && args.Custom_Field_Type.toLowerCase() !== 'number') {
                errors['IVE0519'] = 'Validation is required';
            }
        } else if (['single choice', 'multiple choice'].includes(args.Custom_Field_Type?.toLowerCase())) {
            if (!args.Dropdown_Values?.length) errors['IVE0520'] = 'Dropdown Values are required';
        } else if (args.Custom_Field_Type?.toLowerCase() === 'url') {
            if (!args.Url_Link?.length) errors['IVE0521'] = 'Url Link is required';
        }

        // Form Id Association Validations
        if (args.Form_Id_Associated?.length) {
            for (let i = 0; i < args.Form_Id_Associated.length; i++) {
                if (!args.Form_Id_Associated[i]?.Form_Id) {
                    errors['IVE0522'] = 'Form Id is required ';
                }
                if (!args.Form_Id_Associated[i]?.Mandatory || !args.Form_Id_Associated[i]?.Mandatory?.length) {
                    errors['IVE0522'] = 'Mandatory is required';
                } else if (!['yes', 'no'].includes(args.Form_Id_Associated[i]?.Mandatory?.toLowerCase())) {
                    errors['IVE0522'] = `Mandatory should be either 'Yes' or 'No'`;
                }

            }
        } else {
            errors['IVE0522'] = 'Form association is required';
        }

        // Check for Duplicate Name
        const duplicateCheck = await organizationDbConnection(ehrTables.customFields)
            .select('Custom_Field_Id')
            .where('Custom_Field_Name', args.Custom_Field_Name)
            .whereNot('Custom_Field_Id', args.Custom_Field_Id || null)
            .first();

        if (duplicateCheck) errors['IVE0515'] = 'Custom Field Name already exists';

        // Check for Duplicate Integration_Mapping_Key
        let formAssociatedData = args.Form_Id_Associated.filter((form) => form.Integration_Mapping_Key);
        if (formAssociatedData?.length > 0) {
            let integrationMappingKeys = await organizationDbConnection(ehrTables.customFieldAssociatedForms)
                .select('Integration_Mapping_Key')
                .whereIn('Form_Id', formAssociatedData.map((form) => form.Form_Id))
                .whereIn('Integration_Mapping_Key', formAssociatedData.map((form) => form.Integration_Mapping_Key))
                .whereNot('Custom_Field_Id', args.Custom_Field_Id || null);

            if (integrationMappingKeys?.length > 0) {
                integrationMappingKeys = integrationMappingKeys.map((key) => key.Integration_Mapping_Key);
                errors['IVE0523'] = `Integration Mapping Key ${integrationMappingKeys.join(', ')} already associated with the same form`;
            }
        }

        return errors;
    } catch (e) {
        console.log('Error in performInputValidations', e);
        throw e;
    }
};
