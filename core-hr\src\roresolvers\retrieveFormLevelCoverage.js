// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formName } = require('../../common/appconstants');


module.exports.retrieveFormLevelCoverage = async (parent, args, context, info) => {
    let organizationDbConnection;
    try {
        console.log("Inside retrieveFormLevelCoverage function.")
        let employeeId = context.Employee_Id;
        let accessFormName = args.formName;
        let formLevelCoverageSettingRetrieved={};
        organizationDbConnection = knex(context.connection.OrganizationDb);
        // check their rights
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, employeeId, accessFormName, '', 'UI');
        if (Object.keys(checkRights).length > 0 && checkRights.Role_View === 1) {
        // check their rights
        return (
            organizationDbConnection(ehrTables.formLevelCoverage + " as FLC")
            .select('FLC.*',"EPI.Emp_First_Name","EPI.Emp_Middle_Name","EPI.Emp_Last_Name", organizationDbConnection.raw("CONCAT_WS(' ',EPI.Emp_First_Name, EPI.Emp_Middle_Name, EPI.Emp_Last_Name) as Updated_By"))
            .leftJoin(ehrTables.empPersonalInfo + " as EPI", "EPI.Employee_Id", "FLC.Updated_By")
            .where('FLC.Form_Id',args.Form_Id)
                .then((data) => {
                    if (data && data.length) {
                        formLevelCoverageSettingRetrieved=data;
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        return { errorCode: "", message: "Form level coverage retrieved successfully.", formLevelCoverage: formLevelCoverageSettingRetrieved };
                    } else {
                        console.log('Form level coverage data not found')
                        throw 'FLC0103'
                    }
                })
                .catch((err) => {
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    console.log('Error in formLevelCoverage .catch() block', err);
                    let errResult = commonLib.func.getError(err, 'FLC0103');
                    throw new ApolloError(errResult.message, errResult.code);
                })
        )
            }
    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in retrieveFormLevelCoverage function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'FLC0002');
        throw new ApolloError(errResult.message, errResult.code);
    }
}
