const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const { validateWithRulesAndReturnMessages } = require('@cksiva09/validationlib/src/validator');
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../common/tablealias');
const { formIds } = require('../../common/appconstants');
const { updateDataSetupDashboard, isLocationUsed, getCustomForms } = require('../../common/commonfunctions');
//Require apollo server to return error message
const { ApolloError, UserInputError } = require('apollo-server-lambda');
const moment = require('moment');

module.exports.addUpdateLocation = async (parent, args, context, info) => {

    console.log("Inside addUpdateLocation() function ");
    let organizationDbConnection, appManagerDbConnection, validationError = {};

    try {

        organizationDbConnection = knex(context.connection.OrganizationDb);
        appManagerDbConnection = knex(context.connection.AppManagerDb);

        let loginEmployeeId = context.Employee_Id;
        let locationId = args.locationId || 0;

        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, '', '', 'UI', false, formIds.locations);

        if (Object.entries(checkRights).length && (!locationId && checkRights.Role_Add === 1) || (locationId && checkRights.Role_Update === 1)) {

            let fieldValidations = [
                {
                    argsName: 'locationName',
                    validationName: 'locationName',
                },
                {
                    argsName: 'street1',
                    validationName: 'street1',
                },
                {
                    argsName: 'street2',
                    validationName: 'street1',
                },
                {
                    argsName: 'description',
                    validationName: 'description',
                }
            ]

            //Insert the phone Only When Data is available
            if (args.phone && args.phone.length) {
                fieldValidations.push({
                    argsName: 'phone',
                    validationName: 'phone',
                })
            }


            fieldValidations.forEach((field) => {
                const validation = validateWithRulesAndReturnMessages(args[field.argsName], field.validationName, field.argsName);
                if (validation !== 'Validation not found' && validation !== true) {
                    validationError[field.argsName] = validation;
                }
            })
            if (!args.street2) {
                delete validationError['street2'];
            }

            let customPincode = await getCustomFields(appManagerDbConnection, 'Pincode', context.Org_Code)
            let customNoPincodeError = 1;
            if (!customPincode || (customPincode.Required && customPincode.Enable)) {
                if (!args.pincode)
                    customNoPincodeError = 0;
            }
            if (Object.entries(validationError).length || !customNoPincodeError) {
                throw 'IVE0000';
            }


            let customFormName = await getCustomForms(appManagerDbConnection, formIds.locations, context.Org_Code);

            let formName = customFormName && customFormName.New_Form_Name ? customFormName.New_Form_Name : 'Locations';

            let isExistLocation = await organizationDbConnection(ehrTables.location).count('Location_Id as count')
                .where('Location_Name', args.locationName).andWhere('Location_Type', args.locationType)
                .modify(query => {
                    if (args.locationCode) {
                        query.andWhere('Location_Code', args.locationCode);
                    }
                    if (locationId) {
                        query.andWhere('Location_Id', '!=', locationId);
                    }
                }).first().then(result => result.count);

            if (isExistLocation) {
                throw 'CHR00114';
            }


            const totalRecord = await locationExists(organizationDbConnection, args.locationName, locationId, 'MainBranch')
            if ((totalRecord > 0 && args.locationType.toLowerCase() !== 'mainbranch') || (totalRecord === 0 && args.locationType.toLowerCase() === 'mainbranch')) {
            if(args.locationId){
                let oldStatus = await organizationDbConnection(ehrTables.location).select('Location_Status').where('Location_Id', locationId).first().then(result => result.Location_Status);
                oldStatus=oldStatus ? oldStatus : ''
                if (args.locationStatus.toLowerCase() == 'inactive' && oldStatus.toLowerCase() == 'active') {
                    let isLocation = await isLocationUsed(organizationDbConnection, locationId, 'edit');
                    if (isLocation)
                        throw 'CHR00113';
                }
            }

                let locationData = {
                    Location_Name: args.locationName,
                    Location_Code: args.locationCode ? args.locationCode : null,
                    Location_Type: args.locationType,
                    Street1: args.street1,
                    Street2: args.street2 ? args.street2 : null,
                    City_Id: args.cityId,
                    State_Id: args.stateId,
                    Country_Code: args.countryCode,
                    Pincode: args.pincode ? args.pincode : null,
                    Phone: args.phone,
                    Notes: args.notes ? args.notes : null,
                    Fax: args.fax ? args.fax : null,
                    Currency_Symbol: args.currencySymbol,
                    Description: args.description ? args.description : null,
                    Org_Id: args.orgId ? args.orgId : null,
                    Zone_Id: args.zoneId ? args.zoneId : null,
                    Currency_Id: args.currencyId ? args.currencyId : null,
                    External_LocationId: args.externalLocationId ? args.externalLocationId : null,
                    Location_Status: args.locationStatus,
                    Barangay: args.barangay ? args.barangay : null,
                    Barangay_Id: args.barangayId ? args.barangayId : null,
                    Region: args.region ? args.region : null,
                }

                if (args.locationId) {

                    locationData.Updated_On = moment().utc().format('YYYY-MM-DD HH:mm:ss');
                    locationData.Updated_By = loginEmployeeId;
                    await organizationDbConnection(ehrTables.location).update(locationData)
                        .where('Location_Id', args.locationId);

                } else {

                    locationData.Added_On = moment().utc().format('YYYY-MM-DD HH:mm:ss');
                    locationData.Added_By = loginEmployeeId;
                    await organizationDbConnection(ehrTables.location).insert(locationData);

                }


                let formId = formIds.locations;

                let dataSetupStatus = await organizationDbConnection(ehrTables.dataSetupDashboard).select('Status').where('Form_Id', formId).first();
                if (dataSetupStatus.Status.toLowerCase() == 'open' || (formId == '18' && dataSetupStatus.Status.toLowerCase() == 'inprogress')) {
                    await updateDataSetupDashboard(organizationDbConnection, null, formId, 'Completed');
                }

                let systemLogParams = {
                    action: args.locationId ? 'Update' : 'Add',
                    userIp: context.User_Ip,
                    employeeId: loginEmployeeId,
                    formName: formName,
                    trackingColumn: 'Location_Name, Location_Code, Location_Type, Phone, Location_Status, Region, Barangay, Barangay_Id',
                    organizationDbConnection: organizationDbConnection,
                    uniqueId: args.locationName + ', ' + args.locationCode + ', ' + args.locationType + ', ' + args.phone + ', ' + args.locationStatus + ', ' + (args.region || '') + ', ' + (args.barangay || '') + ', ' + (args.barangayId || '')
                };

                await commonLib.func.createSystemLogActivities(systemLogParams);

                organizationDbConnection ? organizationDbConnection.destroy() : null;
                appManagerDbConnection ? appManagerDbConnection.destroy() : null;

                return { errorCode: '', message: 'Successfully adding/updating the location details' };

            } else {
                throw 'CHR00112'
            }
        } else {
            throw '_DB0111'
        }

    } catch (e) {
        console.error('Error in addUpdateLocation function main catch block.', e);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        appManagerDbConnection ? appManagerDbConnection.destroy() : null;
        let errResult
        if (e === 'IVE0000') {
            console.error('Validation error in the addUpdateLocation function', validationError);
            errResult = commonLib.func.getError('', 'IVE0000');
            throw new UserInputError(errResult.message, { validationError: validationError });//Return error response
        } else {
            let errorCode = e.code === 'ER_DUP_ENTRY' ? 'CHR0141' : e;
            errResult = commonLib.func.getError(errorCode, 'CHR00115');
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
}


async function locationExists(organizationDbConnection, locationName, locationId, mainBranch = NULL) {

    let locQry = organizationDbConnection(ehrTables.location).count('Location_Id as count');
    locQry = !mainBranch ? locQry.where('Location_Name', locationName) : locQry.where('Location_Type', 'MainBranch')
        .andWhere('Location_Status', 'Active');

    if (locationId) {
        locQry = locQry.andWhere('Location_Id', '!=', locationId);
    }

    const isExistLocation = await locQry.first().then(result => result.count);
    return isExistLocation;
}




async function getCustomFields(appmanagerDbConnection, fieldName = null, orgCode = null) {

    // Assume getOrgCode and getCustomFieldDetails are defined functions
    let orgCodeLevelCustomFormDetails = await getCustomFieldDetails(appmanagerDbConnection, fieldName, orgCode);
    if (!orgCodeLevelCustomFormDetails) {
        orgCodeLevelCustomFormDetails = await getCustomFieldDetails(appmanagerDbConnection, fieldName);

    }
    return orgCodeLevelCustomFormDetails;
};


async function getCustomFieldDetails(appmanagerDbConnection, fieldName = null, orgCode = null) {
    // Build the base query
    let query = appmanagerDbConnection('customization_fields as CF')
        .select('CF.Field_Id', 'CF.Enable', 'CF.New_Field_Name', 'CF.Required', 'F.Field_Name')
        .leftJoin('fields as F', 'F.Field_Id', 'CF.Field_Id');
    if (fieldName !== null) {
        if (orgCode) {
            query = query.where('CF.Org_Code', orgCode)
                .andWhere('F.Field_Name', fieldName)
                .andWhere('CF.Customization_Applicable_For', 'Specific Organization');
        } else {
            query = query.where('F.Field_Name', fieldName)
                .andWhere('CF.Customization_Applicable_For', 'All Organization');
        }
    } else {
        if (orgCode) {
            query = query.where('CF.Customization_Applicable_For', 'Specific Organization')
                .andWhere('CF.Org_Code', orgCode);
        } else {
            query = query.where('CF.Customization_Applicable_For', 'All Organization');
        }
    }
    // Fetch a single row if fieldName is provided
    const customFormDetails = await query.first();
    return customFormDetails;


}

