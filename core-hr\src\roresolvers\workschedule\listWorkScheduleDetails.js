//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../../../common/tablealias');
//Require constants
const { formName } = require('../../../common/appconstants');

//List the work schedule details in the work schedule form
module.exports.listWorkScheduleDetails = async (parent, args, context, info) => {
    console.log('Inside listWorkScheduleDetails() function.');
    let organizationDbConnection;
    let errResult;
    try{
        organizationDbConnection = knex(context.connection.OrganizationDb);
        //Get the login employee - work schedule form access
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, context.Employee_Id , formName.workSchedule, '', 'UI');
        //Check view rights exist or not                 
        if(Object.keys(checkRights).length>0 && checkRights.Role_View === 1) {
            return(
                organizationDbConnection(ehrTables.workSchedule)
                .select('WorkSchedule_Id as workScheduleId','WorkSchedule_Code as workScheduleCode', 'Title as workSchedule','WorkSchedule_Status as status',
                organizationDbConnection.raw(
                `TIME_FORMAT(Regular_Work_Start_Time, '%H:%i') as 'businessHoursStartTime',`
                +`TIME_FORMAT(Regular_Work_End_Time, '%H:%i') as 'businessHoursEndTime',`
                +`TIME_FORMAT(Break_Schedule_Start_Time, '%H:%i') as 'breakScheduleStartTime',`
                +`TIME_FORMAT(Break_Schedule_End_Time, '%H:%i') as 'breakScheduleEndTime'`),
                'Target_Effort_In_Hours_Per_Week as targetEffortInHoursPerWeek')
                .then((workScheduleDetails)=>{
                    // destroy DB connection
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    //return success response
                    return { errorCode:'',message:'Work schedule details retrieved successfully.',workScheduleDetails:(workScheduleDetails.length>0)?workScheduleDetails:[]};
                })
                .catch(catchError => {
                    console.log('Error in listWorkScheduleDetails function .catch block',catchError);
                    errResult = commonLib.func.getError(catchError, 'CWS0101');
                    // destroy DB connection
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    // return error response
                    throw new ApolloError(errResult.message,errResult.code);
                })
            );
        }
        else{
            console.log('Login employee id does not have view access to work schedule form.');
            throw ('_DB0100');
        }
    }catch(listWorkScheduleDetailsMainCatchErr) {
        console.log('Error in the listWorkScheduleDetails() function main catch block. ',listWorkScheduleDetailsMainCatchErr);
        // destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        errResult = commonLib.func.getError(listWorkScheduleDetailsMainCatchErr, 'CWS0001');
        // return error response
        throw new ApolloError(errResult.message,errResult.code);
    }
};