//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
let moment = require('moment');
//Require table alias
const { ehrTables } = require('../../../common/tablealias');
const { formName, formIds } = require('../../../common/appconstants');

//function to update on duty settings
module.exports.updateOnDutySettings = async (parent, args, context, info) => {
    console.log('Inside updateOnDutySettings function');
    let organizationDbConnection;
    try {
        let loginEmployeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, formName.onDutySettings, '', 'UI');
        if (Object.keys(checkRights).length > 0 && checkRights.Role_Update === 1) {
            return (
                organizationDbConnection
                    .transaction(async (trx) => {
                        return (
                            organizationDbConnection(ehrTables.onDutySettings)
                                .select('*')
                                .transacting(trx)
                                .then((settings) => {
                                    if (settings && settings.length) {
                                        return (
                                            organizationDbConnection(ehrTables.onDutySettings)
                                                .update({
                                                    Coverage: args.Coverage,
                                                    Custom_Group_Id: args.Custom_Group_Id,
                                                    Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                                                    Updated_By: loginEmployeeId
                                                })
                                                .where('On_Duty_Settings_Id', args.onDutySettingsId ? args.onDutySettingsId: 1)
                                                .then(async (updateSettings) => {
                                                    if (updateSettings) {
                                                        let updateCustomGroup = await updateAssociatedForms(organizationDbConnection, args, 1)
                                                        if (updateCustomGroup) {
                                                            return "success"
                                                        }
                                                    } else {
                                                        console.log('Error while updating short time off settings')
                                                        throw 'CHS0104'
                                                    }
                                                })
                                        )
                                    } else {
                                        return (
                                            organizationDbConnection(ehrTables.onDutySettings)
                                                .insert({
                                                    On_Duty_Settings_Id: 1,
                                                    Coverage: args.Coverage,
                                                    Custom_Group_Id: args.Custom_Group_Id,
                                                    Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                                                    Updated_By: loginEmployeeId
                                                })
                                                .then(async (insertSettings) => {
                                                    if (insertSettings) {
                                                        let updateCustomGroup = await updateAssociatedForms(organizationDbConnection, args, insertSettings[0])
                                                        if (updateCustomGroup) {
                                                            return "success"
                                                        }
                                                    } else {
                                                        throw 'CHS0104'
                                                    }
                                                })

                                        )
                                    }
                                })
                        )
                    })
                    .then((data) => {
                        if (data) {
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            return { errorCode: "", message: "On duty settings has been updated successfully." };
                        } else {
                            throw 'CHS0104'
                        }
                    })
                    .catch((catchError) => {
                        console.log('Error in updateOnDutySettings .catch() block', catchError);
                        let errResult = commonLib.func.getError(catchError, 'CHS0104');
                        //Destroy DB connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        //Return error response
                        throw new ApolloError(errResult.message, errResult.code);
                    })
            )
        } else {
            console.log('No rights to update the On duty settings');
            throw '_DB0111';
        }
    } catch (mainCatchError) {
        console.log('Error in updateOnDutySettings function main block', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        let errResult = commonLib.func.getError(mainCatchError, 'CHS0004');
        // return response
        throw new ApolloError(errResult.message, errResult.code);
    }
}

async function updateAssociatedForms(organizationDbConnection, args, onDutySettingsId) {
    try {
        console.log('Inside updateAssociatedForms', onDutySettingsId)
        return (
            organizationDbConnection(ehrTables.customGroupAssociated)
                .delete()
                .where("Form_Id", formIds.onDuty)
                .andWhere("Parent_Id", onDutySettingsId)
                .then(() => {
                    if(args.Custom_Group_Id){
                        return (
                            organizationDbConnection(ehrTables.customGroupAssociated)
                                .insert({
                                    Parent_Id: onDutySettingsId,
                                    Form_Id: formIds.onDuty,
                                    Custom_Group_Id: args.Custom_Group_Id,
                                })
                                .then((data) => {
                                    if (data) {
                                        return true
                                    } else {
                                        return false
                                    }
                                })
                        )
                    }else{
                        return true
                    }
                })
        )
    } catch (err) {
        console.log('Error in updateAssociatedForms', err)
        return false
    }
}