// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formName, formIds } = require('../../../../common/appconstants');
module.exports.retrieveLopRecoverySettingsDetails = async (parent, args, context, info) => {
    let organizationDbConnection;
    try {
        console.log("Inside retrieveLopRecoverySettingsDetails function.")
        let employeeId = context.Employee_Id;
        let formId = formIds.lopRecoverySettings;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        // check their rights
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, employeeId, '', '', 'UI', false, formId);

        if (Object.keys(checkRights).length > 0 && checkRights.Role_View === 1) {
            return (
                organizationDbConnection(ehrTables.lopRecoverySettings + " as LRS")
                    .select('LRS.*', 'CGA.Custom_Group_Id', 'CGA.Form_Id', 'CEG.Group_Name',
                        "WF.Workflow_Name",
                        organizationDbConnection.raw("(CASE WHEN CGA.Custom_Group_Id IS NOT NULL THEN 'Custom_Group' ELSE 'Organization' END) as Coverage"),
                        organizationDbConnection.raw("CONCAT_WS(' ',EPI.Emp_First_Name, EPI.Emp_Middle_Name, EPI.Emp_Last_Name) as Updated_By"), organizationDbConnection.raw("CONCAT_WS(' ',EPI2.Emp_First_Name,EPI2.Emp_Middle_Name, EPI2.Emp_Last_Name) as Added_By"))
                    .leftJoin(ehrTables.workflows + " as WF", "LRS.Workflow_Id", "WF.Workflow_Id")
                    .leftJoin(ehrTables.empPersonalInfo + " as EPI", "EPI.Employee_Id", " LRS.Updated_By")
                    .leftJoin(ehrTables.empPersonalInfo + " as EPI2", "EPI2.Employee_Id", "LRS.Added_By")
                    .leftJoin(ehrTables.customGroupAssociated + " as CGA", function () {
                        this.on("LRS.Lop_Settings_Id", "=", "CGA.Parent_Id")
                            .andOn("CGA.Form_Id", "=", formId);
                    })
                    .leftJoin(ehrTables.cusEmpGroup + " as CEG", "CEG.Group_Id", "CGA.Custom_Group_Id")
                    .then((data) => {

                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        return { errorCode: "", message: "lop settings configuration retrieved successfully.", lopRecoverySettingsDetails: data };

                    })
                    .catch((err) => {
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        console.log('Error in retrieveLopRecoverySettingsDetails .catch() block', err);
                        let errResult = commonLib.func.getError(err, 'SCL0101');
                        throw new ApolloError(errResult.message, errResult.code);
                    })
            )
        }
        else {
            throw '_DB0100';
        }
    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in retrieveLopRecoverySettingsDetails function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'SCL0001');
        throw new ApolloError(errResult.message, errResult.code);
    }
}