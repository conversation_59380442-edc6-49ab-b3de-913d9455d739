//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { UserInputError,ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables,appManagerTables } = require('../../../common/tablealias');
//Require constants
const { formName,systemLogs, formIds } = require('../../../common/appconstants');
//Require validation functions
const { validateWeekOffInputs,formWeekOffInputList } = require('../../../common/workScheduleValidation');
//Require moment time zone
const moment = require('moment-timezone');
//Require validation functions
const { validateWSAndAttendance } = require('../../../common/workScheduleValidation');

//Add the week off for the work schedule
module.exports.addWeekOff = async (parent, args, context, info) => {
    console.log('Inside addWeekOff() function.');
    let organizationDbConnection;
    let appManagerDbConnection;
    let errResult;
    let validationError={};
    try{
        //get emails from envionment
        let emailFrom=process.env.emailFrom;
        let emailTo=process.env.emailTo;
        let sesRegion=process.env.sesRegion;
        let loginEmployeeId = context.Employee_Id;
        let orgCode=context.Org_Code;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        appManagerDbConnection = knex(context.connection.AppManagerDb);

        //Get the login employee - work schedule form access
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection,loginEmployeeId,formName.workSchedule,'','UI');
        //Check add rights exist or not                 
        if(Object.keys(checkRights).length>0 && checkRights.Role_Add === 1) {
            validationError = await validateWeekOffInputs(args);
            //Check validation error exist or not
            if(Object.keys(validationError).length ===0){
                let workScheduleId = args.workScheduleId;
                let dashboardType = args.dashboardType;
                
                if(dashboardType === 'HRMSDASHBOARD'){
                    /** 1. Validate the attendance exist or not for the non-shift roster employees.
                     * 2. Get the shift type associated with the work schedule and validate the
                    * shift is scheduled or not for that shift type.*/
                    await validateWSAndAttendance(organizationDbConnection,workScheduleId);
                }

                let userActionName;
                //Validate the work schedule id exist in the table or not
                return(
                organizationDbConnection(ehrTables.workSchedule)
                .count('WorkSchedule_Id as workScheduleExistCount')
                .where('WorkSchedule_Id',workScheduleId)
                .then(async(workScheduleExistResult) => {
                    //If work schedule id exist
                    if(workScheduleExistResult && workScheduleExistResult.length > 0 &&
                    workScheduleExistResult[0].workScheduleExistCount > 0){
                        //Initiate a transaction
                        return (
                        organizationDbConnection
                        .transaction(function (trx) {
                            //Delete the week off details for the work schedule id
                            return (
                            organizationDbConnection(ehrTables.workscheduleWeekoff)
                            .delete()
                            .where('WorkSchedule_Id',workScheduleId)
                            .transacting(trx)
                            .then(async() => {
                                //Get the login employee current date and time based on login employee location
                                let currentDateTime = await commonLib.func.getEmployeeTimeZone(loginEmployeeId, organizationDbConnection, 1);
                                //Form week off input formation params
                                let weekOffInputFormationArgs = {workScheduleId,currentDateTime,loginEmployeeId};
                                //Call the function to get the week off creation input details
                                let weekOffInsertArray = await formWeekOffInputList(args.weekOffList,weekOffInputFormationArgs);
                                //If the week off exist
                                if(weekOffInsertArray.length>0){
                                    userActionName = systemLogs.roleAdd;
                                    //Insert the week off details for the work schedule id
                                    return (
                                    organizationDbConnection(ehrTables.workscheduleWeekoff)
                                    .insert(weekOffInsertArray)
                                    .transacting(trx)
                                    .then(() => {
                                        return 'success';
                                    })
                                    )
                                }else{
                                    userActionName = systemLogs.roleUpdate;
                                    return 'success';
                                }
                            })
                            )
                        })
                        .then(async() => {
                            /** Log message:
                             * 1. Add Work Schedule week off for the work schedule - 1
                             * 2. Update Work Schedule week off for the work schedule - 1 */
                            let systemLogParams = {
                                action: userActionName,
                                userIp: context.User_Ip,
                                employeeId: loginEmployeeId,
                                formId: formIds.workSchedule,
                                organizationDbConnection: organizationDbConnection,
                                uniqueId: workScheduleId,
                                message: `Week off has been added successfully.`,
                                isEmployeeTimeZone: 0,
                                changedData: args
                            };
                            //Call the function to add the system log
                            await commonLib.func. createSystemLogActivities(systemLogParams);
                            //Destroy DB connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            appManagerDbConnection ? appManagerDbConnection.destroy() : null;
                            // We will be triggering the step function to updateWeekOffDate.
                            let inputParams={'orgCode':orgCode,'workScheduleId':args.workScheduleId,'orgCode':orgCode}
                            let triggerupdateWeekOffDateResponse= await commonLib.stepFunctions.triggerStepFunction(process.env.startUpdateWeekOffDateStepFunction,'startUpdateWeekOff','',inputParams);
                            console.log('Response after triggering generateReport step function',triggerupdateWeekOffDateResponse);
                            //Return success response
                            return {errorCode: '',message:'Week off added successfully.'};
                        })
                        .catch(async(weekOffCatchError) => {
                            console.log("Error in addWeekOff() function week off .catch block", weekOffCatchError);
                            throw weekOffCatchError;
                        })
                        );
                    }else{
                        console.log('Work schedule details do not exist for the work schedule id,',workScheduleId);
                        throw 'CWS0008';
                    }
                })
                .catch(function (workScheduleCatchError) {
                    console.log('Error in addWeekOff() function work schedule .catch() block', workScheduleCatchError);
                    errResult = commonLib.func.getError(workScheduleCatchError, 'CWS0106');
                    //Destroy DB connection
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    appManagerDbConnection ? appManagerDbConnection.destroy() : null;
                    throw new ApolloError(errResult.message,errResult.code);//Return error response
                })
                );
            }else{
                throw 'IVE0000';
            }
        }
        else{
            console.log('Login employee id does not have add access to work schedule form.');
            throw ('_DB0101');
        }
    }catch(addWeekOffMainCatchErr) {
        console.log('Error in the addWeekOff() function main catch block. ',addWeekOffMainCatchErr);
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        appManagerDbConnection ? appManagerDbConnection.destroy() : null;
        //If validation error exist
        if (addWeekOffMainCatchErr === 'IVE0000') {
            console.log('Validation error in the addWeekOff() function',validationError);
            errResult = commonLib.func.getError('', 'IVE0000');
            throw new UserInputError(errResult.message, { validationError: validationError });//Return error response
        } else{
            errResult = commonLib.func.getError(addWeekOffMainCatchErr, 'CWS0005');
            throw new ApolloError(errResult.message,errResult.code);//Return error response
        }
    }
};

