// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require app constants
const {awsSesTemplates} = require('../../common/appconstants')

module.exports.sendCommonEmail = async (parent, args, context, info) => {
    try {
        console.log("Inside sendCommonEmail function.")
        let notificationParams = args
        //Prefill the Source
        notificationParams.Source = process.env.emailFrom
        //Prefill the Template if not given
        if(!args.Template){
            notificationParams.Template = awsSesTemplates.commonTemplate
        }
        let region = process.env.sesRegion
        let sendEmailNotifications = await commonLib.func.sendBulkEmailNotifications(notificationParams, region)
        if (sendEmailNotifications) {
            return { errorCode: "", message: "Email has been sent successfully." };
        } else {
            throw 'CCH0008'
        }

    }
    catch (e) {
        console.log('Error in sendCommonEmail function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'CCH0008');
        throw new ApolloError(errResult.message, errResult.code);
    }
}