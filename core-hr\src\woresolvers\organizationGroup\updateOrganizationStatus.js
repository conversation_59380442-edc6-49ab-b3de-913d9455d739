const { ApolloError } = require('apollo-server-lambda')
const {
  formIds
} = require('../../../common/appconstants')
const { ehrTables } = require('../../../common/tablealias')
const moment = require('moment')
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib
// require knex/*  */
const knex = require('knex')

module.exports.updateOrganizationStatus = async (parent, args, context) => {
  try {
    const loginEmployee_Id = context.Employee_Id
    let organizationGroupFormId = formIds.organizationGroup
    const organizationDbConnection = knex(context.connection.OrganizationDb)
    let checkRights = await commonLib.func.checkEmployeeAccessRights(
      organizationDbConnection,
      loginEmployee_Id,
      '',
      '',
      'UI',
      false,
      organizationGroupFormId
    )
    if (
      false && Object.keys(checkRights).length <= 0 ||
      (args.organizationGroupId && checkRights.Role_Update === 0)
    ) {
      
        throw '_DB0102'
      
    }
    const {
     Status,
     organizationGroupId
    } = args


    let organizationGroupData={
        "Status":Status
    }

    await checkEmployeesExist(organizationDbConnection,ehrTables,organizationGroupId);

      organizationGroupData.Updated_On = moment()
        .utc()
        .format('YYYY-MM-DD HH:mm:ss')
      organizationGroupData.Updated_By = loginEmployee_Id

     return(await organizationDbConnection(ehrTables.organizationGroup)
        .where('Organization_Group_Id', organizationGroupId)
        .update(organizationGroupData).then(async (data)=>{
            let systemLogParam = {
                userIp: context.User_Ip,
                employeeId: loginEmployee_Id,
                organizationDbConnection: organizationDbConnection,
                message: "The organization group status updated successfully"
              }
              await commonLib.func.createSystemLogActivities(systemLogParam)
          
              organizationDbConnection ? organizationDbConnection.destroy() : null
              return {
                errorCode: '',
                message: "The organization group status updated successfully"
              }
        }).catch(err=>{
            console.log(
                'Error in the updateOrganizationStatus() catch block. ',
                err
              )
              throw err;
        })
      )
  
  } catch (error) {
    console.log(
      'Error in the updateOrganizationStatus() function main catch block. ',
      error
    )
      errResult = commonLib.func.getError(error, 'CHR0135')
      throw new ApolloError(errResult.message, errResult.code)
    
  }
}
async function checkEmployeesExist(organizationDbConnection,ehrTables,organizationGroupId){
    try{
        let [employeeData, candidateData] = await Promise.all([
            organizationDbConnection(ehrTables.organizationGroup + ' as OG')
              .leftJoin(
                ehrTables.empJob + ' as EJ',
                'EJ.Organization_Group_Id',
                'OG.Organization_Group_Id'
              )
              .select('EJ.Organization_Group_Id')
              .where('OG.Organization_Group_Id', organizationGroupId)
              .whereNotNull('EJ.Organization_Group_Id'),
          
            organizationDbConnection(ehrTables.organizationGroup + ' as OG')
              .leftJoin(
                ehrTables.candidateJob + ' as CJ',
                'CJ.Organization_Group_Id',
                'OG.Organization_Group_Id' // Corrected here
              )
              .select('CJ.Organization_Group_Id')
              .where('OG.Organization_Group_Id', organizationGroupId)
              .whereNotNull('CJ.Organization_Group_Id')
          ]);
if(employeeData && employeeData.length){
    throw 'CHR0136'
}
else if(candidateData && candidateData.length){
    throw 'CHR0137'
}
    }
    catch(err){
        throw err;
    }

}
