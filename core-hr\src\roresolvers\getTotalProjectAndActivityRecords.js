// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const {formIds } = require('../../common/appconstants');

let organizationDbConnection;
module.exports.getTotalProjectAndActivityRecords = async (parent, args, context, info) => {
    console.log('Inside getTotalProjectAndActivityRecords function');
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let loginEmployeeId = context.Employee_Id;
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, null, '', 'UI', false, formIds.projects);
            if (Object.keys(checkRights).length > 0 && checkRights.Role_View === 1) {
                return(
                    await organizationDbConnection(ehrTables.projectDetails + " as PD")
                    .count('PD.Project_Id as projectAndActivityRecordsCount')
                    .leftJoin(ehrTables.projectActivities + " as PA", "PA.Project_Id", "PD.Project_Id")
                    .whereIn('PD.Project_Id', args.projectId)
                    .then(async (data) => {
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        return { errorCode: "", message: "Project and activity records count detail been retrieved successfully.", projectAndActivityRecordsCount: data && data.length ? data[0].projectAndActivityRecordsCount: 0};
                    })
                    .catch((catchError) => {
                        //Destroy DB connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        console.log('Error in getTotalProjectAndActivityRecords function catch block.', catchError);
                        let errResult = commonLib.func.getError(catchError, 'CHR0126');
                        throw new ApolloError(errResult.message, errResult.code)
                    })
                )
            }
            else {
                console.log('Employee do not have view access rights');
                throw '_DB0100';
            }
    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in getTotalProjectAndActivityRecords function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'CHR00103');
        throw new ApolloError(errResult.message, errResult.code)
       
    }
}
