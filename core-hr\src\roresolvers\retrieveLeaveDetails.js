// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formName } = require('../../common/appconstants');


let organizationDbConnection;
module.exports.retrieveLeaveDetails = async (parent, args, context, info) => {
    try {
        console.log("Inside retrieveLeaveDetails function.")
        let employeeId = context.Employee_Id;
        const leaveId = args.leaveId;
        let leaveSettingsRetrieved = {}
        organizationDbConnection = knex(context.connection.OrganizationDb);
        // check their rights
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, employeeId, formName.task_Management, '', 'UI');
        if (Object.keys(checkRights).length > 0 && checkRights.Role_View === 1) {
            return (
                organizationDbConnection(ehrTables.empLeaves)
                    .select('L.*','LT.Leave_Name as Leave_Type', 'U.Unit_Tag as Duration', 'ER.ESIC_Reason', 'L.Reason', 
                    organizationDbConnection.raw(`CONCAT_WS(' ',EPI1.Emp_First_Name,EPI1.Emp_Middle_Name, EPI1.Emp_Last_Name) as 'Employee_Name',
                    CONCAT_WS(' ',EPI2.Emp_First_Name,EPI2.Emp_Middle_Name, EPI2.Emp_Last_Name) as 'Alternate_Person',
                    CONCAT_WS(' ',EPI3.Emp_First_Name,EPI3.Emp_Middle_Name, EPI3.Emp_Last_Name) as 'Added_By',
                    CONCAT_WS(' ',EPI4.Emp_First_Name,EPI4.Emp_Middle_Name, EPI4.Emp_Last_Name) as 'Updated_By'`),
                    )
                    .from(ehrTables.empLeaves+' as L')
                    .innerJoin(ehrTables.leaveType+' as LT', 'L.LeaveType_Id','LT.LeaveType_Id')
                    .innerJoin(ehrTables.empPersonalInfo+' as EPI1','L.Employee_Id','EPI1.Employee_Id')
                    .leftJoin(ehrTables.empPersonalInfo+' as EPI2','L.Alternate_Person','EPI2.Employee_Id')
                    .innerJoin(ehrTables.empPersonalInfo+' as EPI3','L.Added_By','EPI3.Employee_Id')
                    .leftJoin(ehrTables.empPersonalInfo+' as EPI4','L.Updated_By','EPI4.Employee_Id')
                    .leftJoin(ehrTables.esicReason+' as ER','L.Reason_Id','ER.Reason_Id')
                    .innerJoin(ehrTables.unitData+' as U', 'L.Duration','U.Target_Value')
                    .where('Leave_Id',leaveId)
                    .then((data) => {
                        if(data.length){
                            leaveDetails = data[0];
                            //destroy the connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            return { errorCode: "", message: "Leave details retrieved successfully.", leaveDetails: leaveDetails };
                        }else{
                            console.log('Leave details not found')
                            throw 'EI00113'
                        }
                    })
                    .catch((err)=>{
                        console.log('Error in retrieveLeaveDetails .catch() block', err);
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        let errResult = commonLib.func.getError(err, 'EI00113');
                        throw new ApolloError(errResult.message, errResult.code);
                    })
            )
        }
        else {
            throw '_DB0100';
        }

    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in retrieveLeaveDetails function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'EI00112');
        throw new ApolloError(errResult.message, errResult.code);
    }
}
