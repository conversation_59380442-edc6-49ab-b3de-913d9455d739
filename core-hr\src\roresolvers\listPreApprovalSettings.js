// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;

// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formName, formIds } = require('../../common/appconstants');

let organizationDbConnection;
module.exports.listPreApprovalSettings = async (parent, args, context, info) => {
    try {
        console.log("Inside listPreApprovalSettings function.")
        let employeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        // get the access rights of the login employee for the accreditation form
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, employeeId, formName.preApprovalSettings, '', 'UI', false, formIds.preApprovalSetting);
        if (Object.keys(checkRights).length > 0 && checkRights.Role_View === 1) {
            return (
                organizationDbConnection(ehrTables.preApprovalSettings)
                    .select('A.Pre_Approval_Configuration_Id as preApprovalConfigurationId', 'A.Pre_Approval_Type as preApprovalType', 'A.Type_Of_Day as typeOfDay',
                        'A.Coverage as coverage', 'A.Custom_Group_Id as customGroupId', 'A.Period as period', 'A.No_Of_Pre_Approval_Request as noOfPreApprovalRequest',
                        'A.Restrict_Sandwich as restrictSandwich', 'A.Restrict_Sandwich_For as restrictSandwichFor', 'A.Advance_Notification_Days as advanceNotificationDays',
                        "A.Workflow_Id as workflowId", "WF.Workflow_Name as workflowName", 'A.Status as status', 'A.Document_Upload as documentUpload',
                        'A.Max_Days_For_Document_Upload as maxDaysForDocumentUpload', 'A.Max_Days_Allowed_Per_Request as maxDaysAllowedPerRequest',
                        'A.Added_On as addedOn', 'A.Updated_On as updatedOn', 'CEG.Group_Name as customGroupName',
                        organizationDbConnection.raw("CONCAT_WS(' ',EPI.Emp_First_Name, EPI.Emp_Middle_Name, EPI.Emp_Last_Name) as addedByName"),
                        organizationDbConnection.raw("CONCAT_WS(' ',EPI2.Emp_First_Name, EPI2.Emp_Middle_Name, EPI2.Emp_Last_Name) as updatedByName"),)
                    .from(ehrTables.preApprovalSettings + " as A")
                    .leftJoin(ehrTables.cusEmpGroup + " as CEG", "CEG.Group_Id", "A.Custom_Group_Id")
                    .leftJoin(ehrTables.workflows + " as WF", "A.Workflow_Id", "WF.Workflow_Id")
                    .leftJoin(ehrTables.empPersonalInfo + " as EPI", "EPI.Employee_Id", "A.Added_By")
                    .leftJoin(ehrTables.empPersonalInfo + " as EPI2", "EPI2.Employee_Id", "A.Updated_By")
                    .then(async (preApprovalSettings) => {
                        //destroy the connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        return { errorCode: "", message: "Pre-approval settings details retrieved successfully.", preApprovalSettings: preApprovalSettings };
                    })
                    .catch((err) => {
                        //Destroy DB connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        console.log('Error in listPreApprovalSettings function .catch block.', err);
                        let errResult = commonLib.func.getError(err, 'CHR0035');
                        throw new ApolloError(errResult.message, errResult.code);
                    })
            )
        }
        else {
            console.log('Employee do not have view access rights');
            throw '_DB0100';
        }

    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in listPreApprovalSettings function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'CHR0035');
        throw new ApolloError(errResult.message, errResult.code);
    }
}
