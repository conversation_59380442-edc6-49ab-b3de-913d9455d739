const alphaNumSpCDotHySlashRegex = /^[\w\.\,\#\+\&\/\-\(\)\:\'\ ]*$/;
const numberValidation = /^[0-9]+$/;
/** Only alphanumeric, spaces and symbols + , / . # & : () ' - allowed. */
const descriptionRegex = /^[\w\.\,\#\+\&\/\-\(\)\:\'\`\?\n ]*$/;
/** Only alphanumeric, spaces and symbols . , & () allowed. */
const alphaNumSpaceSymbolRegex = /^[\w\.\&\,\(\)\ ]+$/;
// Holiday Description and Name
const holidayDescriptionName = /^[\w\.\,\#\+\&\/\-\(\)\:\'\–"= "]*$/;
// Mobile number validation
const mobileNumberValidator = /^\d{5,15}$/;
// Mobile number country code
const mobileNumberCountryCodeValidator = /^\+\d{1,3}$/;
const notesRegex = /^[\w\.\,\#\+\&\/\-\(\)\:\'\`\?\"\'\n ]*$/;
//Common Input Validation
const commonInputAlphaNumValidation = /^[\p{L}\p{M}\p{Nd}\s.,#\+&/\-():'ñd’!@#$%^&*±_=|\\;?"]+$/u

/** Common Validation */
module.exports = {
    alphaNumSpCDotHySlashValidation: function (input) {
        return (result = alphaNumSpCDotHySlashRegex.test(input) ? true : false);
    },
    booleanNumberValidation: function (input) {
        return (result = (input === 1 || input === 0) ? true : false);
    },
    checkLength: function (input, minLength, maxLength) {
        return (result = (input.length < minLength || input.length > maxLength) ? false : true);
    },
    checkMinMaxValue: function (input, minValue, maxValue) {
        return (result = (input < minValue || input > maxValue) ? false : true);
    },
    numberValidation: function (input) {
        return (result = numberValidation.test(input) ? true : false);
    },
    descriptionValidation: function (input) {
        return (result = descriptionRegex.test(input) ? true : false);
    },
    notesValidation: function (input) {
        return (result = notesRegex.test(input) ? true : false);
    },
    weekOffDurationValidation: function (input) {
        return (result = (input === 0 || input === 0.5 || input === 1) ? true : false);
    },
    alphaNumSpaceSymbolValidation: function (input) {
        return (result = alphaNumSpaceSymbolRegex.test(input) ? true : false);
    },
    holidayDescription: function (input) {
        return (result = holidayDescriptionName.test(input) ? true: false);
    },
    mobileNumberValidation: function (input1, input2){
        if (result = mobileNumberValidator.test(input1) ? true: false){
            return (result = mobileNumberCountryCodeValidator.test(input2) ? true: false);
        }
    },
    commonInputAlphaNumValidation: function (input) {
        return (result = commonInputAlphaNumValidation.test(input) ? true : false);
    }
};