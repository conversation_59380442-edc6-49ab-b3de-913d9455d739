//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError, UserInputError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../../common/tablealias');
const { formName, formIds } = require('../../common/appconstants');
const moment = require('moment');

//fuction to list holidays based on customGroup
let organizationDbConnection;
module.exports.listCustomGroupHolidays = async (parent, args, context, info) => {
    console.log('Inside listCustomGroupHolidays function');
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let loginEmployeeId = context.Employee_Id;
        let validationError = {}
        if (!args.year.length && !args.month) {
            if (!args.year) {
                validationError['IVE0267'] = commonLib.func.getError('', 'IVE0267').message;
            } else {
                validationError['IVE0289'] = commonLib.func.getError('', 'IVE0289').message;
            }
        }
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, formName.holidays, '', 'UI');
        if (Object.keys(validationError).length === 0) {
            if (Object.keys(checkRights).length > 0 && checkRights.Role_View === 1) {
                return (
                    await organizationDbConnection(ehrTables.holidaySettings)
                        .select('Holiday_Settings_Type')
                        .from(ehrTables.holidaySettings)
                        .then(async (data) => {
                            if (data[0].Holiday_Settings_Type !== 'CUSTOMGROUP') {
                                throw 'CGH0104'
                            } else {
                                return (
                                    await organizationDbConnection(ehrTables.holidayCustomGroup)
                                        .select("HCG.*", "H.Holiday_Name", "CG.Group_Id", "CG.Group_Name", organizationDbConnection.raw("CONCAT_WS(' ',EPI.Emp_First_Name,EPI.Emp_Middle_Name, EPI.Emp_Last_Name) as addedByName"), organizationDbConnection.raw("CONCAT_WS(' ',EPI2.Emp_First_Name,EPI2.Emp_Middle_Name, EPI2.Emp_Last_Name) as updatedByName"))
                                        .innerJoin(ehrTables.holiday + " as H", "H.Holiday_Id", "HCG.Holiday_Id")
                                        .leftJoin(ehrTables.customGroupAssociated + " as CGA", "CGA.Parent_Id", "HCG.Holiday_Assign_Id")
                                        .leftJoin(ehrTables.cusEmpGroup + " as CG", "CG.Group_Id", "CGA.Custom_Group_Id")
                                        .leftJoin(ehrTables.empPersonalInfo + " as EPI", "EPI.Employee_Id", "HCG.Added_By")
                                        .leftJoin(ehrTables.empPersonalInfo + " as EPI2", "EPI2.Employee_Id", "HCG.Updated_By")
                                        .from(ehrTables.holidayCustomGroup + " as HCG")
                                        .where("CGA.Form_Id", formIds.holidays)
                                        .where(function () {
                                            if (args.year.length > 1) {
                                                for (let i = 0; i < args.year.length; i++) {
                                                    let dates = []
                                                    dates.push(`${args.year[i]}-01-01`)
                                                    dates.push(`${args.year[i]}-12-31`)
                                                    this.orWhereBetween("HCG.Holiday_Date", dates)
                                                }
                                            } else {
                                                if (args.month === 'All') {
                                                    let dates = []
                                                    dates.push(`${args.year[0]}-01-01`)
                                                    dates.push(`${args.year[0]}-12-31`)
                                                    this.andWhereBetween("HCG.Holiday_Date", dates)
                                                } else {
                                                    let dates = []
                                                    dates.push(`${args.year[0]}-${args.month}-01`)
                                                    dates.push(`${args.year[0]}-${args.month}-31`)
                                                    this.andWhereBetween("HCG.Holiday_Date", dates)
                                                }
                                            }
                                        })
                                        .orderBy('HCG.Holiday_Date', 'desc')
                                        .then((data) => {
                                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                                            return { errorCode: "", message: "Custom group holiday data has been fetched successfully.", listCustomGroupHolidays: data };
                                        })
                                        .catch((catchError) => {
                                            console.log('Error in listCustomGroupHolidays .catch() block', catchError);
                                            let errResult = commonLib.func.getError(catchError, 'CGH0101');
                                            //Destroy DB connection
                                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                                            throw new ApolloError(errResult.message, errResult.code);
                                        })
                                )
                            }
                        })
                )
            }
            else {
                console.log('No rights to view holidays');
                throw '_DB0100';
            }
        } else {
            throw 'IVE0000';
        }
    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in listCustomGroupHolidays function main catch block.', e);
        if (e === 'IVE0000') {
            let errResult = commonLib.func.getError('', 'IVE0000');
            console.log('Validation error in listCustomGroupHolidays function - ', validationError);
            // return response
            throw new UserInputError(errResult.message, { validationError: validationError });
        } else {
            let errResult = commonLib.func.getError(e, 'CGH0001');
            throw new ApolloError(errResult.message, errResult.code)
        }
    }
}