//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError, UserInputError } = require('apollo-server-lambda');
let moment = require('moment-timezone');
//Require table alias
const { ehrTables } = require('../../common/tablealias');
const { formName } = require('../../common/appconstants');
const { updateAllLeaveSettings } = require('../../common/commonfunctions');

//function to update leave settings
module.exports.updateLeaveSettings = async (parent, args, context, info) => {
    console.log('Inside updateLeaveSettings function');
    let organizationDbConnection;
    let validationError = {};
    try {
        let loginEmployeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, formName.leaveSettings, '', 'UI');

        //Input Validation
        if (!args.Enable_CAMU_Scheduler) {
            validationError['IVE0263'] = commonLib.func.getError('', 'IVE0263').message;
        }
        if (!args.Allow_Upline_Managers_Approval) {
            validationError['IVE0264'] = commonLib.func.getError('', 'IVE0264').message;
        }
        if (!args.Enable_Workflow) {
            validationError['IVE0265'] = commonLib.func.getError('', 'IVE0265').message;
        }
        if (!args.Enforce_Alternate_Person_For_Leave) {
            validationError['IVE0297'] = commonLib.func.getError('', 'IVE0297').message;
        }
        if (!args.Coverage_For_Alternate_Person) {
            validationError['IVE0298'] = commonLib.func.getError('', 'IVE0298').message;
        }

        if (Object.keys(validationError).length === 0) {
            if (Object.keys(checkRights).length > 0 && checkRights.Role_Update === 1) {
                if (args.Enable_Workflow !== args.Old_Workflow) {
                    return (
                        organizationDbConnection(ehrTables.empLeaves)
                            .select('Approval_Status')
                            .whereIn('Approval_Status', ['Applied', 'Returned', 'Cancel Applied'])
                            .from(ehrTables.empLeaves)
                            .then((data) => {
                                if (data.length) {
                                    //Workflow label is editable only when there are no pending leave
                                    throw 'EM0266'
                                } else {
                                    if (args.Enable_Workflow == 'Yes') {
                                        return (
                                            organizationDbConnection
                                                .transaction(async (trx) => {
                                                    //get the id of workflow module id with the form Id 31
                                                    return (
                                                        organizationDbConnection(ehrTables.workflows)
                                                            .select("WF.Workflow_Id")
                                                            .where('WFM.Form_Id', 31)
                                                            .innerJoin(ehrTables.workflowModule + ' as WFM', 'WFM.Workflow_Module_Id', 'WF.Workflow_Module_Id ')
                                                            .from(ehrTables.workflows + ' as WF')
                                                            .andWhere("WF.Default_Workflow", 1)
                                                            .transacting(trx)
                                                            .then((workflowId) => {
                                                                if (workflowId.length) {
                                                                    let workflowModuleId = workflowId[0].Workflow_Id
                                                                    return (
                                                                        organizationDbConnection(ehrTables.leaveType)
                                                                            .update({
                                                                                'Workflow_Id': workflowModuleId
                                                                            })
                                                                            .transacting(trx)
                                                                            .then(async () => {
                                                                                    let response = await updateAllLeaveSettings(args, organizationDbConnection, loginEmployeeId)
                                                                                    if (response) {
                                                                                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                                                                                        return { errorCode: "", message: "Leave settings has been updated successfully." };
                                                                                    } else {
                                                                                        throw 'EM0265'
                                                                                    }
                                                                            })
                                                                            .catch((err) => {
                                                                                console.log('Error while updating leave type', err)
                                                                                throw 'EM0268'
                                                                            })
                                                                    )

                                                                } else {
                                                                    console.log('There are no Workflow_Id integrated with leave form')
                                                                    throw 'EM0267'
                                                                }
                                                            })
                                                            .catch(catchError => {
                                                                console.log('Error while fetching workflow Id', catchError);
                                                                let errResult = commonLib.func.getError(catchError, 'EM0267');
                                                                //Destroy DB connection
                                                                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                                                //Return error response
                                                                throw new ApolloError(errResult.message, errResult.code);
                                                            })

                                                    )
                                                }))

                                    } else {
                                        return (
                                            organizationDbConnection(ehrTables.leaveType)
                                                .update({
                                                    'Workflow_Id': null
                                                })
                                                .then(async () => {
                                                        let response = await updateAllLeaveSettings(args, organizationDbConnection, loginEmployeeId)
                                                        if (response) {
                                                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                                                            return { errorCode: "", message: "Leave settings has been updated successfully." };
                                                        } else {
                                                            throw 'EM0265'
                                                        }
                                                })
                                        )
                                    }
                                }
                            })
                            .catch(catchError => {
                                console.log('Error in updateLeaveSettings .catch() block', catchError);
                                let errResult = commonLib.func.getError(catchError, 'EM0268');
                                //Destroy DB connection
                                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                //Return error response
                                throw new ApolloError(errResult.message, errResult.code);
                            })
                    )
                } else {
                    let response = await updateAllLeaveSettings(args, organizationDbConnection, loginEmployeeId)
                    if (response) {
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        return { errorCode: "", message: "Leave settings has been updated successfully." };
                    } else {
                        throw 'EM0265'
                    }
                }
            } else {
                console.log('No rights to update the leave settings');
                throw '_DB0111';
            }
        } else {
            throw 'IVE0000';
        }
    } catch (mainCatchError) {
        console.log('Error in updateLeaveSettings function main block', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        if (mainCatchError === 'IVE0000') {
            let errResult = commonLib.func.getError('', 'IVE0000');
            console.log('Validation error in updateLeaveSettings function - ', validationError);
            // return response
            throw new UserInputError(errResult.message, { validationError: validationError });
        } else {
            errResult = commonLib.func.getError(mainCatchError, 'EM00117');
            // return response
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
}