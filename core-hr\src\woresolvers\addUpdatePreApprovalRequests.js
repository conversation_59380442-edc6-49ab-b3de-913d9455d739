// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../common/tablealias');
//Require apollo server to return error message
const { UserInputError, ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formName, systemLogs } = require('../../common/appconstants');
//Require validation function
const { validatePreApprovalRequestsInputs } = require('../../common/inputValidations');
const moment = require('moment');
const { getPreApprovalsTakenCount, getStartAndEndDate, getOrganizationCoveragePreApprovalSettings, getCustomGroupCoveragePreApprovalSettings } = require('../../common/commonfunctions');

module.exports.addUpdatePreApprovalRequests = async (parent, args, context, info) => {
    let organizationDbConnection;
    let errResult;
    let validationError = {};
    let errorMessage = "";
    try {
        let loginEmployeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        const { preApprovalId, employeeId, preApprovalType, duration, period, startDate, endDate, reason, status,overTimeHours } = args;
        // check their rights
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, formName.preApprovalRequests, '', 'UI', false, args.formId);
        if (Object.keys(checkRights).length > 0 && ((preApprovalId === 0 && checkRights.Role_Add === 1) || (preApprovalId > 0 && checkRights.Role_Update === 1))) {
            validationError = await validatePreApprovalRequestsInputs(args);
            //Check validation error exist or not
            if (Object.keys(validationError).length === 0) {

                let totalDays = 0;
                let preApprovalSettings = {};
                errorMessage = "";
                /** Get the organization level Pre-approval settings */
                preApprovalSettings = await getOrganizationCoveragePreApprovalSettings(organizationDbConnection, preApprovalType);
                if (Object.keys(preApprovalSettings).length === 0) {
                    /** Get the Custom group level Pre-approval settings */
                    preApprovalSettings = await getCustomGroupCoveragePreApprovalSettings(organizationDbConnection, preApprovalType, employeeId)
                }
                if (Object.keys(preApprovalSettings).length === 0) {
                    throw ('CHR0051');
                }
                //Get the type of day
                let typeOfDay = preApprovalSettings.typeOfDay;

                typeOfDay = typeOfDay && typeOfDay.length ? JSON.parse(typeOfDay) : [];
                if (preApprovalSettings.restrictSandwich === "Yes" && preApprovalType.toLowerCase() === 'work from home' && status !== "Cancelled") {
                    let sandwichExist = await checkSandwichExist(startDate, endDate, organizationDbConnection, employeeId, preApprovalType, preApprovalSettings, preApprovalId);
                    if (sandwichExist) {
                        throw 'CHR0071';
                    }
                }
                let workflowFormId = getWorkflowFormId(preApprovalType);
                let approvalRequestData = {};
                if (preApprovalId) {
                    //If it is a update action then update the Updated_on and Updated_By
                    approvalRequestData = await getApprovalRequest(organizationDbConnection, preApprovalId);
                }
                if (status !== 'Cancel Applied' && status !== 'Cancelled') {

                    if (preApprovalId) {
                        if (approvalRequestData.Status !== 'Applied') {
                            errorMessage = "Once the record is approved / cancelled / rejected, editing will not be permitted.";
                        }

                        ({ errorResponse:errorMessage } = await validateAttendanceExist(organizationDbConnection,{ employeeId,startDate,endDate, preApprovalType, actionType: 'Editing' }));
                    }


                    let preApprovalsExist = await getPreApprovalsTakenCount(organizationDbConnection, employeeId, preApprovalType, startDate, endDate, preApprovalId, preApprovalSettings.typeOfDay);
                    if (preApprovalsExist) {
                        validationError['IVE0317'] = commonLib.func.getError('', 'IVE0317').message2;
                    }

                    /** We do not want to restrict the admin to add the pre-approval based on the advance notification days */
                    if (checkRights.Employee_Role.toLowerCase() !== 'admin' && preApprovalSettings.advanceNotificationDays > 0) {

                        // Get the current date
                        const currentDate = new Date();
                        // Add 5 days
                        currentDate.setDate(currentDate.getDate() + parseInt(preApprovalSettings.advanceNotificationDays));

                        if (startDate < currentDate.toISOString().split('T')[0]) {
                            validationError['IVE0316'] = commonLib.func.getError('', 'IVE0316').message1;
                        }
                    }

                    //Throw validation error if exist
                    if (Object.keys(validationError).length > 0) {
                        throw ('IVE0000');
                    }

                    const result = await commonLib.shiftAndTimeManagement.fetchEmployeeWeekOffAndHolidayDetails(
                        organizationDbConnection,
                        employeeId,
                        startDate,
                        endDate
                    );
                    // Extract employee-specific data safely
                    const employeeData = result?.[employeeId] || {};
                    // Destructure with fallback defaults
                    const {
                        weekOffAndHolidayDetails: weekOffHolidayDetails = [],
                      workScheduleToSend = []
                    } = employeeData;
                    let restrictNextDay = 0;
                    let shiftNotScheduledDates = [];
                    let nonHolidayDates = [], holidayDates = [];
                    let nonWeekOffDates = [], weekOffDates = [];
                    weekOffHolidayDetails.map((data) => {
                        if (!data.isShiftScheduled) {
                            shiftNotScheduledDates.push(moment(data.date).format('MMMM D, YYYY'));
                        }

                        if (preApprovalType.toLowerCase() === 'work from home') {
                            if (data.isShiftScheduled && !data.isHoliday && (!data.isWeekOffDay || data.weekOffDuration == 0.5)) {
                                totalDays++;
                            }
                        } else if (preApprovalType.toLowerCase() === 'on duty' || preApprovalType.toLowerCase() === 'overtime work') {
                            //If type of day is Business Working Day Check isShiftScheduled
                            //If type of day is Holiday Check isHoliday
                            //If type of day is Week Off Check isWeekOffDay

                            if (typeOfDay.includes('Business Working Day') && data.isShiftScheduled && !data.isHoliday && !data.isWeekOffDay) {
                                totalDays++;
                            }

                            if (typeOfDay.includes('Holiday') && data.isHoliday && data.isShiftScheduled) {
                                holidayDates.push(moment(data.date).format('MMMM D, YYYY'));
                                totalDays++;
                            } else {
                                nonHolidayDates.push(moment(data.date).format('MMMM D, YYYY'));
                            }

                            if (typeOfDay.includes('Week Off') && data.isWeekOffDay && !data.isHoliday && data.isShiftScheduled) {
                                weekOffDates.push(moment(data.date).format('MMMM D, YYYY'));
                                totalDays++;
                            } else {
                                nonWeekOffDates.push(moment(data.date).format('MMMM D, YYYY'));
                            }
                        }
                        else if (preApprovalType.toLowerCase() === 'work during week off') {
                            if (data.isShiftScheduled && data.isWeekOffDay) {
                                totalDays++;
                            } else {
                                nonWeekOffDates.push(moment(data.date).format('MMMM D, YYYY'));
                            }
                        }
                        else if (preApprovalType.toLowerCase() === 'work during holiday') {
                            if (data.isShiftScheduled && data.isHoliday) {
                                totalDays++;
                            } else {
                                nonHolidayDates.push(moment(data.date).format('MMMM D, YYYY'));
                            }
                        }
                        // if(data.isShiftScheduled && data.isHoliday && preApprovalSettings.restrictSandwich === 'Yes' &&
                        //  preApprovalSettings.restrictSandwichFor.includes("Work during holiday")){
                        //     console.log("restrict holiday sandwich");
                        //     restrictNextDay = 1;
                        // } else if(data.isShiftScheduled && data.isWeekOffDay && data.weekOffDuration != 0.5 && preApprovalSettings.restrictSandwich === 'Yes' &&
                        //  preApprovalSettings.restrictSandwichFor.includes("Work during week off") ){
                        //     console.log("restrict weekoff sandwich");
                        //     restrictNextDay = 1;
                        // } else {
                        //     restrictNextDay = 0;
                        // }
                    });

                    if (shiftNotScheduledDates.length > 0) {
                        errorMessage = "The employee has no shifts scheduled for " + shiftNotScheduledDates.join(" ,");
                    }

                    if (nonWeekOffDates.length > 0 && preApprovalType.toLowerCase() === 'work during week off') {
                        errorMessage = "Pre-approval request to 'work during week off' cannot be applied to non week-off dates, specifically for" + nonWeekOffDates.join(", ") + ".";
                    }

                    if (nonHolidayDates.length > 0 && preApprovalType.toLowerCase() === 'work during holiday') {
                        errorMessage = "Pre-approval request to 'work during holiday' cannot be granted for non-holiday dates, specifically, " + nonHolidayDates.join(", ") + ".";
                    }

                    if (preApprovalType.toLowerCase() === 'on duty') {
                        if (typeOfDay.includes('Holiday') && !typeOfDay.includes('Week Off') && !typeOfDay.includes('Business Working Day') && nonHolidayDates.length > 0) {
                            errorMessage = "Pre-approval request to 'on duty' cannot be granted for non-holiday dates, specifically, " + nonHolidayDates.join(", ") + ".";
                        } else if (typeOfDay.includes('Week Off') && !typeOfDay.includes('Holiday') && !typeOfDay.includes('Business Working Day') && nonWeekOffDates.length > 0) {
                            errorMessage = "Pre-approval request to 'on duty' cannot be granted for non week-off dates, specifically, " + nonWeekOffDates.join(", ") + ".";
                        } else if (typeOfDay.includes('Business Working Day') && !typeOfDay.includes('Week Off') && typeOfDay.includes('Holiday') && weekOffDates.length > 0) {
                            errorMessage = "Pre-approval request to 'on duty' cannot be granted for week-off dates, specifically, " + weekOffDates.join(", ") + ".";
                        } else if (typeOfDay.includes('Business Working Day') && !typeOfDay.includes('Holiday') && typeOfDay.includes('Week Off') && holidayDates.length > 0) {
                            errorMessage = "Pre-approval request to 'on duty' cannot be granted for holiday dates, specifically, " + holidayDates.join(", ") + ".";
                        }
                    }
                    if (preApprovalType.toLowerCase() === 'overtime work') {
                        if (typeOfDay.includes('Holiday') && !typeOfDay.includes('Week Off') && !typeOfDay.includes('Business Working Day') && nonHolidayDates.length > 0) {
                            errorMessage = "Pre-approval request to 'overtime work' cannot be granted for non-holiday dates, specifically, " + nonHolidayDates.join(", ") + ".";
                        } else if (typeOfDay.includes('Week Off') && !typeOfDay.includes('Holiday') && !typeOfDay.includes('Business Working Day') && nonWeekOffDates.length > 0) {
                            errorMessage = "Pre-approval request to 'overtime work' cannot be granted for non week-off dates, specifically, " + nonWeekOffDates.join(", ") + ".";
                        } else if (typeOfDay.includes('Business Working Day') && !typeOfDay.includes('Week Off') && typeOfDay.includes('Holiday') && weekOffDates.length > 0) {
                            errorMessage = "Pre-approval request to 'overtime work' cannot be granted for week-off dates, specifically, " + weekOffDates.join(", ") + ".";
                        } else if (typeOfDay.includes('Business Working Day') && !typeOfDay.includes('Holiday') && typeOfDay.includes('Week Off') && holidayDates.length > 0) {
                            errorMessage = "Pre-approval request to 'overtime work' cannot be granted for holiday dates, specifically, " + holidayDates.join(", ") + ".";
                        }
                    }


                    if (totalDays == 0) {
                        if (preApprovalType.toLowerCase() === 'work from home')
                            errorMessage = "Please note, the pre-approval request to work from home is only valid for standard working days.";
                        else if (preApprovalType.toLowerCase() === 'on duty')
                            errorMessage = "Please note, the pre-approval request to on duty is only valid for specific days designated as " + typeOfDay.join(", ") + ".";
                        else if (preApprovalType.toLowerCase() === 'work during week off')
                            errorMessage = "A pre-approval request to work during a 'week off' can only be made for those specific days designated as 'week off'.";
                        else if (preApprovalType.toLowerCase() === 'work during holiday')
                            errorMessage = "The option to request pre-approval to work during a holiday is only available for official holiday dates.";
                        else if (preApprovalType.toLowerCase() === 'overtime work')
                            errorMessage = "Please note, the pre-approval request for overtime work is only valid for specific days designated as " + typeOfDay.join(", ") + ".";
                    }
                    // if (preApprovalType?.toLowerCase() === 'overtime work') {
                    //     const matchedSchedule = workScheduleToSend?.find(data =>
                    //         moment(data?.Work_Schedule_Date).format('YYYY-MM-DD') === moment(startDate).format('YYYY-MM-DD')
                    //     );
                    
                    //     if (matchedSchedule && matchedSchedule.Regular_To) {
                    //         const regularEndTime = moment(matchedSchedule.Regular_To, 'YYYY-MM-DD HH:mm:ss');
                    //         const overtimeDuration = moment.duration(args?.overTimeHours || '00:00');
                    //         const overtimeEndTime = moment(regularEndTime).add(overtimeDuration);
                    
                    //         if (overtimeEndTime.isAfter(regularEndTime)) {
                    //             errorMessage = `Pre-approval request to 'Overtime Work' cannot be granted for half day work schedule, specifically, ${matchedSchedule.Work_Schedule_Date}.`;
                    //         }
                    //     }
                    // }                    

                    const { start, end } = await getStartAndEndDate(new Date(startDate), preApprovalSettings.period);

                    if (end < endDate) {
                        let periodMsg = await getPeriodMessage(preApprovalSettings.period)
                        errorMessage = "You can submit request for pre-approval for dates that are within the same " + periodMsg + " in a single request.";
                    }

                    if (errorMessage) {
                        throw ('CHR0065');
                    }

                    let preApprovalsTaken = await getPreApprovalsTakenCount(organizationDbConnection, employeeId, preApprovalType, start, end, preApprovalId, preApprovalSettings.typeOfDay);
                    totalDays = duration.toLowerCase() === 'half day' ? 0.5 : totalDays;
                    console.log(' noOfPreApprovalRequest: ', preApprovalSettings.noOfPreApprovalRequest, ', preApprovalsTaken: ', preApprovalsTaken, ", totalDays: ", totalDays);
                    if (preApprovalsTaken + totalDays > preApprovalSettings.noOfPreApprovalRequest) {
                        validationError['IVE0317'] = commonLib.func.getError('', 'IVE0317').message3;
                    }

                    if (Object.keys(validationError).length > 0) {
                        throw ('IVE0000');
                    }
                } else {
                    ({ errorResponse:errorMessage } = await validateAttendanceExist(organizationDbConnection,{ employeeId,startDate,endDate, preApprovalType, actionType: 'Cancellation' }));
                    totalDays = args.totalDays;
                }
                if (
                    preApprovalSettings.maxDaysAllowedPerRequest > 0 &&
                    parseFloat(parseFloat(totalDays).toFixed(2)) > parseFloat(parseFloat(preApprovalSettings.maxDaysAllowedPerRequest).toFixed(2))
                ) {
                    throw 'CHR00116';
                }                

                if (errorMessage) {
                    throw ('CHR0065');
                }
                let eventId = await getWorkFlowEventId(organizationDbConnection, preApprovalSettings.workflowId);

                let currentDateTime = moment().utc().format('YYYY-MM-DD HH:mm:ss');

                let preApprovalRequestsData = {
                    "Pre_Approval_Id": preApprovalId,
                    "Employee_Id": employeeId,
                    "Pre_Approval_Type": preApprovalType,
                    "Type_Of_Day": preApprovalSettings.typeOfDay,
                    "Duration": preApprovalType.toLowerCase() === 'overtime work' ? 'Full Day' : duration,
                    "Period": period,
                    "Start_Date": startDate,
                    'Over_Time_Hours': overTimeHours || null,
                    "End_Date": endDate,
                    "Total_Days": preApprovalType.toLowerCase() === 'overtime work' ? 1 : totalDays,
                    "Reason": reason,
                    "Status": status
                }

                if (preApprovalId) {

                    preApprovalRequestsData.Updated_On = currentDateTime;
                    preApprovalRequestsData.Updated_By = loginEmployeeId;

                    let instanceData = { ...preApprovalRequestsData };
                    instanceData.Added_On = approvalRequestData.Added_On;
                    instanceData.Added_By = approvalRequestData.Added_By;
                    instanceData.File_Name = args.fileName,
                        instanceData.File_Size = args.fileSize

                    if (status != 'Cancelled') {
                        let initiatePreApprovalWorkfflow = await commonLib.func.initiateWorkflow(eventId, instanceData, context.Org_Code, workflowFormId, loginEmployeeId);
                        if (initiatePreApprovalWorkfflow.status == 200 && initiatePreApprovalWorkfflow.data && initiatePreApprovalWorkfflow.data.workflowProcessInstanceId) {
                            preApprovalRequestsData.Process_Instance_Id = initiatePreApprovalWorkfflow.data.workflowProcessInstanceId
                            await deleteOldApprovalRecords(organizationDbConnection, approvalRequestData.Process_Instance_Id);
                        }
                    } else {
                        await deleteOldApprovalRecords(organizationDbConnection, approvalRequestData.Process_Instance_Id);
                    }


                    if ("formId" in preApprovalRequestsData) {
                        delete preApprovalRequestsData.formId;
                    }
                    return (
                        organizationDbConnection
                            .transaction(function (trx) {
                                return (
                                    organizationDbConnection(ehrTables.preApprovalRequests)
                                        .update(preApprovalRequestsData)
                                        .where("Pre_Approval_Id", preApprovalId)
                                        .transacting(trx)
                                        .then(async (updateResult) => {
                                            if (updateResult) {

                                                await insertUpdateDocuments(organizationDbConnection, args.fileName, args.fileSize, preApprovalId);
                                                // Log message: Add projects Project_Id - 1
                                                let systemLogParams = {
                                                    action: systemLogs.roleUpdate,
                                                    userIp: context.User_Ip,
                                                    employeeId: loginEmployeeId,
                                                    formName: formName.preApprovalRequests,
                                                    trackingColumn: 'Pre_Approval_Id',
                                                    organizationDbConnection: organizationDbConnection,
                                                    uniqueId: preApprovalId
                                                };
                                                //Call function to add the system log
                                                await commonLib.func.createSystemLogActivities(systemLogParams);
                                                return true;
                                            }
                                            else {
                                                throw 'CHR0068'
                                            }
                                        }


                                        )
                                )
                            }).then((data) => {
                                //destroy the connection
                                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                return { errorCode: "", message: "Pre-approval request details updated successfully." };

                            }).catch((catchError) => {
                                console.log('Error while updaing the pre-approval request details details', catchError);
                                //Destroy DB connection
                                organizationDbConnection ? organizationDbConnection.destroy() : null;

                                errResult = commonLib.func.getError(errorCode, 'CHR0050');
                                throw new ApolloError(errResult.message, errResult.code);
                            })
                    )
                } else {
                    //If it is a add action then update the Added_on and Added_By
                    preApprovalRequestsData.Added_On = currentDateTime;
                    preApprovalRequestsData.Added_By = loginEmployeeId;
                    let instanceData = { ...preApprovalRequestsData };
                    instanceData.File_Name = args.fileName,
                        instanceData.File_Size = args.fileSize

                    return (
                        organizationDbConnection(ehrTables.preApprovalRequests)
                            .insert(preApprovalRequestsData)
                            .then(async (insertData) => {
                                if (insertData) {
                                    await insertUpdateDocuments(organizationDbConnection, args.fileName, args.fileSize, insertData[0])
                                    let preApprovalId = insertData[0];
                                    preApprovalRequestsData.Pre_Approval_Id = insertData[0];
                                    instanceData.Pre_Approval_Id = insertData[0];
                                    let initiatePreApprovalWorkfflow = await commonLib.func.initiateWorkflow(eventId, instanceData, context.Org_Code, workflowFormId, loginEmployeeId);
                                    if (initiatePreApprovalWorkfflow.status == 200 && initiatePreApprovalWorkfflow.data && initiatePreApprovalWorkfflow.data.workflowProcessInstanceId) {
                                        let updateParam = { Process_Instance_Id: initiatePreApprovalWorkfflow.data.workflowProcessInstanceId };
                                        let updateData = await commonLib.func.updateTableBasedOnCondition(organizationDbConnection, ehrTables.preApprovalRequests, updateParam, "Pre_Approval_Id", insertData[0]);

                                        // Log message: Add projects Project_Id - 1
                                        let systemLogParams = {
                                            action: systemLogs.roleAdd,
                                            userIp: context.User_Ip,
                                            employeeId: loginEmployeeId,
                                            formName: formName.preApprovalRequests,
                                            trackingColumn: 'Pre_Approval_Id',
                                            organizationDbConnection: organizationDbConnection,
                                            uniqueId: preApprovalId
                                        };
                                        //Call function to add the system log
                                        await commonLib.func.createSystemLogActivities(systemLogParams);

                                        //destroy the connection
                                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                                        return { errorCode: "", message: "Pre-approval details added successfully." };
                                    }
                                    else {
                                        throw ('CHR0044');
                                    }


                                } else {
                                    throw ('CHR0068');
                                }
                            })
                    )
                }
            } else {
                throw 'IVE0000';
            }
        }
        else {
            if (preApprovalId) {
                console.log("The employee does not have edit access.");
                throw '_DB0102';
            } else {
                console.log("The employee does not have add access.");
                throw '_DB0101';
            }
        }
    }
    catch (e) {
        console.log('Error in addUpdatePreApprovalRequests  function main catch block.', e);
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        //If validation error exist
        if (e === 'IVE0000') {
            console.log('Validation error in the addUpdatePreApprovalRequests  function', validationError);
            errResult = commonLib.func.getError('', 'IVE0000');
            throw new UserInputError(errResult.message, { validationError: validationError });//Return error response
        } else if (e === 'CHR0065') {
            throw new ApolloError(errorMessage, 'CHR0065');
        } else {
            errResult = commonLib.func.getError(e, 'CHR0050');
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
}

async function getWorkFlowEventId(organizationDbConnection, workflowId) {
    try {
        return (
            organizationDbConnection(ehrTables.workflows + "as A")
                .select('A.Event_Id as eventId')
                .from(ehrTables.workflows + " as A")
                .where("A.Workflow_Id", workflowId)
                .then(async (workflowData) => {
                    if (workflowData && workflowData.length > 0) {
                        return workflowData[0].eventId;
                    } else {
                        throw ('CHR0054');
                    }

                })
                .catch((err) => {
                    throw ('CHR0054');
                })
        )
    } catch (err) {
        console.log('Error in getOrganizationCoveragePreApprovalSettings catch block', err);
        throw ('CHR0054');
    }
}

async function getApprovalRequest(organizationDbConnection, preApprovalId) {
    try {
        return (
            organizationDbConnection(ehrTables.preApprovalRequests + "as A")
                .select('*')
                .from(ehrTables.preApprovalRequests + " as A")
                .where("A.Pre_Approval_Id", preApprovalId)
                .then(async (preApprovalData) => {
                    if (preApprovalData && preApprovalData.length > 0) {
                        return preApprovalData[0];
                    } else {
                        console.log("Pre-approval record does not exist");
                        throw ('CHR0055');
                    }
                })
                .catch((catchErr) => {
                    console.log("Error in getApprovalRequest .catch block", catchErr);
                    throw ('CHR0055');
                })
        )
    } catch (mainErr) {
        console.log('Error in getApprovalRequest catch block', mainErr);
        throw ('CHR0055');
    }
}

async function deleteOldApprovalRecords(organizationDbConnection, processInstanceId) {
    try {
        return (
            organizationDbConnection(ehrTables.taUserTask)
                .delete()
                .where("Process_Instance_Id", processInstanceId)
                .then(async (deleteUserTask) => {
                    organizationDbConnection(ehrTables.taUserTaskHistory)
                        .delete()
                        .where("Process_Instance_Id", processInstanceId)
                        .then(async (deleteUserTask) => {
                            return true;
                        })
                        .catch((err) => {
                            throw ('CHR0052');
                        })
                })
                .catch((err) => {
                    throw ('CHR0052');
                })
        )
    } catch (err) {
        console.log('Error in getApprovalRequest catch block', err);
        throw ('CHR0052');
    }
}

async function getPeriodMessage(period) {
    try {
        switch (period.toLowerCase()) {
            case 'weekly':
                return 'week';
            case 'monthly':
                return 'month';
            case 'quarterly':
                return 'quarter';
            case 'half yearly':
                return 'half year';
            case 'yearly':
                return 'year';
            default:
                return '';
        }
    } catch (err) {
        console.log('Error in getPeriodMessage catch block', err);
        throw err;
    }
}

async function insertUpdateDocuments(organizationDbConnection, fileName, fileSize, preApprovalId) {
    try {
        return (
            organizationDbConnection(ehrTables.preApprovalDocuments)
                .delete()
                .where('Pre_Approval_Id', preApprovalId)
                .then(() => {
                    if (fileName) {
                        return (
                            organizationDbConnection(ehrTables.preApprovalDocuments)
                                .insert({
                                    'Pre_Approval_Id': preApprovalId,
                                    'File_Name': fileName,
                                    'File_Size': fileSize
                                })
                                .then((data) => {
                                    if (data) {
                                        return true
                                    } else {
                                        throw 'CHR0069'
                                    }
                                })
                        )
                    }
                })
        )
    } catch (err) {
        console.log('Error while adding documents', err)
        throw 'CHR0069'
    }
}
async function checkSandwichExist(startDate, endDate, organizationDbConnection, employeeId, preApprovalType, preApprovalSettings, preApprovalId) {
    try {
        let [stryear, strmonth] = startDate.split('-');
        let monthStartDate = moment([stryear, strmonth - 1]).subtract(10, 'days').format('YYYY-MM-DD');
        let monthEndDate = moment(startDate).endOf('month').add(10, 'days').format('YYYY-MM-DD');
        let currentDate = new Date(startDate);
        let currentEndDate = new Date(endDate);
        let restrictSandwichForval = JSON.parse(
            preApprovalSettings.restrictSandwichFor
        ).join(",")
        let weekOffHolidayDetails = await commonLib.payroll.getWeekOffAndHolidays(organizationDbConnection, employeeId, monthStartDate, monthEndDate);
        if (restrictSandwichForval.includes("Week Off") && restrictSandwichForval.includes("Holiday")) {
            if (await checkDatesForWeekOff(startDate, endDate, weekOffHolidayDetails, true) || await checkDatesForholiday(startDate, endDate, weekOffHolidayDetails, true)) {
                throw 'CHR0070'
            }
        }
        else if (restrictSandwichForval === "Week Off") {
            if (await checkDatesForWeekOff(startDate, endDate, weekOffHolidayDetails, false)) {
                throw 'CHR0070'
            }
        }
        else {
            if (await checkDatesForholiday(startDate, endDate, weekOffHolidayDetails, false)) {
                throw 'CHR0070'
            }
        }
        let weekOffStartCount = 0;
        let weekoffEndCount = 0;
        let holidayStartCount = 0;
        let holidayEndCount = 0;
        let preApprovalTakenCount = 0;
        let count = 0;
        while (true) {
            // Get the previous date

            currentDate.setDate(currentDate.getDate() - 1);
            currentEndDate.setDate(currentEndDate.getDate() + 1)
            const currentDateStr = currentDate.toISOString().split('T')[0];
            let currentDateEnd = currentEndDate.toISOString().split('T')[0];
            let weekOffDayObject;
            let endweekOffDayObject;
            let holidatstartDate;
            let holidayEndDayObject;
            if (weekOffStartCount === 0 && ((restrictSandwichForval.includes("Week Off") && restrictSandwichForval.includes("Holiday")) || restrictSandwichForval === "Week Off")) {

                weekOffDayObject = weekOffHolidayDetails.find(
                    (data) => {
                        if (!(restrictSandwichForval.includes("Week Off") && restrictSandwichForval.includes("Holiday"))) {
                            return (data.date === currentDateStr && data.isWeekOffDay === 0 && data.weekOffDuration === 0
                                || data.date === currentDateStr && data.isWeekOffDay === 1 && data.weekOffDuration === 0.5)
                        }
                        else {
                            return (data.date === currentDateStr && data.isWeekOffDay === 0 && data.weekOffDuration === 0 && data.isHoliday === 0
                                || data.date === currentDateStr && data.isWeekOffDay === 1 && data.weekOffDuration === 0.5 && data.isHoliday === 0)
                        }
                    }

                );
            }
            else if (weekOffStartCount === 0) {
                weekOffStartCount++;
            }

            if (weekoffEndCount === 0 && ((restrictSandwichForval.includes("Week Off") && restrictSandwichForval.includes("Holiday")) || restrictSandwichForval === "Week Off")) {
                endweekOffDayObject = weekOffHolidayDetails.find(
                    (data) => {
                        if (!(restrictSandwichForval.includes("Week Off") && restrictSandwichForval.includes("Holiday"))) {
                            return (data.date === currentDateEnd && data.isWeekOffDay === 0 && data.weekOffDuration === 0
                                || data.date === currentDateEnd && data.isWeekOffDay === 1 && data.weekOffDuration === 0.5)
                        }
                        else {
                            return (data.date === currentDateEnd && data.isWeekOffDay === 0 && data.weekOffDuration === 0 && data.isHoliday === 0
                                || data.date === currentDateEnd && data.isWeekOffDay === 1 && data.weekOffDuration === 0.5 && data.isHoliday === 0)
                        }
                    }
                );
            }
            else if (weekoffEndCount === 0) {
                weekoffEndCount++;
            }
            if (holidayStartCount === 0 && ((restrictSandwichForval.includes("Week Off") && restrictSandwichForval.includes("Holiday")) || restrictSandwichForval === "Holiday")) {
                holidatstartDate = weekOffHolidayDetails.find(
                    (data) => {
                        if (!(restrictSandwichForval.includes("Week Off") && restrictSandwichForval.includes("Holiday"))) {
                            return (data.date === currentDateStr && data.isHoliday === 0)
                        }
                        else {
                            return (data.date === currentDateStr && data.isWeekOffDay === 0 && data.weekOffDuration === 0 && data.isHoliday === 0
                                || data.date === currentDateStr && data.isWeekOffDay === 1 && data.weekOffDuration === 0.5 && data.isHoliday === 0)
                        }

                    })
            }
            else if (holidayStartCount === 0) {
                holidayStartCount++;
            }
            if (holidayEndCount === 0 && ((restrictSandwichForval.includes("Week Off") && restrictSandwichForval.includes("Holiday")) || restrictSandwichForval === "Holiday")) {
                holidayEndDayObject = weekOffHolidayDetails.find(
                    (data) => {
                        if (!(restrictSandwichForval.includes("Week Off") && restrictSandwichForval.includes("Holiday"))) {
                            return (data.date === currentDateEnd && data.isHoliday === 0)
                        }
                        else {
                            return (data.date === currentDateEnd && data.isWeekOffDay === 0 && data.weekOffDuration === 0 && data.isHoliday === 0
                                || data.date === currentDateEnd && data.isWeekOffDay === 1 && data.weekOffDuration === 0.5 && data.isHoliday === 0)
                        }

                    }
                );
            }
            else if (holidayEndCount === 0) {
                holidayEndCount++;
            }

            if (weekOffDayObject) {
                weekOffStartCount++;
            }
            if (endweekOffDayObject) {
                weekoffEndCount++;
            }
            if (holidatstartDate) {
                holidayStartCount++;

            }
            if (holidayEndDayObject) {
                holidayEndCount++;
            }
            if ((weekOffDayObject && count > 0) || (endweekOffDayObject && count > 0) || (holidatstartDate && count > 0) || (holidayEndDayObject && count > 0)) {
                let dateObj = [];
                if (weekOffDayObject) {
                    dateObj.push(weekOffDayObject.date)
                }
                if (endweekOffDayObject) {
                    dateObj.push(endweekOffDayObject.date)
                }
                if (holidatstartDate) {
                    dateObj.push(holidatstartDate.date)
                }
                if (holidayEndDayObject) {
                    dateObj.push(holidayEndDayObject.date);
                }
                await Promise.all(dateObj.map(async (data) => {
                    let preapprovalExist = await getPreApprovalsTakenCount(organizationDbConnection, employeeId, preApprovalType, data, data, preApprovalId, preApprovalSettings.typeOfDay);
                    if (preapprovalExist > 0) {
                        preApprovalTakenCount = preapprovalExist;
                    }
                }));

            }

            if (preApprovalTakenCount > 0) {
                return preApprovalTakenCount;
            }
            if (weekOffStartCount > 0 && weekoffEndCount > 0 && holidayStartCount > 0 && holidayEndCount > 0) {
                break;
            }
            count++;


        }

    } catch (err) {
        throw err;
    }


}
async function checkDatesForWeekOff(startDate, endDate, weekOffAndHolidayDetails, both) {
    let currentDate = new Date(startDate);
    let currentEndDate = new Date(endDate);
    while (currentDate <= currentEndDate) {
        const currentDateStr = currentDate.toISOString().split('T')[0];
        let holidayCheck = true;
        const weekOffDayObject = weekOffAndHolidayDetails.find(
            (data) => data.date === currentDateStr && data.isWeekOffDay === 1 && data.weekOffDuration === 1
        );
        if (weekOffDayObject && !both) {
            const weekOffNextDayObject = weekOffAndHolidayDetails.find(
                (data) => {
                    const holDate = new Date(currentDate);
                    holDate.setDate(holDate.getDate() + 1);
                    const holDatestr = holDate.toISOString().split('T')[0];
                    return (
                        data.date === holDatestr && data.isHoliday !== 1
                    )
                }
            );
            const weekOffPreviousDayObject = weekOffAndHolidayDetails.find(
                (data) => {
                    const holDate = new Date(currentDate);
                    holDate.setDate(holDate.getDate() - 1);
                    const holDatestr = holDate.toISOString().split('T')[0];
                    return (
                        data.date === holDatestr && data.isHoliday !== 1
                    )
                }
            );
            if (!weekOffNextDayObject || !weekOffPreviousDayObject) {
                holidayCheck = false;
            }
        }


        if (weekOffDayObject && holidayCheck) {

            return true;
        }

        // Move to the next date
        currentDate.setDate(currentDate.getDate() + 1);
    }
    return false;
}
async function checkDatesForholiday(startDate, endDate, weekOffAndHolidayDetails, both) {
    let currentDate = new Date(startDate);
    let currentEndDate = new Date(endDate);
    while (currentDate <= currentEndDate) {
        const currentDateStr = currentDate.toISOString().split('T')[0];
        let weekendCheck = true;
        const holidayDayObject = weekOffAndHolidayDetails.find(
            (data) => data.date === currentDateStr && data.isHoliday === 1
        );
        if (holidayDayObject && !both) {
            const holidayNextDayObject = weekOffAndHolidayDetails.find(
                (data) => {
                    const weekDate = new Date(currentDate);
                    weekDate.setDate(weekDate.getDate() + 1);
                    const weekDateStr = weekDate.toISOString().split('T')[0];
                    return (
                        data.date === weekDateStr && data.isWeekOffDay !== 1 && data.weekOffDuration !== 0.5)
                }
            );
            const holidayPreviousDayObject = weekOffAndHolidayDetails.find(
                (data) => {
                    const weekDate = new Date(currentDate);
                    weekDate.setDate(weekDate.getDate() - 1);
                    const weekDateStr = weekDate.toISOString().split('T')[0];
                    return (
                        data.date === weekDateStr && data.isWeekOffDay !== 1 && data.weekOffDuration !== 0.5)
                }
            );
            if (!holidayNextDayObject || !holidayPreviousDayObject) {
                weekendCheck = false;
            }
        }

        if (holidayDayObject && weekendCheck) {
            return true;
        }

        // Move to the next date
        currentDate.setDate(currentDate.getDate() + 1);
    }
    return false;
}

function getWorkflowFormId(preApprovalType) {
    let formId;
    if (preApprovalType.toLowerCase() === 'work from home') {
        formId = 244;
    } else if (preApprovalType.toLowerCase() === 'work during week off') {
        formId = 245
    }
    else if (preApprovalType.toLowerCase() === 'work during holiday') {
        formId = 246
    }
    else if (preApprovalType.toLowerCase() === 'on duty') {
        formId = 301
    }
    else if(preApprovalType.toLowerCase()=== "overtime work"){
        formId = 366
    }
    return formId;
}

async function validateAttendanceExist(organizationDbConnection,validationDetails) {
    try {
        let { employeeId,startDate,endDate, actionType,preApprovalType } = validationDetails;
        let errorMessage = '';
        let attendanceCount = 0;

        let approvalStatus = ['Draft','Applied', 'Returned', 'Approved','Cancel Applied'];
        const attendanceDetails = await commonLib.shiftAndTimeManagement.getAttendanceDetails(organizationDbConnection,[employeeId],startDate,endDate,approvalStatus);
        console.log("attendanceDetails",attendanceDetails,[employeeId],startDate,endDate);
        // Only process if attendance data is available
        if (attendanceDetails && attendanceDetails.length > 0) {
            let actionTypeToValidate = actionType?.toLowerCase() || '';
            let preApprovalTypeToValidate = preApprovalType?.toLowerCase() || '';

            if (preApprovalTypeToValidate === 'work from home' && actionTypeToValidate === 'cancellation') {
                attendanceCount = attendanceDetails.filter(row =>
                    row.Checkin_Work_Place_Id === 1 || row.Checkout_Work_Place_Id === 1
                ).length;
            } else {
                attendanceCount = attendanceDetails.length;
            }

            if (attendanceCount > 0) {
                errorMessage = `${actionType} of the record is not permitted as attendance already exists.`;
            }   
        }
        return { errorResponse: errorMessage };
    } catch (err) {
        console.log('Error in validateAttendanceExist function main catch block.', err,validationDetails);
        throw err;
    }
}
