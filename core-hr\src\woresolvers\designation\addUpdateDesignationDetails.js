// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;

// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../common/tablealias');
//Require apollo server to return error message
const { UserInputError, ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formName, formIds, systemLogs } = require('../../../common/appconstants');
//Require validation function
const { validateAddUpdateDesignationInputs } = require('../../../common/inputValidations');
const { insertDataInTable, updateDataSetupDashboard } = require('../../../common/commonfunctions');
const { validateDesignationAssociated, validateDesignationAssociatedToJobRole } = require('../../../common/designationValidation');

module.exports.addUpdateDesignationDetails = async (parent, args, context, info) => {
    let organizationDbConnection;
    let errResult;
    let validationError = {};
    let desAssociatedForms = '';
    let desAssosiateToJobRole = '';
    let isEntomoDataChanged = false;
    try {
        console.log("Inside addUpdateDesignationDetails function...");
        let loginEmployeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        const { designationId, designationCode, designationName, level, probationDays, gradeId, employeeConfirmation, attendanceEnforcedPayment, attendanceEnforcedGeoLocation,
            description, noticePeriodDaysWithinProbation, noticePeriodDaysAfterProbation, status, accreditationId, noticePeriodPayByEmployer, noticePeriodPayByEmployee } = args;
        validationError = await validateAddUpdateDesignationInputs(args);

        //Check validation error exist or not
        if (Object.keys(validationError).length === 0) {
            // check their rights
            let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, formName.designationOrPosition, '', 'UI');
            if (Object.keys(checkRights).length > 0 && ((designationId === 0 && checkRights.Role_Add === 1) || (designationId > 0 && checkRights.Role_Update === 1))) {
                let queryDesignation = organizationDbConnection(ehrTables.designation)
                    .select('Designation_Name', 'Designation_Code')
                    .where(function () {
                        if (designationCode && designationCode.length) {
                            this.where('Designation_Code', designationCode)
                        } else {
                            this.where('Designation_Name', designationName)
                        }
                    })

                if (designationId) {
                    queryDesignation = queryDesignation.whereNot('Designation_Id', designationId)
                }

                let designationNameExist = await queryDesignation
                    .then((result) => {
                        return result;
                    }).catch((e) => {
                        console.log('Error in checking the designation name exists .catch block', e);
                        throw (commonLib.func.getError(e, 'CHR0026'));
                    })

                if (designationNameExist.length === 0) {
                    let desAssociatedFormsErrorCode = '';
                    if (designationId && status === "InActive") {
                        desAssociatedForms = await validateDesignationAssociated(organizationDbConnection, designationId, designationName, 'edit');
                        if (desAssociatedForms) {
                            desAssociatedFormsErrorCode = 'CHR0032';
                        }
                        desAssosiateToJobRole =  await validateDesignationAssociatedToJobRole(organizationDbConnection, designationId);
                        if (desAssosiateToJobRole?.isAssociated) {
                            throw "CHR00116"
                        }

                    }

                    if (desAssociatedFormsErrorCode) {
                        throw desAssociatedFormsErrorCode;
                    } else {
                        //Get the login employee current date and time based on login employee location
                        let currentDateTime = await commonLib.func.getEmployeeTimeZone(loginEmployeeId, organizationDbConnection, 1);

                        let designationData = {
                            "Designation_Id": designationId,
                            "Designation_Name": designationName,
                            "Designation_Code": designationCode && designationCode.length ? designationCode : null,
                            "Probation_Days": probationDays,
                            "Description": description,
                            "Grade_Id": gradeId,
                            "Level": level ? level : null,
                            "Employee_Confirmation": employeeConfirmation,
                            "Attendance_Enforced_Payment": attendanceEnforcedPayment,
                            "Attendance_Enforced_GeoLocation": attendanceEnforcedGeoLocation,
                            "Notice_Period_Days_Within_Probation": noticePeriodDaysWithinProbation,
                            "Notice_Period_Days_After_Probation": noticePeriodDaysAfterProbation,
                            "Designation_Status": status,
                            "Notice_Period_Pay_By_Employer": noticePeriodPayByEmployer === 'Yes' ? 'Yes' : 'No',
                            "Notice_Period_Pay_By_Employee": noticePeriodPayByEmployee === 'Yes' ? 'Yes' : 'No'
                        }


                        if (designationId) {
                            //Validate the entomo related data update
                            if (context.partnerid && context.partnerid.toLowerCase() === 'entomo') {
                                isEntomoDataChanged = await isEntomoRelatedDataChanged(organizationDbConnection, designationData);
                            }
                            //If it is a update action then update the Updated_on and Updated_By
                            designationData.Updated_On = currentDateTime;
                            designationData.Updated_By = loginEmployeeId;

                            return (
                                organizationDbConnection
                                    .transaction(function (trx) {
                                        return (
                                            organizationDbConnection(ehrTables.designation)
                                                .update(designationData)
                                                .where("Designation_Id", designationId)
                                                .transacting(trx)
                                                .then(async (designationUpdateData) => {
                                                    await deleteDesignationAdditionalDetails(organizationDbConnection, trx, designationId);
                                                    await addDesignationAdditionalDetails(organizationDbConnection, trx, designationId, accreditationId);

                                                    // Log message: Add designations Designation_Id - 1
                                                    let systemLogParams = {
                                                        action: systemLogs.roleUpdate,
                                                        userIp: context.User_Ip,
                                                        employeeId: loginEmployeeId,
                                                        formName: formName.designationOrPosition,
                                                        trackingColumn: 'Designation_Name',
                                                        organizationDbConnection: organizationDbConnection,
                                                        uniqueId: designationName
                                                    };
                                                    //Call function to add the system log
                                                    await commonLib.func.createSystemLogActivities(systemLogParams);
                                                    return true;
                                                })
                                        )
                                    }).then(async (data) => {

                                        if (data) {
                                            //If the partner is "entomo" call the async
                                            if (context.partnerid && context.partnerid.toLowerCase() === 'entomo' && isEntomoDataChanged) {
                                                designationData.Code = designationData.Designation_Code
                                                designationData.Name = designationData.Designation_Name
                                                designationData.Old_Code = isEntomoDataChanged

                                                let inputData = {
                                                    'orgCode': context.Org_Code, 'partnerId': context.partnerid, 'inputParams': {
                                                        entityId: designationId,
                                                        entityType: 'Designation',
                                                        functionName: 'entomoSync',
                                                        action: 'update',
                                                        formData: designationData
                                                    }
                                                }
                                                //Trigger Entomo Sync
                                                await commonLib.stepFunctions.triggerStepFunction(process.env.commonStepFunction, 'commonStepFunction', '', inputData);
                                            }
                                        }

                                        //destroy the connection
                                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                                        return { errorCode: "", message: "Designation details updated successfully." };

                                    }).catch((e) => {
                                        console.log('Error while updaing the designation details transaction .catch block', e);
                                        //Destroy DB connection
                                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                                        errResult = commonLib.func.getError(e, 'CHR0017');
                                        throw new ApolloError(errResult.message, errResult.code);
                                    })
                            )


                        } else {
                            //If it is a add action then update the Added_on and Added_By
                            designationData.Added_On = currentDateTime;
                            designationData.Added_By = loginEmployeeId;

                            return (
                                organizationDbConnection
                                    .transaction(function (trx) {
                                        return (
                                            organizationDbConnection(ehrTables.designation)
                                                .insert(designationData)
                                                .transacting(trx)
                                                .then(async (designationInsertData) => {
                                                    let designationInsertId = designationInsertData[0];
                                                    await addDesignationAdditionalDetails(organizationDbConnection, trx, designationInsertId, accreditationId);
                                                    //Update the datasetup_dashboard status
                                                    await updateDataSetupDashboard(organizationDbConnection, trx, formIds.designations, 'Completed')
                                                    // Log message: Add designations Designation_Id - 1
                                                    let systemLogParams = {
                                                        action: systemLogs.roleAdd,
                                                        userIp: context.User_Ip,
                                                        employeeId: loginEmployeeId,
                                                        formName: formName.designationOrPosition,
                                                        trackingColumn: 'Designation_Name',
                                                        organizationDbConnection: organizationDbConnection,
                                                        uniqueId: designationName
                                                    };
                                                    //Call function to add the system log
                                                    await commonLib.func.createSystemLogActivities(systemLogParams);
                                                    return designationInsertId;
                                                })
                                        )
                                    }).then(async (data) => {

                                        if (data && context.partnerid && context.partnerid.toLowerCase() === 'entomo') {
                                            designationData.Code = designationData.Designation_Code
                                            designationData.Name = designationData.Designation_Name

                                            let inputData = {
                                                'orgCode': context.Org_Code, 'partnerId': context.partnerid, 'inputParams': {
                                                    entityId: data,
                                                    entityType: 'Designation',
                                                    functionName: 'entomoSync',
                                                    action: 'add',
                                                    formData: designationData
                                                }
                                            }
                                            //Trigger Entomo Sync
                                            await commonLib.stepFunctions.triggerStepFunction(process.env.commonStepFunction, 'commonStepFunction', '', inputData);
                                        }
                                        //destroy the connection
                                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                                        return { errorCode: "", message: "Designation details added successfully." };

                                    }).catch((e) => {
                                        console.log('Error while adding the designation details transaction .catch block', e);
                                        //Destroy DB connection
                                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                                        errResult = commonLib.func.getError(e, 'CHR0014');
                                        throw new ApolloError(errResult.message, errResult.code);
                                    })
                            )
                        }
                    }

                } else {
                    if(designationCode && designationCode.length){
                        validationError['IVE0490'] = commonLib.func.getError('', 'IVE0490').message2;
                    }else{
                        validationError['IVE0281'] = commonLib.func.getError('', 'IVE0281').message1;
                    }
                    throw ('IVE0000');
                }
            }
            else {
                if (designationId) {
                    throw '_DB0102';
                } else {
                    throw '_DB0101';
                }
            }
        } else {
            throw 'IVE0000';
        }


    }
    catch (e) {
        console.log('Error in addUpdateDesignationDetails function main catch block.', e);
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        //If validation error exist
        if (e === 'IVE0000') {
            console.log('Validation error in the addUpdateDesignationDetails function', validationError);
            errResult = commonLib.func.getError('', 'IVE0000');
            throw new UserInputError(errResult.message, { validationError: validationError });//Return error response
        } 
        else if (e === 'CHR00116') {
            throw new ApolloError(`The designation is associated with job roles ${desAssosiateToJobRole.associatedJobRoles.join(',')}`);//return response   
        }
        
        else if (e == 'CHR0032') {
            throw new ApolloError('Unable to update designation as it is associated with ' + desAssociatedForms + ' forms.', e);//return response
        } else {
            errResult = commonLib.func.getError(e, 'CHR0029');
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
}


/**
 * Function to check if entomo related data is changed
 * @param {knex} organizationDbConnection - DB connection
 * @param {Object} designationData - designation data
 * @returns {Boolean} - true if data is changed else false
 */
async function isEntomoRelatedDataChanged(organizationDbConnection, designationData) {
    try {
        //Get the current designation details
        let currentDesignationData = await organizationDbConnection
            .select('Designation_Code as Old_Code', 'Designation_Name', 'Description')
            .from(ehrTables.designation)
            .where('Designation_Id', designationData.Designation_Id)
            .first();

        //Check if the data is changed
        if (currentDesignationData.Old_Code !== designationData.Designation_Code
            || currentDesignationData.Designation_Name !== designationData.Designation_Name
            || currentDesignationData.Description !== designationData.Description
        ) {
            return currentDesignationData.Old_Code
        } else {
            return false
        }
    }
    catch (err) {
        console.log('Error in isEntomoRelatedDataChanged function', err);
        return false
    }
}


async function addDesignationAdditionalDetails(organizationDbConnection, trx, designationId, accreditationId) {
    try {
        if (accreditationId && accreditationId.length > 0) {
            let designationAccreditationData = [];
            accreditationId.map((accreditation) => {
                designationAccreditationData.push({ "Designation_Id": designationId, "Accreditation_Category_And_Type_Id": accreditation });
            });

            await insertDataInTable(organizationDbConnection, trx, designationAccreditationData, ehrTables.designationAccreditationCategoryTypeMapping);

        }
    } catch (addError) {
        console.log('Error in addDesignationAdditionalDetails main catch block', addError);
        throw ('CHR0030');
    }
}

async function deleteDesignationAdditionalDetails(organizationDbConnection, trx, designationId) {
    try {
        return (
            organizationDbConnection(ehrTables.designationAccreditationCategoryTypeMapping)
                .delete()
                .where("Designation_Id", designationId)
                .transacting(trx)
                .then(() => {
                    return 'success';
                })
        )
    } catch (addError) {
        console.log('Error in deleteDesignationAdditionalDetails main catch block', addError);
        throw ('CHR0031');
    }

}

