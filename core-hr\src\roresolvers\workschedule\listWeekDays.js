//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../../../common/tablealias');

//List the week days
module.exports.listWeekDays = async (parent, args, context, info) => {
    console.log('Inside listWeekDays() function.');
    let organizationDbConnection;
    let errResult;
    try{
        organizationDbConnection = knex(context.connection.OrganizationDb);
        //Retrieve the week days from the table
        return(
            organizationDbConnection(ehrTables.weekdays)
            .select('Day_Id as dayId','Day_Name as dayName')
            .then((weekDayDetails)=>{
                //Destroy DB connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                //Return success response
                return { errorCode:'',message:'Week day details retrieved successfully.',weekDayDetails:(weekDayDetails.length>0)?weekDayDetails:[]};
            })
            .catch(catchError => {
                console.log('Error in listWeekDays() function .catch block',catchError);
                errResult = commonLib.func.getError(catchError, 'CWS0103');
                //Destroy DB connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                //Return error response
                throw new ApolloError(errResult.message,errResult.code);
            })
        );
    }catch(listWeekDaysMainCatchErr) {
        console.log('Error in the listWeekDays() function main catch block. ',listWeekDaysMainCatchErr);
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        errResult = commonLib.func.getError(listWeekDaysMainCatchErr, 'CWS0006');
        //Return error response
        throw new ApolloError(errResult.message,errResult.code);
    }
};