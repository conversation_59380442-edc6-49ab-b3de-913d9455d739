//Require table alias
const { ehrTables } = require('./tablealias');

//Get employee applied/cancel applied/returned leave details for a current leave year
async function getDetailsFromLeaveTable(organizationDbConnection, eligibleEmployeeIds) {
    try {
        return (
        organizationDbConnection(ehrTables.empEligibleLeave)
        .min('Leave_Closure_Start_Date as minStartDate')
        .max('Leave_Closure_End_Date as maxEndDate')
        .whereIn('Employee_Id',eligibleEmployeeIds)
        .then(async(leaveDetailMinMaxResult) => {
            //Get the min and max date from the employee eligible leave table to get the applied leaves from emp_leaves table
            let minStartDate = leaveDetailMinMaxResult[0].minStartDate;
            let maxEndDate = leaveDetailMinMaxResult[0].maxEndDate;
            return (
            organizationDbConnection(ehrTables.empLeaves)
            .select('Employee_Id','LeaveType_Id','Start_Date','End_Date','Total_Days')
            .where('Start_Date','>=',minStartDate)
            .where('End_Date','<=',maxEndDate)
            .whereIn('Employee_Id',eligibleEmployeeIds)
            .whereIn('Approval_Status',['Applied','Returned','Cancel Applied'])
            .then(async(employeeLeaveDetails) => {
                return employeeLeaveDetails;
            }))
        }))
    } catch (err) {
        console.log('Error in getDetailsFromLeaveTable function main catch block.', err)
        throw err;
    }
}

//Function to get employee and leave type details where leave encashment was completed and leave closure was not done
async function checkPartialLeaveClosureExist(organizationDbConnection,employeeIds){
    try {
        return (
        organizationDbConnection(ehrTables.empEligibleLeave+' as EL')
        .select('EL.Employee_Id','EL.LeaveType_Id')
        .whereRaw('EL.CO_Year != EL.LE_Year')
        .whereIn('Employee_Id',employeeIds)
        .then((result) => {
           return result;
        }))
    } catch (err) {
        console.log('Error in getDetailsFromLeaveTable function main catch block.', err)
        throw err;
    }     
}

module.exports={
    getDetailsFromLeaveTable,
    checkPartialLeaveClosureExist
}