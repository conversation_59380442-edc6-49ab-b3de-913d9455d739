//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { UserInputError,ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../../../../common/tablealias');
//Require moment
const moment = require('moment');
//Require constants
const { systemLogs,formName,formIds } = require('../../../../common/appconstants');
//Require common function
const {getDetailsFromLeaveTable} = require('../../../../common/leaveOverrideCommonFunction');
const { validateCommonRuleInput } = require('../../../../common/inputValidations');

//Validate the inputs
async function validateInputs(args){
    try{
        let overallValidationErrors = {};
        if(!args.overrideDetails || args.overrideDetails.length === 0){
            overallValidationErrors['IVE0000'] = commonLib.func.getError('', 'IVE0000').message;
        } else {
            for (let i = 0; i < args.overrideDetails.length; i++) {
                let detail = args.overrideDetails[i];
                let fieldValidations = {};

                if (detail.leaveOverrideReason) {
                    fieldValidations.leaveOverrideReason = {
                        ruleField: 'departmentDescription',
                        errorCode: 'IVE0607'
                    };
                }

                let commonRuleErrors = await validateCommonRuleInput(detail, fieldValidations);
                if (Object.keys(commonRuleErrors).length > 0) {
                    overallValidationErrors[i] = commonRuleErrors; // Index by position
                }
            }
        }
        // If there are any errors, throw an error
        if (Object.keys(overallValidationErrors).length > 0) {
            throw 'IVE0000';
        }
        return overallValidationErrors;
    }catch(error){
        console.log('Error in validateInputs() function main catch block.',error);
        throw error;
    }
}
//Resolver function to list the employee eligible leave - leave override history details
module.exports.overrideEmployeeLeaves = async (parent, args, context, info) => {
    console.log('Inside overrideEmployeeLeaves function'); 
    let organizationDbConnection;
    let validationError;
    let errResult;
    try {
        let loginEmployeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        //Get the leave override access for the login employee id
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, formName.leaveOverride, '', 'UI',false, formIds.leaveOverride);
        //If the login employee have the view access
        if(Object.keys(checkRights).length > 0 && checkRights.Role_Update === 1 && checkRights.Employee_Role.toLowerCase() === 'admin') {
            validationError = await validateInputs(args);
            if(Object.keys(validationError).length === 0){
                let updateDetails = args.overrideDetails;
                let invalidInputs = 0;
                let invalidValidationInputs = 0;
                let invalidEligibleDaysInputs = 0;
                let invalidRecordInputs = 0;
                const employeeIds = [];
                const leaveTypeIds = [];
                let allUniqueIds = [];

                updateDetails.forEach(entry => {
                    employeeIds.push(entry.employeeId);
                    leaveTypeIds.push(entry.leaveTypeId);
                });
                
                console.log("LeaveTypeIds",leaveTypeIds)
                console.log("employeeIds",employeeIds)
                
                return (organizationDbConnection
                .transaction(function (trx) {
                    //Get the leave type details
                    return(organizationDbConnection(ehrTables.leaveType+' as LT')
                    .select('LT.LeaveType_Id as leaveTypeId','LT.Carry_Over as carryOver','LT.Carry_Over_Accumulation_Limit as carryOverAccumulationLimit')
                    .whereIn('LeaveType_Id',leaveTypeIds)
                    .transacting(trx)
                    .then(async(leaveTypeDetailsResult) => {
                        let leaveTypeDetails = commonLib.func.organizeData(leaveTypeDetailsResult,'leaveTypeId');
                    
                        //Get the leave eligible details
                        return( organizationDbConnection(ehrTables.empEligibleLeave+' as EEL')
                        .select('EEL.*','EL.Encashed_Days')
                        .leftJoin(ehrTables.encashedLeaves+' as EL',function(){
                            this.on('EEL.Employee_Id','=','EL.Employee_Id')
                            this.on('EEL.LeaveType_Id','=','EL.LeaveType_Id')
                            this.on('EEL.CO_Year','=','EL.EN_Year')
                        })
                        .whereIn('EEL.Employee_Id',employeeIds)
                        .whereIn('EEL.LeaveType_Id',leaveTypeIds)
                        .transacting(trx)
                        .then(async(employeeEligibleLeaveResult) => {
                            let employeeEligibleLeaveDetails = commonLib.func.organizeData(employeeEligibleLeaveResult,'Employee_Id','LeaveType_Id');
                            // console.log("employeeEligibleLeaveDetails",employeeEligibleLeaveDetails)
                            if(employeeEligibleLeaveDetails && Object.keys(employeeEligibleLeaveDetails).length){
                                let employeeLeaveDetails = await getDetailsFromLeaveTable(organizationDbConnection,employeeIds);
                                
                                for(let i=0;i<updateDetails.length;i++){
                                    let employeeId = updateDetails[i].employeeId;
                                    let leaveTypeId = updateDetails[i].leaveTypeId;
                                    let newEligibleDays = updateDetails[i].currentYearEligibleDays;
                                    let newCOBalance = updateDetails[i].coBalance;
                                    let leaveOverrideReason = updateDetails[i]?.leaveOverrideReason || null;

                                    let updateJson = {};
                                    let totalDaysAppliedInLeaves = 0;
                                    let auditEligibleLeaveJson = [];

                                    let currentLeaveTypeJson = leaveTypeDetails[leaveTypeId] ? leaveTypeDetails[leaveTypeId][0] : '';

                                    if(currentLeaveTypeJson){
                                        let currentYearMaxLeaveDays = 365;
                                        let carryOverBalanceMaxDays = 1500;
                                      
                                        if(newEligibleDays <= currentYearMaxLeaveDays && newCOBalance <= carryOverBalanceMaxDays){
                                            let currentEligibleLeaveKey = `${employeeId}|${leaveTypeId}`;
                                            let employeeCurrentEligibleLeaves = employeeEligibleLeaveDetails[currentEligibleLeaveKey][0];

                                            if(employeeCurrentEligibleLeaves){
                                                //Eligible Days
                                                if(newEligibleDays !== employeeCurrentEligibleLeaves['Eligible_Days']){
                                                    updateJson.Eligible_Days = newEligibleDays;
                                                }

                                                //Last CO Balance
                                                if(newCOBalance !== employeeCurrentEligibleLeaves['Last_CO_Balance']){
                                                    updateJson.Last_CO_Balance = newCOBalance;
                                                    updateJson.No_Of_Days = newCOBalance;
                                                }

                                                if(updateJson && Object.keys(updateJson).length){
                                                    updateJson.Leave_Override_Reason = leaveOverrideReason;

                                                    let actualEligibleDays = updateJson.Eligible_Days ? updateJson.Eligible_Days : employeeCurrentEligibleLeaves['Eligible_Days'];
                                                    let actualCoBalance = updateJson.Last_CO_Balance ? updateJson.Last_CO_Balance : employeeCurrentEligibleLeaves['Last_CO_Balance'];
                                                    let encashedDays = employeeCurrentEligibleLeaves['Encashed_Days']>0 ? employeeCurrentEligibleLeaves['Encashed_Days'] : 0;
                                                    let totalLeavesTakenEncashedDays = employeeCurrentEligibleLeaves['Leaves_Taken'] + encashedDays;
                                                    let totalEligibleDays = parseFloat(actualEligibleDays) + parseFloat(actualCoBalance);

                                                    //Filter the leave detail based on the employee id, leave type id and leave closure date range
                                                    const filteredLeaveData = employeeLeaveDetails.filter(leave => {
                                                        return leave.Employee_Id === employeeId &&
                                                            leave.LeaveType_Id === leaveTypeId &&
                                                            leave.Start_Date >= employeeCurrentEligibleLeaves['Leave_Closure_Start_Date'] &&
                                                            leave.End_Date <= employeeCurrentEligibleLeaves['Leave_Closure_End_Date'];
                                                    });
                                                    if(filteredLeaveData && filteredLeaveData.length){
                                                        //Sum the total days
                                                        totalDaysAppliedInLeaves = filteredLeaveData.reduce((sum, leave) => sum + leave.Total_Days, 0);
                                                    }else{
                                                        totalDaysAppliedInLeaves = 0;
                                                    }
                                                    let totalLeavesTaken =  (totalDaysAppliedInLeaves + totalLeavesTakenEncashedDays);
                                                    //Validate the sum of new eligible days and new co balance is greater than or equal all the leaves(including applied,cancel applied, returned and approved) and encashed days
                                                    if(totalEligibleDays >= totalLeavesTaken){
                                                        //Leave Balance
                                                        let leaveBalance = totalEligibleDays - totalLeavesTakenEncashedDays;
                                                        updateJson.Leave_Balance=leaveBalance;
                                                    
                                                        auditEligibleLeaveJson = {
                                                            "LeaveType_Id":employeeCurrentEligibleLeaves['LeaveType_Id'],
                                                            "Employee_Id":employeeCurrentEligibleLeaves['Employee_Id'],
                                                            "Eligible_Days":employeeCurrentEligibleLeaves['Eligible_Days'],
                                                            "Leaves_Taken":employeeCurrentEligibleLeaves['Leaves_Taken'],
                                                            "Leave_Balance":employeeCurrentEligibleLeaves['Leave_Balance'],
                                                            "No_Of_Days":employeeCurrentEligibleLeaves['No_Of_Days'],
                                                            "Last_CO_Balance":employeeCurrentEligibleLeaves['Last_CO_Balance'],
                                                            "CO_Year":employeeCurrentEligibleLeaves['CO_Year'],
                                                            "LE_Year":employeeCurrentEligibleLeaves['LE_Year'],
                                                            "Leave_Closure_Start_Date":employeeCurrentEligibleLeaves['Leave_Closure_Start_Date'],
                                                            "Leave_Closure_End_Date":employeeCurrentEligibleLeaves['Leave_Closure_Start_Date'],
                                                            "Added_On":moment.utc().format('YYYY-MM-DD HH:mm:ss'),
                                                            "Added_By":loginEmployeeId,
                                                            "Leave_Override_Reason": employeeCurrentEligibleLeaves['Leave_Override_Reason'],
                                                            "Audit_Reason": 'leave-override' 
                                                        }
                                                        console.log("updateJson...",updateJson)
                                                        await organizationDbConnection(ehrTables.empEligibleLeave)
                                                        .update(updateJson)
                                                        .where("Employee_Id",employeeId)
                                                        .where("LeaveType_Id",leaveTypeId)
                                                        .transacting(trx)
                                                        .then(async() => {
                                                            await organizationDbConnection(ehrTables.auditEmpEligibleLeave)
                                                            .insert(auditEligibleLeaveJson)
                                                            .transacting(trx)
                                                            .transacting(trx)
                                                            .then(() => {
                                                                allUniqueIds.push(employeeCurrentEligibleLeaves['Eligible_Leave_Id'])
                                                            })
                                                        })
                                                    }else{
                                                        console.log("Total Eligible days is lesser than total leaves taken, totalEligibleDays:",totalEligibleDays,"totalLeavesTaken: ",totalLeavesTaken,"filteredLeaveData:",filteredLeaveData,updateDetails[i],updateJson);
                                                        invalidInputs+=1;    
                                                        invalidEligibleDaysInputs+=1;
                                                    }
                                                }else{
                                                    console.log("No details are changed.",updateDetails);
                                                }
                                            }else{
                                                invalidInputs+=1;
                                                invalidRecordInputs+=1;
                                            }
                                        }else{
                                            console.log("Invalid inputs.",newEligibleDays,newCOBalance,updateDetails[i],currentLeaveTypeJson);
                                            invalidInputs+=1;
                                            invalidValidationInputs+=1;
                                        }
                                    }
                                }
                            }else{
                                console.log('There is no employee eligible leave records for the inputs',args);
                                throw 'CTL0106';
                            }
                        }))
                    }))
                })
                .then(async function() {
                    if(invalidInputs === 0){
                        if(allUniqueIds){
                            console.log("allUniqueIds",allUniqueIds)
                            // Log message: Override Leave Eligibility
                            let systemLogParams = {
                                action: systemLogs.roleUpdate,
                                userIp: context.User_Ip,
                                employeeId: loginEmployeeId,
                                formName: formName.leaveOverride,
                                trackingColumn: 'Eligible Leave Id - '+allUniqueIds,
                                organizationDbConnection: organizationDbConnection
                            };
                            //Call function to add the system log
                            await commonLib.func.createSystemLogActivities(systemLogParams);
                            
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            return { errorCode: '', message: 'Leave days are overridden successfully.'};//Return the response
                        }else{
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            return { errorCode: '', message: 'Leave days are already overrided.'};//Return the response
                        }
                    }else{
                        //In future if multiple leave records are overridden at the same time then messages need to be grouped and returned.
                        if(invalidValidationInputs > 0 ){
                            throw 'CTL0104';
                        }else if (invalidEligibleDaysInputs > 0 ){
                            throw 'CTL0105';
                        }else{
                            throw 'CTL0106';
                        }
                    }
                })
                .catch(function (catchError) {
                    console.log('Error in overrideEmployeeLeaves .catch() block', catchError);
                    errResult = commonLib.func.getError(catchError, 'CTL0103');
                    //Destroy DB connection
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    throw new ApolloError(errResult.message, errResult.code);
                })
                )
            }else{
                console.log("Invalid input",args);
                throw 'IVE0000';
            }
        }else{
            console.log('Login employee does not have update access rights',checkRights);
            throw '_DB0102';
        }
    }catch(error){
        console.log('Error in the overrideEmployeeLeaves() function in the main catch block.',error);
        let errResult;
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        if(error==='IVE0000'){
            console.log('Validation error in the overrideEmployeeLeaves() function.',validationError);
            errResult = commonLib.func.getError('',error);
            throw new UserInputError(errResult.message,{validationError: validationError});
        }else{
            errResult = commonLib.func.getError(error, 'CTL0003');
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
}