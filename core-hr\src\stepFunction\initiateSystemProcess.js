'use strict';
//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;


// Function to initiate initiateSystemProcess step function
module.exports.initiateSystemProcess  = async(event, context) =>{
    try{
        console.log('Inside initiateSystemProcess function',event);
        // based on event define the status
        let status='';
        if(event.status==='Open')
        {
            status='Open';
        }
        else{
            status=event.status;
        }
        let inputParams={'status':status}
        let triggerSystemProcess= await commonLib.stepFunctions.triggerStepFunction(process.env.stateMachineArn,'systemProcess',status,inputParams);
        console.log('Response after triggering system process step function',triggerSystemProcess);


        return {errorCode:'',message: 'System Process initiated successfully.'};
    }
    catch(mainCatchError){
        console.log('Error in initiateSytemProcess function main catch block.', mainCatchError);
        let errResult = commonLib.func.getError(mainCatchError, 'CDG0167');
        return {errorCode:errResult.code,message: errResult.message};
    }
};