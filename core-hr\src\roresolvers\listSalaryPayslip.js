const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const knex = require('knex');
const { ehrTables } = require('../../common/tablealias');
const { ApolloError } = require('apollo-server-lambda');
const { formIds } = require('../../common/appconstants');
const moment = require('moment');
let organizationDbConnection;

module.exports.listSalaryPayslip = async (parent, args, context, info) => {
    console.log('Inside listSalaryPayslip function');
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);
        const { employeeId, year, formId, isMonthly } = args;
        const loginEmployeeId = context.Employee_Id;
        const orgCode = context.Org_Code;

        // Get organization details
        const orgDetails = await organizationDbConnection(ehrTables.orgDetails)
            .where('Org_Code', orgCode)
            .first();
        if (!orgDetails) throw new Error('Organization details not found');

        // Check access rights
        const checkRights = await commonLib.func.checkEmployeeAccessRights(
            organizationDbConnection,
            loginEmployeeId,
            null,
            '',
            'UI',
            false,
            formId
        );

        let isAdmin = 0;
        let isManager = 0;
        let employeeIdsArray = [];

        if (Object.keys(checkRights).length > 0 && checkRights.Role_View === 1) {
            if (employeeId) {
                employeeIdsArray.push(employeeId);
            } else {
                if (checkRights.Employee_Role.toLowerCase() === 'admin') {
                    isAdmin = 1;
                } else {
                    // Check service provider admin
                    const serviceProviderAdmin = await commonLib.func.checkEmployeeAccessRights(
                        organizationDbConnection,
                        loginEmployeeId,
                        '',
                        'Role_Update',
                        '',
                        false,
                        formIds.serviceProviderAdmin
                    );

                    if (serviceProviderAdmin) {
                        const serviceProviderEmployeeIds = await commonLib.func.getServiceProviderEmpIdsForFieldForce(
                            organizationDbConnection,
                            loginEmployeeId,
                            orgCode
                        );
                        employeeIdsArray = employeeIdsArray.concat(serviceProviderEmployeeIds);
                    } else if (checkRights.Is_Manager === 1) {
                        isManager = 1;
                        const managerAccessDetails = await commonLib.func.getManagerHierarchy(
                            organizationDbConnection,
                            loginEmployeeId,
                            0,
                            orgCode
                        );
                        employeeIdsArray = employeeIdsArray.concat(managerAccessDetails);
                    } else {
                        employeeIdsArray.push(loginEmployeeId);
                    }
                }
            }
        } else {
            throw '_DB0100';
        }

        // Check for Syntrum integration - just check if any active record exists
        const externalApiSyncDetail = await organizationDbConnection(ehrTables.externalApiSyncDetails)
            .where('Status', 'Active')
            .first();

        const isSyntrumEnabled = !!externalApiSyncDetail;
        let monthList = [];
        if (isSyntrumEnabled && employeeId) {
            const monthListData = await getMonthList(organizationDbConnection, employeeId, args, context);
                monthList = monthListData.monthList;
        }

        // Determine which table to use - default is bimonthly (bwdSalaryPayslip)
        const tableName = isMonthly ? ehrTables.salaryPayslip : ehrTables.bwdSalaryPayslip;
        const tableAlias = isMonthly ? 'SPS' : 'BMS';

        // Main query for salary payslip
        const payslipQuery = organizationDbConnection(`${tableName} as ${tableAlias}`)
            .select(
                `${tableAlias}.Payslip_Id`,
                `${tableAlias}.Salary_Month`,
                `${tableAlias}.Generated_On`,
                `${tableAlias}.Basic_Salary`,
                `${tableAlias}.Total_Earning`,
                `${tableAlias}.Total_Deduction`,
                `${tableAlias}.Total_Salary`,
                `${tableAlias}.Payment_Status`,
                `${tableAlias}.S3_FileName`,
                'ESC.Eligible_For_Contractor_Tds',
                organizationDbConnection.raw(`
                    CONCAT(
                        LEFT(MONTHNAME(STR_TO_DATE(SUBSTRING_INDEX(${tableAlias}.Salary_Month, ',', 1), '%m')), 3
                    ), ',', SUBSTRING_INDEX(${tableAlias}.Salary_Month, ',', -1)
                ) AS Formatted_Salary_Month`),
                organizationDbConnection.raw(`
                    CASE
                        WHEN EJ.User_Defined_EmpId IS NULL THEN EP.Employee_Id
                        ELSE EJ.User_Defined_EmpId
                    END as User_Defined_EmpId
                `),
                organizationDbConnection.raw(`
                    CONCAT_WS(' ', EP.Emp_First_Name, EP.Emp_Middle_Name, EP.Emp_Last_Name) as Employee_Name
                `),
                organizationDbConnection.raw(`
                    CONCAT_WS(' ', EPI.Emp_First_Name, EPI.Emp_Middle_Name, EPI.Emp_Last_Name) as Generated_By
                `),
                'EJ.Service_Provider_Id',
                'EJ.Location_Id',
                'EJ.Department_Id',
                'EJ.Manager_Id',
                'EJ.Designation_Id',
                'DES.Designation_Name',
                'PT.Template_Id',
                organizationDbConnection.raw(`
                    CASE
                        WHEN D.Department_Code IS NOT NULL
                        THEN CONCAT(D.Department_Code, ' - ', D.Department_Name)
                        ELSE D.Department_Name
                    END AS Department_Name
                `),
                organizationDbConnection.raw(`
                    CASE
                        WHEN L.Location_Code IS NOT NULL
                        THEN CONCAT(L.Location_Code, ' - ', L.Location_Name)
                        ELSE L.Location_Name
                    END AS Location_Name
                `),
                organizationDbConnection.raw(`
                    CASE
                        WHEN ET.Employee_Type_Code IS NOT NULL
                        THEN CONCAT(ET.Employee_Type_Code, ' - ', ET.Employee_Type)
                        ELSE ET.Employee_Type
                    END AS Employee_Type
                `),
                'ET.EmpType_Id',
                organizationDbConnection.raw(`
                    CASE
                        WHEN SP.Service_Provider_Code IS NOT NULL
                        THEN CONCAT(SP.Service_Provider_Code, ' - ', SP.Service_Provider_Name)
                        ELSE SP.Service_Provider_Name
                    END AS Organization_Unit_Name
                `)
            )
            .modify((queryBuilder) => {
                if (!isMonthly) {
                    queryBuilder.select(`${tableAlias}.Pay_Period`);
                }
            })
            .join(`${ehrTables.empPersonalInfo} as EP`, 'EP.Employee_Id', `${tableAlias}.Employee_Id`)
            .leftJoin(`${ehrTables.empPersonalInfo} as EPI`, 'EPI.Employee_Id', `${tableAlias}.GeneratedBy_EmployeeId`)
            .join(`${ehrTables.empJob} as EJ`, 'EP.Employee_Id', 'EJ.Employee_Id')
            .leftJoin(`${ehrTables.employeeType} as ET`, 'EJ.EmpType_Id', 'ET.EmpType_Id')
            .leftJoin(`${ehrTables.location} as L`, 'EJ.Location_Id', 'L.Location_Id')
            .leftJoin(`${ehrTables.department} as D`, 'EJ.Department_Id', 'D.Department_Id')
            .leftJoin(`${ehrTables.designation} as DES`, 'EJ.Designation_Id', 'DES.Designation_Id')
            .leftJoin(`${ehrTables.employeeSalaryConfiguration} as ESC`, 'ESC.Employee_Id', 'EP.Employee_Id')
            .leftJoin(`${ehrTables.serviceProvider} as SP`, 'EJ.Service_Provider_Id', 'SP.Service_Provider_Id')
            .modify((queryBuilder) => {
                // Join with payslip template based on field force setting
                if (orgDetails.Field_Force == 1) {
                    queryBuilder.join(
                        `${ehrTables.payslipTemplate} as PT`,
                        'PT.Service_Provider_Id',
                        'EJ.Service_Provider_Id'
                    )
                    .where('PT.Set_As_Default', 1)
                    .where('PT.Payslip_Type', 'Monthly');
                } else {
                    queryBuilder.join(
                        `${ehrTables.payslipTemplate} as PT`,
                        function() {
                            this.on('PT.Payslip_Type', '=', organizationDbConnection.raw("'Monthly'"));
                        }
                    )
                    .where('PT.Set_As_Default', 1);
                }

                // Apply employee access filters
                if (employeeId) {
                    queryBuilder.where(`${tableAlias}.Employee_Id`, employeeId);
                } else if (!isAdmin) {
                    if (isManager) {
                        if (orgDetails.Restrict_Financial_Access_For_Manager) {
                            queryBuilder.where(`${tableAlias}.Employee_Id`, loginEmployeeId);
                        } else {
                            queryBuilder.whereNotIn(`${tableAlias}.Employee_Id`, loginEmployeeId);
                            queryBuilder.whereIn(`${tableAlias}.Employee_Id`, employeeIdsArray);
                        }
                    } else {
                        queryBuilder.where(`${tableAlias}.Employee_Id`, loginEmployeeId);
                    }
                }

              //  Apply payment status filter for non-admins
                if (!isAdmin) {
                    queryBuilder.whereIn(`${tableAlias}.Payment_Status`, [
                        'Employee Owes',
                        'Nil Payment',
                        'Unpaid',
                        'Paid'
                    ]);
                }

                // Apply year filter
                if (year) {
                    queryBuilder.whereRaw(
                        `SUBSTRING_INDEX(${tableAlias}.Salary_Month, ',', -1) = ?`,
                        [year]
                    );
                }
            }).orderBy(`${tableAlias}.Salary_Month`, 'desc');

        let salaryPayslips = await payslipQuery;
        // Handle Syntrum integration
        if (isSyntrumEnabled && employeeId && monthList.length > 0) {
            const existingPayslips = salaryPayslips.map(payslip => {
                const month = parseInt(payslip.Salary_Month.split(',')[0]);
                return { ...payslip, month };
            });

            const allPayslips = [];

            monthList.forEach(month => {
                const existingPayslip = existingPayslips.find(p => p.month == month);
                if (existingPayslip) {
                    allPayslips.push(existingPayslip);
                } else {
                    allPayslips.push({
                        Payslip_Id: null,
                        Employee_Id: employeeId,
                        Salary_Month: `${month},${args.year}`,
                        Total_Earning: 0,
                        Total_Deduction: 0,
                        Total_Salary: 0,
                        Basic_Salary: 0,
                        Generated_On: null,
                        Payment_Status: null,
                        S3_FileName: null,
                        Eligible_For_Contractor_Tds: null,
                        Formatted_Salary_Month: moment().month(month - 1).format('MMM') + `,${args.year}`,
                        User_Defined_EmpId: null,
                        Employee_Name: null,
                        Generated_By: null,
                        Service_Provider_Id: null,
                        Location_Id: null,
                        Department_Id: null,
                        Manager_Id: null,
                        Designation_Id: null,
                        Designation_Name: null,
                        Template_Id: null,
                        Department_Name: null,
                        Location_Name: null,
                        Employee_Type: null,
                        EmpType_Id: null,
                        Organization_Unit_Name: null,
                        ...(isMonthly ? {} : { Pay_Period: null }),
                        month
                    });
                }
            });

            salaryPayslips = allPayslips;
        }
        salaryPayslips = salaryPayslips.map(payslip => ({
            ...payslip,
            Incentive_Amount: payslip.Total_Salary
        }));

        const response = {
            errorCode: "",
            message: "Salary payslip details retrieved successfully",
            salaryPayslips: salaryPayslips,
            isSyntrumEnabled,
            ...(isSyntrumEnabled && externalApiSyncDetail ? { externalApiSyncDetail } : {}),
            ...(isSyntrumEnabled && monthList.length > 0 ? { monthList } : {})
        };

        return response;

    } catch (error) {
        organizationDbConnection?.destroy();
        console.error('Error in listSalaryPayslip:', error);
        const errResult = commonLib.func.getError(error, 'SPS0001');
        throw new ApolloError(errResult.message, errResult.code);
    } finally {
        organizationDbConnection?.destroy();
    }
};

async function getMonthList(organizationDbConnection, employeeId, args, context) {
    try {
        const [employeeJob, orgDetails] = await Promise.all([
            organizationDbConnection(ehrTables.empJob).select('Date_Of_Join').where('Employee_Id', employeeId).first(),
            organizationDbConnection(ehrTables.orgDetails).select('Payroll_Start_Date').first()
        ]);

        if (!employeeJob?.Date_Of_Join || !orgDetails?.Payroll_Start_Date) {
            throw !employeeJob?.Date_Of_Join ? 'SYN0026' : 'SYN0027';
        }

        const yearStart = moment(`${args.year}-01-01`, 'YYYY-MM-DD');
        const minMonth = moment.max([moment(employeeJob.Date_Of_Join), yearStart, moment(orgDetails.Payroll_Start_Date)]).month() + 1;
        const maxMonth = moment().month() + 1;

        return {
            monthList: Array.from({ length: maxMonth - minMonth + 1 }, (_, i) => minMonth + i),
            employeeDateOfJoin: employeeJob.Date_Of_Join,
            payrollStartDate: orgDetails.Payroll_Start_Date
        };
    } catch (error) {
        console.error("Error while getting month list:", error);
        throw error;
    }
}