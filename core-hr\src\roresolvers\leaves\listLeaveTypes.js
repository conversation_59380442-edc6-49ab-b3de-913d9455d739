//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../../../common/tablealias');

//Resolver function to list the leave types
module.exports.listLeaveTypes = async (parent, args, context, info) => {
    console.log('Inside listLeaveTypes function');
    let organizationDbConnection;
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);
        return (
        organizationDbConnection(ehrTables.leaveType)
        .select('LeaveType_Id as leaveTypeId', 'Leave_Name as leaveName', 'Leave_Status as status')
        .then((result) => {
            organizationDbConnection ? organizationDbConnection.destroy() : null;
            //Return the response
            return { errorCode: '', message: 'Leave types has been retrieved successfully.', leaveTypeDetails: (result && result.length>0) ? result : [] };
        })
        .catch((catchError) => {
            console.log('Error in listLeaveTypes .catch() block', catchError);
            let errResult = commonLib.func.getError(catchError, 'CEL0101');
            //Destroy DB connection
            organizationDbConnection ? organizationDbConnection.destroy() : null;
            throw new ApolloError(errResult.message, errResult.code);
        })
        )
    }catch(error){
        console.log('Error in the listLeaveTypes() function in the main catch block.',error);
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        let errResult = commonLib.func.getError(error, 'CEL0001');
        throw new ApolloError(errResult.message, errResult.code);
    }
}