//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { UserInputError, ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../../../common/tablealias');
//Require constants
const { formName, systemLogs, formIds } = require('../../../common/appconstants');
//Require validation function
const { numberValidation } = require('../../../common/commonvalidation');
const { validateWSAssociated } = require('../../../common/workScheduleValidation');

//Delete the work schedule and week off
module.exports.deleteWorkScheduleAndWeekOff = async (parent, args, context, info) => {
    console.log('Inside deleteWorkScheduleAndWeekOff() function.');
    let organizationDbConnection;
    let errResult;
    let validationError = {};
    let wsAssociatedForms = '';
    try {
        let workScheduleTitle = '', workScheduleCode='';
        let loginEmployeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        //Get the login employee - work schedule form access
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, formName.workSchedule, '', 'UI');
        //Check delete rights exist or not                 
        if (Object.keys(checkRights).length > 0 && checkRights.Role_Delete === 1) {
            if (!(args.workScheduleId || numberValidation(args.workScheduleId)) || args.workScheduleId < 1) {
                validationError['IVE0150'] = commonLib.func.getError('', 'IVE0150').message;
            }
            //Check validation error exist or not
            if (Object.keys(validationError).length === 0) {
                let workScheduleId = args.workScheduleId;
                //Initiate a transaction
                return (
                organizationDbConnection
                .transaction(function (trx) {
                    //Retrieve the work schedule details based on the work schedule id
                    return (
                    organizationDbConnection(ehrTables.workSchedule)
                    .select('Lock_Flag as lockFlag', 'Title as workScheduleTitle', 'WorkSchedule_Code as workScheduleCode')
                    .where('WorkSchedule_Id', workScheduleId)
                    .transacting(trx)
                    .then(async(workScheduleDetails) => {
                        if (workScheduleDetails.length > 0) {
                            workScheduleDetails = workScheduleDetails[0];
                            workScheduleTitle = workScheduleDetails.workScheduleTitle;
                            workScheduleCode = workScheduleDetails.workScheduleCode;
                            wsAssociatedForms = await validateWSAssociated(organizationDbConnection,workScheduleId,workScheduleTitle,args.dashboardType,'delete');
                            if(wsAssociatedForms){
                                throw 'CWS0011';
                            }else{
                                if (workScheduleDetails.lockFlag === 0) {
                                    //Delete the input work schedule
                                    return (
                                        organizationDbConnection(ehrTables.workSchedule)
                                            .delete()
                                            .where('WorkSchedule_Id', workScheduleId)
                                            .transacting(trx)
                                            .then(() => {
                                                //Delete the week off details for the work schedule id
                                                return (
                                                    organizationDbConnection(ehrTables.workscheduleWeekoff)
                                                        .delete()
                                                        .where('WorkSchedule_Id', workScheduleId)
                                                        .transacting(trx)
                                                        .then(() => {
                                                            return 'success';
                                                        })
                                                )
                                            })
                                    )
                                } else {
                                    console.log('Work schedule is edited by the same or some other user.', workScheduleDetails);
                                    throw '_EC0005';
                                }
                            }
                        } else {
                            console.log('Work schedule details do not exist for the work schedule id.');
                            throw 'CWS0008';
                        }
                    })
                    )
                })
                .then(async () => {
                    //Log message: Delete Work Schedule and week off - Regular shift - 1
                    let systemLogParams = {
                        action: systemLogs.roleDelete,
                        userIp: context.User_Ip,
                        employeeId: loginEmployeeId,
                        formId: formIds.workSchedule,
                        organizationDbConnection: organizationDbConnection,
                        uniqueId: workScheduleId,
                        message: `The work schedule ${workScheduleCode||''} ${workScheduleTitle} and week off has been deleted successfully.`,
                        isEmployeeTimeZone: 0,
                        changedData: args
                    };
                    //Call function to add the system log
                    await commonLib.func.createSystemLogActivities(systemLogParams);
                    //Destroy DB connection
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    //function to delete from weekof dates according to workschedule id
                    deleteWeekOffDate(args.workScheduleId, context.connection.OrganizationDb);
                    //Return the response
                    return { errorCode: '', message: 'Work schedule and week off deleted successfully.' };
                })
                .catch(catchError => {
                    console.log('Error in deleteWorkScheduleAndWeekOff() function .catch block', catchError , ' and wsAssociatedForms: ', wsAssociatedForms);
                    if (catchError == 'CWS0011'){
                        throw new ApolloError('Unable to delete work schedule and week off as it is associated in '+wsAssociatedForms+' forms.',catchError);
                    }else{ 
                        errResult = commonLib.func.getError(catchError, 'CWS0107');
                    }
                    //Destroy DB connection
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    //Return error response
                    throw new ApolloError(errResult.message, errResult.code);
                })
                );
            } else {
                throw 'IVE0000';
            }
        }
        else {
            console.log('Login employee id does not have delete access to work schedule form.');
            throw ('_DB0103');
        }
    } catch (deleteWSWeekOffMainCatchErr) {
        console.log('Error in the deleteWorkScheduleAndWeekOff() function main catch block. ', deleteWSWeekOffMainCatchErr);
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        //If validation error exist
        if (deleteWSWeekOffMainCatchErr === 'IVE0000') {
            console.log('Validation error in the deleteWorkScheduleAndWeekOff() function', validationError);
            errResult = commonLib.func.getError('', 'IVE0000');
            throw new UserInputError(errResult.message, { validationError: validationError });//Return error response
        } else {
            errResult = commonLib.func.getError(deleteWSWeekOffMainCatchErr, 'CWS0014');
            throw new ApolloError(errResult.message, errResult.code);//Return error response
        }
    }
};

async function deleteWeekOffDate(workScheduleId, connection) {
    try {
        let organizationDbConnection = knex(connection);
        let delWorkScheduleFromWeekOffDates = await commonLib.func.delWorkScheduleFromWeekOffDates(organizationDbConnection, workScheduleId);
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
    }
    catch (e) {
        console.log("Error Occure while deleting workschedule from week off date", e);
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
    }
}