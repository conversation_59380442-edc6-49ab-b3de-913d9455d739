
// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../common/tablealias');
// Require Apollo Server to return error message
const { ApolloError, UserInputError } = require('apollo-server-lambda');
// require input validator
const { validateCommonRuleInput } = require('../../../common/inputValidations');
// require moment
const moment = require('moment');

module.exports.addUpdateAirTicketSetting = async (parent, args, context, info) => {
    let validationError = {};
    let organizationDbConnection;
    try {
        const { Employee_Id: loginEmployeeId, Org_Code: orgCode, User_Ip: userIp } = context;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let accessFormId = args.formId
        // Check access rights
        const checkRights = await commonLib.func.checkEmployeeAccessRights(
            organizationDbConnection,
            loginEmployeeId,
            '',
            '',
            'UI',
            false,
            accessFormId
        );

        if (!checkRights || Object.keys(checkRights).length <0 || (args.airTicketSettingId && checkRights.Role_Update != 1) ||(!args.airTicketSettingId && checkRights.Role_Add != 1) || checkRights.Employee_Role.toLowerCase() !== 'admin') {
            if(checkRights && checkRights.Role_Update === 0){
                throw '_DB0102';
            }
            else if(checkRights && checkRights.Role_Add === 0){
                throw '_DB0101';
            }
            else if(checkRights && checkRights.Employee_Role.toLowerCase() !== 'admin'){
                throw '_DB0109';
            }
            throw '_DB0104';
        }

        if (args.status.toLowerCase() === 'inactive' && args.airTicketSettingId) {
            const associatedRecord = await organizationDbConnection(ehrTables.empAirTicketPolicy + " as EATP")
              .innerJoin(ehrTables.airTicketSettings + " as ATS", "ATS.Air_Ticket_Setting_Id", "EATP.Air_Ticket_Setting_Id")
              .where('EATP.Air_Ticket_Setting_Id', args.airTicketSettingId)
              .where('EATP.Status','Active')
              .first();
            if (associatedRecord) {
              throw 'ATT0008';
            }
          }

        const fieldValidations = {
            city: 'IVE0308',
            country: 'IVE0129',
            airTicketingCategory: 'IVE0532',
            infantAmount: 'IVE0533',
            childAmount: 'IVE0533',
            adultAmount: 'IVE0533'
        };

        validationError = await validateCommonRuleInput(args, fieldValidations);
        if (Object.keys(validationError).length > 0) {
            throw 'IVE0000';
        }

        // Prepare data object
        const airTicketData = {
            Destination_City: args.city,
            Destination_Country: args.country,
            Air_Ticketing_Category: args.airTicketingCategory,
            Status: args.status,
            Infant_Amount: parseFloat(args.infantAmount || 0).toFixed(2),
            Child_Amount: parseFloat(args.childAmount || 0).toFixed(2),
            Adult_Amount: parseFloat(args.adultAmount || 0).toFixed(2),
        };

        // Handle timestamps
        if (args.airTicketSettingId) {
            airTicketData.Updated_On = moment().utc().format('YYYY-MM-DD HH:mm:ss');
            airTicketData.Updated_By=loginEmployeeId
        } else {
            airTicketData.Added_On = moment().utc().format('YYYY-MM-DD HH:mm:ss');
            airTicketData.Added_By = loginEmployeeId;
        }

        if (args.status.toLowerCase() === 'active') {
            const duplicateQuery = organizationDbConnection(ehrTables.airTicketSettings)
                .whereRaw('LOWER(Destination_City) = ?', [args.city.toLowerCase()])
                .whereRaw('LOWER(Air_Ticketing_Category) = ?', [args.airTicketingCategory.toLowerCase()])
                .whereRaw('LOWER(Destination_Country) = ?', [args.country.toLowerCase()])
                .where('Status', 'Active');

            if (args.airTicketSettingId) {
                duplicateQuery.whereNot('Air_Ticket_Setting_Id', args.airTicketSettingId);
            }

            const duplicateRecords = await duplicateQuery;

            if (duplicateRecords && duplicateRecords.length > 0) {
                throw 'ATT0006';
            }
        }

        // Perform insert/update operation
        const result = args.airTicketSettingId
            ? await organizationDbConnection(ehrTables.airTicketSettings)
                .where('Air_Ticket_Setting_Id', args.airTicketSettingId)
                .update(airTicketData)
            : await organizationDbConnection(ehrTables.airTicketSettings)
                .insert(airTicketData);

        if (!result) throw 'ATT0003';

        // Log system activity
        await commonLib.func.createSystemLogActivities({
            userIp,
            employeeId: loginEmployeeId,
            organizationDbConnection,
            message: `Air ticket setting for ${args.city} ${args.airTicketSettingId ? 'updated' : 'added'} successfully`,
        });

        if (organizationDbConnection) organizationDbConnection.destroy();

        return {
            errorCode: '',
            message: `Air ticket setting ${args.airTicketSettingId ? 'updated' : 'added'} successfully`,
            airTicketSettingData: {
                ...airTicketData,
                Air_Ticket_Setting_Id: args.airTicketSettingId || result[0]
            }
        };
    } catch (error) {
        if (organizationDbConnection) organizationDbConnection.destroy();
        if (error === 'IVE0000') {
            console.log('Validation error in addUpdateAirTicketSetting', validationError);
            throw new UserInputError('Validation failed', { validationError });
        } else {
            const errResult = commonLib.func.getError(error, 'ATT0005');
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
};

