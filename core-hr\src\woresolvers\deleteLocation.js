const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../common/tablealias');
const { ApolloError } = require('apollo-server-lambda');
const { isLocationUsed, getCustomForms, updateDataSetupDashboard } = require('../../common/commonfunctions');
const {formIds} = require('../../common/appconstants');

module.exports.deleteLocation = async (parent, args, context, info) => {

    console.log("Inside deleteLocation() function ");
    let organizationDbConnection, appManagerDbConnection;

    try{

        organizationDbConnection = knex(context.connection.OrganizationDb);
        appManagerDbConnection = knex(context.connection.AppManagerDb);

        let loginEmployeeId = context.Employee_Id;
        let locationId = args.locationId;
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, '', '', 'UI', false, formIds.locations);

        if(Object.entries(checkRights).length && checkRights.Role_Delete === 1){


            let isLocation = await isLocationUsed(organizationDbConnection, locationId, 'delete');
            if(isLocation){
                throw 'CHR00110';
            }
            
            let customFormName = await getCustomForms(appManagerDbConnection, formIds.locations, context.Org_Code);
            let formName = customFormName && customFormName.New_Form_Name ? customFormName.New_Form_Name : 'Locations';

            await organizationDbConnection(ehrTables.location)
                    .where('Location_Id', locationId).del();

            let dataSetupStatus = await organizationDbConnection(ehrTables.dataSetupDashboard).select('Status').where('Form_Id', formIds.locations).first();  
            if(dataSetupStatus && dataSetupStatus.Status.toLowerCase() == 'completed')      
                await updateDataSetupDashboard(organizationDbConnection, null, formIds.locations, 'Open');

            let systemLogParams = {
                action: 'Delete',
                userIp: context.User_Ip,
                employeeId: loginEmployeeId,
                formName: formName,
                trackingColumn: 'Location_Id',
                organizationDbConnection: organizationDbConnection,
                uniqueId: locationId
            };
    
            await commonLib.func.createSystemLogActivities(systemLogParams);

            organizationDbConnection ? organizationDbConnection.destroy() : null;
            appManagerDbConnection ? appManagerDbConnection.destroy() : null;
            return {errorCode: '', message: 'Successfully deleted the location details'};
        
        }else{
            throw '_DB0103';
        }
        
    } catch(err){
        console.error('Error in deleteLocation function main catch block.', err);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        appManagerDbConnection ? appManagerDbConnection.destroy() : null;
        let errResult = commonLib.func.getError(err, 'CHR00109');
        throw new ApolloError(errResult.message, errResult.code);
    }
}