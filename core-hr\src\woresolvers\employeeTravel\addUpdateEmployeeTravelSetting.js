const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const knex = require('knex');
const { ApolloError } = require('apollo-server-lambda');
const { ehrTables } = require('../../../common/tablealias');
const moment = require('moment');
module.exports.addUpdateEmployeeTravelSetting = async (parent, args, context) => {
  let organizationDbConnection;
  try {
    console.log('Inside addUpdateEmployeeTravelSetting function', args);
    const {
      employeeTravelSettingId,
      emailRecipients,
      additionalRecipients,
      formId,
    } = args;
    const loginEmployeeId = context.Employee_Id;

    // Check access rights
    organizationDbConnection = knex(context.connection.OrganizationDb);
    const checkRights = await commonLib.func.checkEmployeeAccessRights(
      organizationDbConnection,
      loginEmployeeId,
      null,
      '',
      'UI',
      false,
      formId
    );

    if (Object.keys(checkRights).length <= 0 || checkRights.Role_Update !== 1) {
      console.log('No update access to this form');
      throw '_DB0111';
    }

    // Prepare data for insert/update
    const travelSettingData = {
      Email_Recipients : JSON.stringify(emailRecipients),
      Additional_Recipients : JSON.stringify(additionalRecipients),
      Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
      Updated_By: loginEmployeeId,
    };

    if (!employeeTravelSettingId) {
      // Insert new record
      travelSettingData.Added_On = moment().utc().format('YYYY-MM-DD HH:mm:ss');
      travelSettingData.Added_By = loginEmployeeId;

     await organizationDbConnection(ehrTables.employeeTravelSetting)
        .insert(travelSettingData)
        .returning('*');

      return {
        errorCode: '',
        message: 'Employee travel setting added successfully.',

      };
    } else {
      // Update existing record
      const updatedRecord = await organizationDbConnection(ehrTables.employeeTravelSetting)
        .where('Employee_Travel_Setting_Id ', employeeTravelSettingId)
        .update(travelSettingData)
        .returning('*');

      return {
        errorCode: '',
        message: 'Employee travel setting updated successfully.'
      };
    }
  } catch (error) {
    console.error('Error in addUpdateEmployeeTravelSetting function:', error);
    const errResult = commonLib.func.getError(error, 'CCH0017');
    throw new ApolloError(errResult.message, errResult.code);
  } finally {
    if (organizationDbConnection) await organizationDbConnection.destroy();
  }
};