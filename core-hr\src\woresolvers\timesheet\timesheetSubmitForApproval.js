const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const knex = require('knex');
const { ehrTables } = require('../../../common/tablealias');
const { ApolloError } = require('apollo-server-lambda');
const { formIds } = require('../../../common/appconstants');
const { getTimesheetInstanceData,getTimeSheetEventId, getExistingTimesheetData } = require('../../../common/commonfunctions');
const moment = require('moment');
module.exports.timesheetSubmitForApproval = async (parent, args, context, info) => {
  let organizationDbConnection;
  try {
    console.log("Inside timesheetSubmitForApproval function.");
    let employeeId = context.Employee_Id;
    let projectFormId = formIds.projectSettings
    const formId = args.selfService === 1 ? formIds.timeSheet : formIds.timeSheetMyTeam;
    let orgCode=context.Org_Code;
    organizationDbConnection = knex(context.connection.OrganizationDb);
    // Checking employee access rights
    let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, employeeId, '', '', 'UI', false, formId);
    if(args.selfService === 0 && Object.keys(checkRights).length >= 0 && checkRights.Is_Manager !== 1 && checkRights.Employee_Role.toLowerCase() !== 'admin'){
      throw '_DB0114'
    }
    if(Object.keys(checkRights).length <= 0 || checkRights.Role_Update === 0){
      throw "_DB0102";
    }
      //Update the data
      await organizationDbConnection.transaction(async (trx) => {
        let existingData = await getExistingTimesheetData(organizationDbConnection,trx,args)
        if(existingData && existingData.length > 0){
          throw 'CHR0122';
      }
        await organizationDbConnection(ehrTables.empTimesheet)
          .update({
            "Approval_Status": "Applied"
          })
          .transacting(trx)
          .where("Request_Id", args.requestId).then(async (data) => {
            let instanceData = await getTimesheetInstanceData(organizationDbConnection, args,trx);
            instanceData.Added_On = moment().utc().format('YYYY-MM-DD HH:mm:ss');
            instanceData.Added_By = employeeId;
            let eventId = await getTimeSheetEventId(organizationDbConnection);
            let initiateTimesheetWorkflow = await commonLib.func.initiateWorkflow(eventId, instanceData, orgCode, projectFormId,employeeId);
            if (initiateTimesheetWorkflow.status == 200 && initiateTimesheetWorkflow.data && initiateTimesheetWorkflow.data.workflowProcessInstanceId) {
              let updateParam = { Process_Instance_Id: initiateTimesheetWorkflow.data.workflowProcessInstanceId };
              await commonLib.leaveCommonFunction.updateTableWithTrxBasedOnCondition(organizationDbConnection, ehrTables.empTimesheet, updateParam, trx, 'Request_Id', '=', args.requestId, "", "");
            }


          }).catch((err) => {
            console.log("Error in timesheetSubmitForApproval function .catch block.",err);
            throw err;
          })

      }).then(async (result) => {
        let systemLogParam = {
          userIp: context.User_Ip,
          employeeId: employeeId,
          organizationDbConnection: organizationDbConnection,
          message: 
          `Timesheet with request id : ${args.requestId} has been sent for approval`
      };
        await commonLib.func.createSystemLogActivities(systemLogParam);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        return { errorCode: "", message: "timesheet  details submitted for approval successfully." };
      }).catch((error) => {
        //Destroy DB connection
        console.log("Error in timesheetSubmitForApproval function .catch block.",error)
        throw 'CHR0123';
      });
    
  } catch (e) {
    //Destroy DB connection
    organizationDbConnection ? organizationDbConnection.destroy() : null;
    console.log("Error in timesheetSubmitForApproval function main catch block.", e);

    let errResult = commonLib.func.getError(e, "CHR00101");
    throw new ApolloError(errResult.message, errResult.code);
  }
};


