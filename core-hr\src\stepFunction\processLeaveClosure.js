const knex = require('knex');
//Require moment
const moment = require('moment-timezone');

// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//get tablealias
const{appManagerTables,ehrTables}=require("../../common/tablealias")
const{defaultValues}=require('../../common/appconstants');
const{getLeaveClosureAndLeaveEncashmentDetails,getEmployeeAndLeaveTypeInArray,getPendingLeaveRequest,processPendingLeaveRequestWhichIsEligibleForLeaveClosure,processAndUpdateEncashMentLeave,getAllResignationDetails,getAllTheEmployeesIdWhoseResignationIsInAppliedOrApproved,getAuditEmpEligibleLeave,processCarryOverLeaveClosureDetails,getEmployeeChildCount,addChildCountAndCustomGroupToLeaveClosureAndLeaveEncashmentDetails,getCustomGroupEmployeeDetails,getLeaveQuarterDetails,getDetailsBasedOnCondition}=commonLib.leaveCommonFunction;

//this function is the main function to process the leave closure for each db if any new configuration is added we don't need to do any change in this function.
module.exports.processLeaveClosure=async (event,context)=>{
    let appmanagerDbConnection;
    let organizationDbConnection;
    let masterTable=appManagerTables.leaveClosureManager;
    try{
        console.log('Inside processLeaveClosure function',event);
        let inputStatus=event.input.status;
        let inputParams={Status:"InProgress"};
        if(inputStatus && inputStatus.toLowerCase()==='failed')
        {
            inputStatus='Failed'
        }
        else if(inputStatus && inputStatus.toLowerCase()==='inprogress')
        {
            inputStatus='InProgress'
        }
        else{
            inputStatus='Open'
        }
        let databaseConnection=await commonLib.stepFunctions.getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,'',1);
        // check whether data exist or not
        if(Object.keys(databaseConnection).length){
            // form app manager database connection
            appmanagerDbConnection=knex(databaseConnection.AppManagerDb);
            let openInstances= await commonLib.func.getDataFromTableAccordingToStatus(appmanagerDbConnection,masterTable,'Status',inputStatus);
            if(openInstances && openInstances.length>0)
            {
                //number of instances should be processed in one call
                let instanceToBeProcessed=(openInstances.length>defaultValues.activeInstanceToBeProcessed)?(defaultValues.activeInstanceToBeProcessed):(openInstances.length); 
                for(let i=0;i<instanceToBeProcessed;i++)
                {
                    let orgCode=openInstances[i]['Org_Code'];
                    inputParams={Status:"InProgress"};
                    await commonLib.func.updateTableBasedOnCondition(appmanagerDbConnection,masterTable,inputParams,"Org_Code",orgCode)
                    let orgRegionDetails = await commonLib.func.getOrgRegionDetails(appmanagerDbConnection,orgCode);
                    if(orgRegionDetails && Object.keys(orgRegionDetails).length > 0){
                        let additionalHeaders = await commonLib.stepFunctions.formAdditionalHeaders(orgRegionDetails);
                        //Get database connection
                        let connection=await commonLib.stepFunctions.getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,orgCode,0,additionalHeaders);
                        if(Object.keys(connection).length>0){
                            organizationDbConnection = knex(connection.OrganizationDb);
                            await processEachOrgForLeaveClosure(organizationDbConnection,appmanagerDbConnection,orgCode)
                        }
                    }
                    else{
                        inputParams['Status']='Failed';
                        inputParams['Error']="Error while getting  orgRegionDetails."
                        await commonLib.func.updateTableBasedOnCondition(appmanagerDbConnection,masterTable,inputParams,"Org_Code",orgCode);
                    }
                    organizationDbConnection?organizationDbConnection.destroy():null;
                }
            }  
        }
        appmanagerDbConnection?appmanagerDbConnection.destroy():null;
        organizationDbConnection?organizationDbConnection.destroy():null;
        let response={
            nextStep:'End',
            input:{'status':inputStatus},
            message:'Event triggered to end the process because no instance is remaining to process.'          
        }
        return response;
    }
    catch(e)
    {
        console.log("Error in processLeaveClosure main catch block",e);
        appmanagerDbConnection?appmanagerDbConnection.destroy():null;
        organizationDbConnection?organizationDbConnection.destroy():null;
        let response={
            nextStep:'End',
            input:{'status':inputStatus},
            message:'Event triggered to end the process because error occurred.'          
        }
        return response;
    }
}


async function processEachOrgForLeaveClosure(organizationDbConnection,appmanagerDbConnection,orgCode)
{
    let inputParams={Status:"Success"};
    try{
        console.log('Inside processEachOrgForLeaveClosure function.',)
        let currentUtcDateTime=moment.utc().format('YYYY-MM-DD HH:mm:ss')
        let currentDate=moment(currentUtcDateTime).format('YYYY-MM-DD');
        //we will get all the employess whose leave closure need to be done along with thier leave type and leave taken details
        console.log("currentUtcDateTime",currentUtcDateTime,'orgCode',orgCode);
        let leaveClosureAndLeaveEncashmentDetails= await getLeaveClosureAndLeaveEncashmentDetails(organizationDbConnection,currentDate);
        if(leaveClosureAndLeaveEncashmentDetails.length>0)
        {
            let employeeAndLeaveTypeInArray= await getEmployeeAndLeaveTypeInArray(leaveClosureAndLeaveEncashmentDetails,0);
            console.log("getEmployeeAndLeaveTypeInArray",employeeAndLeaveTypeInArray)
            let leaveTypeIdHavingClosure=employeeAndLeaveTypeInArray.leaveTypeIdHavingClosure;
            let employeeIdHavingClosure=employeeAndLeaveTypeInArray.employeeIdHavingClosure;

            let childCount= await getEmployeeChildCount(organizationDbConnection,employeeIdHavingClosure);
            
            let customGroupLeaveType=await getCustomGroupEmployeeDetails(organizationDbConnection,leaveTypeIdHavingClosure);
            console.log("customGroupLeaveType",customGroupLeaveType);

            leaveClosureAndLeaveEncashmentDetails= await addChildCountAndCustomGroupToLeaveClosureAndLeaveEncashmentDetails(leaveClosureAndLeaveEncashmentDetails,childCount,customGroupLeaveType);
            console.log("leaveClosureAndLeaveEncashmentDetails",leaveClosureAndLeaveEncashmentDetails);

            let pendingLeaveRequest=await getPendingLeaveRequest(organizationDbConnection,leaveTypeIdHavingClosure,employeeIdHavingClosure);
            console.log('pendingLeaveRequest',pendingLeaveRequest);

            leaveClosureAndLeaveEncashmentDetails=await processPendingLeaveRequestWhichIsEligibleForLeaveClosure(organizationDbConnection,pendingLeaveRequest,leaveClosureAndLeaveEncashmentDetails,currentUtcDateTime);
            console.log('leaveClosureAndLeaveEncashmentDetails',leaveClosureAndLeaveEncashmentDetails);

            leaveClosureAndLeaveEncashmentDetails=await processAndUpdateEncashMentLeave(organizationDbConnection,leaveClosureAndLeaveEncashmentDetails,currentUtcDateTime);
            console.log('leaveClosureAndLeaveEncashmentDetails',leaveClosureAndLeaveEncashmentDetails);

            let auditEmpEligibleLeaveDetails=await getAuditEmpEligibleLeave(organizationDbConnection,leaveTypeIdHavingClosure,employeeIdHavingClosure);
            console.log('auditEmpEligibleLeaveDetails',auditEmpEligibleLeaveDetails);
            
            let leaveQuarterDetails= await getLeaveQuarterDetails(organizationDbConnection);
            let serviceLeaveDetails= await getDetailsBasedOnCondition(organizationDbConnection,ehrTables.empServiceLeave,"","","");
            let maternityLeaveSlabDetails= await getDetailsBasedOnCondition(organizationDbConnection,ehrTables.maternitySlab,"","","");
            let experienceLeaveDetails= await getDetailsBasedOnCondition(organizationDbConnection,ehrTables.empExperienceLeave,"","","");
            let orgDetails= await commonLib.func.getOrgDetails(orgCode,organizationDbConnection,0);
            
            await processCarryOverLeaveClosureDetails(organizationDbConnection,leaveClosureAndLeaveEncashmentDetails,auditEmpEligibleLeaveDetails,orgDetails,leaveTypeIdHavingClosure,leaveQuarterDetails,serviceLeaveDetails,maternityLeaveSlabDetails,experienceLeaveDetails,currentUtcDateTime)
        }
        else{
            console.log('No leave closure is pending for',currentDate);
        }
        await commonLib.func.updateTableBasedOnCondition(appmanagerDbConnection,appManagerTables.leaveClosureManager,inputParams,"Org_Code",orgCode)
    }
    catch(e)
    {
        console.log('Error in processEachOrgForLeaveClosure function main catch block',e)
        if(e && typeof(e)==='string' && e.length===7)
        {
            e=e;
        }
        else{
            e='CDG0164';
        }
        let errResult = commonLib.func.getError(e, 'CDG0164');
        inputParams['Error']=errResult.message;
        await commonLib.func.updateTableBasedOnCondition(appmanagerDbConnection,appManagerTables.leaveClosureManager,inputParams,"Org_Code",orgCode)
    }
}