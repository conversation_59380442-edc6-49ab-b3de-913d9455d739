// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../common/tablealias');
// require formIds
const { formIds } = require('../../../common/appconstants');
// require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');

//function to retrieve dynamic form fields
module.exports.retrieveDynamicFormFields = async (parent, args, context, info) => {
    let organizationDbConnection;
    let result = [];
    try {
        console.log("Inside retrieveDynamicFormFields function.");
        const loginEmployeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);

        let accessFormId = args.formId || formIds.customFields;
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, null, '', 'UI', false, accessFormId);
        if (Object.keys(checkRights).length === 0 || checkRights.Role_View !== 1) {
            throw ('_DB0100');
        }

        // Fetch dynamic fields data
        let dynamicFieldsData = await organizationDbConnection(ehrTables.customFields + " as CF")
            .select(
                "CF.*",
                "CFAF.Form_Id",
                "CFAF.Integration_Mapping_Key",
                "CFAF.Mandatory",
                "EF.Form_Name",
                "IV.Validation_Name",
                organizationDbConnection.raw("CONCAT_WS(' ', EP1.Emp_First_Name, EP1.Emp_Middle_Name, EP1.Emp_Last_Name) as Added_By"),
                organizationDbConnection.raw("CONCAT_WS(' ', EP2.Emp_First_Name, EP2.Emp_Middle_Name, EP2.Emp_Last_Name) as Updated_By")
            )
            .leftJoin(ehrTables.customFieldAssociatedForms + " as CFAF", "CFAF.Custom_Field_Id", "CF.Custom_Field_Id")
            .leftJoin(ehrTables.inputValidations + " as IV", "IV.Validation_Id", "CF.Validation_Id")
            .leftJoin(ehrTables.ehrForms + " as EF", "EF.Form_Id", "CFAF.Form_Id")
            .leftJoin(ehrTables.empPersonalInfo + " as EP1", "EP1.Employee_Id", "CF.Added_By")
            .leftJoin(ehrTables.empPersonalInfo + " as EP2", "EP2.Employee_Id", "CF.Updated_By")
            .modify((queryBuilder) => {
                if (args.formId) {
                    queryBuilder.where("CFAF.Form_Id", args.formId);
                }
            });

        if (dynamicFieldsData && dynamicFieldsData.length) {

            // Step 1: Get role IDs from the dynamic fields
            let roleIds = dynamicFieldsData
                .map(field => field.Roles_Id ? JSON.parse(field.Roles_Id) : [])
                .flat();

            // Remove duplicates from roleIds array
            roleIds = [...new Set(roleIds)];

            // Step 2: Fetch role names based on roleIds
            let roleNames = [];
            if (roleIds.length > 0) {
                roleNames = await organizationDbConnection(ehrTables.roles + " as R")
                    .select("R.Roles_Id", "R.Roles_Name")
                    .whereIn('R.Roles_Id', roleIds);
            }

            // Step 3: Group the data to create the desired structure
            let dynamicFieldsMap = new Map();

            dynamicFieldsData.forEach(row => {
                // Initialize the field if not already in the map
                if (!dynamicFieldsMap.has(row.Custom_Field_Id)) {
                    dynamicFieldsMap.set(row.Custom_Field_Id, {
                        Custom_Field_Id: row.Custom_Field_Id,
                        Custom_Field_Name: row.Custom_Field_Name,
                        Custom_Field_Type: row.Custom_Field_Type,
                        Min_Validation: row.Min_Validation,
                        Max_Validation: row.Max_Validation,
                        Validation_Id: row.Validation_Id,
                        Url_Link: row.Url_Link,
                        Validation_Name: row.Validation_Name,
                        Dropdown_Values: row.Dropdown_Values,
                        Visibility_Condition: formVisibilityCondition(row.Visibility_Condition, dynamicFieldsData),
                        Added_On: row.Added_On,
                        Added_By: row.Added_By,
                        Updated_On: row.Updated_On,
                        Updated_By: row.Updated_By,
                        Roles_Id: [],
                        FormIds: []
                    });
                }

                const field = dynamicFieldsMap.get(row.Custom_Field_Id);

                // Add roles if not already added
                if (row.Roles_Id && !field.Roles_Id?.length) {
                    // Convert the JSON string to an array
                    const rolesArray = JSON.parse(row.Roles_Id);
                    field.Roles_Id = rolesArray.map(role => {
                        return {
                            Roles_Id: role,
                            Roles_Name: roleNames?.find(r => r.Roles_Id == role)?.Roles_Name
                        }
                    })
                }

                // Add form details if not already added
                if (row.Form_Id && !field.FormIds.some(form => form.Form_Id === row.Form_Id)) {
                    field.FormIds.push({
                        Form_Id: row.Form_Id,
                        Form_Name: row.Form_Name,
                        Integration_Mapping_Key: row.Integration_Mapping_Key,
                        Mandatory: row.Mandatory
                    });
                }
            });

            // Step 4: Convert the map to an array
            result = Array.from(dynamicFieldsMap.values());
        }

        return {
            errorCode: "",
            message: "Custom form fields retrieved successfully.",
            dynamicFields: JSON.stringify(result)
        };

    } catch (e) {
        console.log('Error in retrieveDynamicFormFields function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'CF00001');
        throw new ApolloError(errResult.message, errResult.code);
    } finally {
        //Destroy DB connection
        if (organizationDbConnection) {
            organizationDbConnection.destroy();
        }
    }
}

/**
 * Processes visibility condition for a custom field by finding the referenced field's details
 * @param {Object} visibilityCondition - The visibility condition object containing Custom_Field_Id and Value
 * @param {Array} dynamicFieldsData - Array of all dynamic fields with their details
 * @returns {Object|null} Returns enhanced visibility condition object with field name and type, or null if no condition
 */
const formVisibilityCondition = (visibilityCondition, dynamicFieldsData) => {
    if (visibilityCondition) {
        visibilityCondition = JSON.parse(visibilityCondition);
        const visibilityConditionField = dynamicFieldsData.find(field => field.Custom_Field_Id == visibilityCondition.Custom_Field_Id);
        if (visibilityConditionField) {
            const visibilityConditionFieldDetails = {
                ...visibilityCondition,
                Custom_Field_Name: visibilityConditionField.Custom_Field_Name,
                Custom_Field_Type: visibilityConditionField.Custom_Field_Type,
            };
            return visibilityConditionFieldDetails;
        }
    }
    return null;
}
