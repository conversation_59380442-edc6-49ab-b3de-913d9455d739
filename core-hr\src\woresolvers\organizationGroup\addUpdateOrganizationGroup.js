const { UserInputError, ApolloError } = require('apollo-server-lambda')
const {
  formName,
  systemLogs,
  formIds
} = require('../../../common/appconstants')
const { ehrTables } = require('../../../common/tablealias')
const moment = require('moment')
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib
const { validateCommonRuleInput } = require('../../../common/inputValidations')
// require knex
const knex = require('knex')

module.exports.addUpdateorganizationGroup = async (parent, args, context) => {
  let validationError = {}
  let isEntomoRelatedChanged = true
  try {
    const loginEmployee_Id = context.Employee_Id
    let organizationGroupFormId = formIds.organizationGroup
    const organizationDbConnection = knex(context.connection.OrganizationDb)
    let checkRights = await commonLib.func.checkEmployeeAccessRights(
      organizationDbConnection,
      loginEmployee_Id,
      '',
      '',
      'UI',
      false,
      organizationGroupFormId
    )
    if (
      Object.keys(checkRights).length <= 0 ||
      (args.organizationGroupId && checkRights.Role_Update === 0) ||
      (!args.organizationGroupId && checkRights.Role_Add === 0)
    ) {
      if (!args.organizationGroupId && checkRights.Role_Add === 0) {
        throw '_DB0101'
      } else {
        throw '_DB0102'
      }
    }
    const {
      organizationGroupId,
      description,
      Status,
      organizationGroupCode,
      organizationGroup,
      level
    } = args
    let organizationGroupData = {
      Organization_Group_Code: organizationGroupCode,
      Organization_Group: organizationGroup,
      Level: level ? level : null
    }
    if (description) {
      organizationGroupData.Description = description
    }
    if (Status && Status.length) {
      organizationGroupData.Status = Status
    }
    const fieldValidations = {}
    if (description) {
      fieldValidations.description = 'IVE0283'
    }
    if (organizationGroup) {
      fieldValidations.organizationGroup = 'IVE0435'
    }
    if (organizationGroupCode) {
      fieldValidations.organizationGroupCode = 'IVE0499'
    }
    if (level !== null && level !== undefined) {
      fieldValidations.level = 'IVE0606'
    }
    if (fieldValidations) {
      validationError = await validateCommonRuleInput(args, fieldValidations)
    }
    if (Object.keys(validationError).length !== 0) {
      throw 'IVE0000'
    }

    //Already Exist Validation
    let alreadyExists = await organizationDbConnection(ehrTables.organizationGroup)
      .select('Organization_Group_Id')
      .where(function () {
        if (organizationGroupCode) {
          this.where('Organization_Group_Code', organizationGroupCode)
        } else {
          this.where('Organization_Group', organizationGroup)
        }
      })
      .whereNot('Organization_Group_Id', organizationGroupId || null)
      .first()

    if (alreadyExists) {
      throw 'CHR0140'
    }

    let updateResult
    if (organizationGroupId) {

      //If partnerId  is entomo then check entomo data is changed or not
      if (context.partnerid && context.partnerid.toLowerCase() === 'entomo') {
        isEntomoRelatedChanged = await validateEntomoDataChange(organizationDbConnection, organizationGroupId, organizationGroupData)
      }

      organizationGroupData.Updated_On = moment()
        .utc()
        .format('YYYY-MM-DD HH:mm:ss')
      organizationGroupData.Updated_By = loginEmployee_Id

      updateResult = await organizationDbConnection(ehrTables.organizationGroup)
        .where('Organization_Group_Id', organizationGroupId)
        .update(organizationGroupData)
    } else {
      organizationGroupData.Added_On = moment()
        .utc()
        .format('YYYY-MM-DD HH:mm:ss')
      organizationGroupData.Added_By = loginEmployee_Id
      updateResult = await organizationDbConnection(
        ehrTables.organizationGroup
      ).insert(organizationGroupData)
    }
    if (!updateResult) {
      throw 'CHR0133'
    }
    let systemLogParam = {
      userIp: context.User_Ip,
      employeeId: loginEmployee_Id,
      organizationDbConnection: organizationDbConnection,
      message: ` The organization group for organization_group_id : ${organizationGroupId ? organizationGroupId : updateResult[0]
        } is being ${organizationGroupId ? 'updated' : 'added'}.`
    }
    await commonLib.func.createSystemLogActivities(systemLogParam)

    if (updateResult && context.partnerid && context.partnerid.toLowerCase() === 'entomo' && isEntomoRelatedChanged) {
      organizationGroupData.Code = organizationGroupData.Organization_Group_Code
      organizationGroupData.Old_Code = isEntomoRelatedChanged
      organizationGroupData.Name = organizationGroupData.Organization_Group
      organizationGroupData.Description = organizationGroupData.Description ? organizationGroupData.Description : null
      let inputData = {
        'orgCode': context.Org_Code, 'partnerId': context.partnerid, 'inputParams': {
          entityId: updateResult,
          entityType: 'Organization Group',
          functionName: 'entomoSync',
          action: args.organizationGroupId ? 'Update' : 'Add',
          formData: organizationGroupData
        }
      }
      //Trigger Entomo Sync
      await commonLib.stepFunctions.triggerStepFunction(process.env.commonStepFunction, 'commonStepFunction', '', inputData);
    }

    organizationDbConnection ? organizationDbConnection.destroy() : null
    return {
      errorCode: '',
      message: `The organization group is being ${organizationGroupId ? 'updated' : 'added'
        }. successfully`
    }
  } catch (error) {
    console.log(
      'Error in the addUpdateorganizationGroup() function main catch block. ',
      error
    )
    if (error === 'IVE0000') {
      console.log(
        'Validation error in the addUpdateorganizationGroup function',
        validationError
      )
      errResult = commonLib.func.getError('', 'IVE0000')
      throw new UserInputError(errResult.message, {
        validationError: validationError
      }) //Return error response
    } else {
      let errorCode = error.code === 'ER_DUP_ENTRY' ? 'CHR0130' : error
      errResult = commonLib.func.getError(errorCode, 'CHR0131')
      throw new ApolloError(errResult.message, errResult.code)
    }
  }
}

async function validateEntomoDataChange(organizationDbConnection, organizationGroupId, organizationGroupData) {
  try {
    let entomoData = await organizationDbConnection
      .select('Organization_Group_Code as Old_Code', 'Organization_Group', 'Description')
      .from(ehrTables.organizationGroup)
      .where('Organization_Group_Id', organizationGroupId)
      .first()

    if (entomoData) {
      if (entomoData.Old_Code !== organizationGroupData.Organization_Group_Code ||
        entomoData.Organization_Group !== organizationGroupData.Organization_Group ||
        entomoData.Description !== organizationGroupData.Description) {
        return entomoData.Old_Code
      }
    }
    return false
  } catch (error) {
    console.log('Error in validateEntomoDataChange function', error)
    return false
  }
}
