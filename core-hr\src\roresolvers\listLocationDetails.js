
//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../../common/tablealias');

module.exports.listLocationDetails = async (parent, args, context, info) => {
    
    let organizationDbConnection;
    try {
        console.log("Inside listLocationDetails() function")
        organizationDbConnection = knex(context.connection.OrganizationDb);

        let query =  organizationDbConnection(ehrTables.location + ' as LOC')
        .select('LOC.*', 'CI.City_Name', 'ST.State_Name', 'COU.Country_Name', 'CURR.Currency_Name', 'TIME.Zone_Id as TimeZone_Id',
            organizationDbConnection.raw("CONCAT_WS(' ',TIME.TimeZone_Id, TIME.Offset_Time) as TimeZone_Name"),
            organizationDbConnection.raw("CONCAT_WS(' ', UPBY.Emp_First_Name, UPBY.Emp_Middle_Name, UPBY.Emp_Last_Name) as Updated_By_Name"),
            organizationDbConnection.raw("CONCAT_WS(' ', ADBY.Emp_First_Name, ADBY.Emp_Middle_Name, ADBY.Emp_Last_Name) as Added_By_Name"),
            'LOC.Barangay', 'LOC.Barangay_Id', 'LOC.Region'
        )
        .leftJoin(ehrTables.city + ' as CI', 'CI.City_Id', 'LOC.City_Id')
        .leftJoin(ehrTables.state + ' as ST', 'ST.State_Id', 'LOC.State_Id')
        .leftJoin(ehrTables.country + ' as COU', 'COU.Country_Code', 'LOC.Country_Code')
        .leftJoin(ehrTables.currency + ' as CURR', 'CURR.Currency_Id', 'LOC.Currency_Id')
        .leftJoin(ehrTables.empPersonalInfo + ' as UPBY', 'UPBY.Employee_Id', 'LOC.Updated_By')
        .leftJoin(ehrTables.empPersonalInfo + ' as ADBY', 'ADBY.Employee_Id', 'LOC.Added_By')
        .leftJoin(ehrTables.timezone + ' as TIME', 'TIME.Zone_Id', 'LOC.Zone_Id')
        if(args.offset){
            query.offset(args.offset);
        }
        if(args.limit){
            query.limit(args.limit);
        }
        const locationData = await query;

        organizationDbConnection ? organizationDbConnection.destroy() : null;

        return { errorCode: '', message: 'Successfully retrieved location list details', location: locationData}
 
    }catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.error('Error in listLocationDetails function main catch block.', e);
        let errResult = commonLib.func.getError(e, '_UH0001');
        throw new ApolloError(errResult.message, errResult.code)
    }
}
