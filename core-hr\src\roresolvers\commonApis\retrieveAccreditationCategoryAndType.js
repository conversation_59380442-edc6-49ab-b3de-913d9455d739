// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');

let organizationDbConnection;
module.exports.retrieveAccreditationCategoryAndType = async (parent, args, context, info) => {
    try {
        console.log("Inside retrieveAccreditationCategoryAndType function.")
        organizationDbConnection = knex(context.connection.OrganizationDb);
        return (
             organizationDbConnection(ehrTables.accreditationCategoryType)
                .select("*")
                .then((data) => {
                    //destroy the connection
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    return { errorCode: "", message: "Accreditation category and type retrieved successfully.", accreditationCategoryAndType: data };
                })
                .catch((err) => {
                    console.log('Error in retrieveAccreditationCategoryAndType .catch() block', err);
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    let errResult = commonLib.func.getError(err, 'CCH0108');
                    throw new ApolloError(errResult.message, errResult.code);
                })
        )
    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in retrieveAccreditationCategoryAndType function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'CCH0008');
        throw new ApolloError(errResult.message, errResult.code);
    }
}
