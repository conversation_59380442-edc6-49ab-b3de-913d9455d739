//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { UserInputError, ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../../common/tablealias');
const { getAttendanceExist, getCompensatoryOffExist, getLeaveExist, checkSameHolidayExists, getPaySlipExist, updateDataSetupDashboard } = require('../../common/commonfunctions');
const { formName, formIds } = require('../../common/appconstants');
const { validateHolidayInputs } = require('../../common/holidayInputValidation')
const moment = require('moment');


module.exports.addCustomGroupHolidays = async (parent, args, context, info) => {
    console.log('Inside addCustomGroupHolidays function');
    let organizationDbConnection;
    let validationError = {};
    let loginEmployeeId = context.Employee_Id;
    let orgCode = context.Org_Code
    let typeOfValidation = null
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);

        //Form Access check for adding custom group holidays
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, formName.holidays, '', 'UI');
        if (Object.keys(checkRights).length > 0 && checkRights.Role_Add === 1) {
            validationError = await validateHolidayInputs(args, 1);
            if (Object.keys(validationError).length == 0) {
                let loginEmployeeCurrentDateTime = await commonLib.func.getEmployeeTimeZone(loginEmployeeId, organizationDbConnection, 1);
                return (
                    organizationDbConnection
                        .transaction(async (trx) => {
                            let updateDetails = args.holidayData
                            let customGroupIds = []
                            //With the updateDetails form the updatable data
                            let formedData = updateDetails.map((el) => {
                                let form = []
                                //Get dates between Start_Date and End_Date
                                while (new Date(el.Start_Date) <= new Date(el.End_Date)) {
                                    form.push({
                                        'Holiday_Id': el.Holiday_Id,
                                        'Holiday_Date': el.Start_Date,
                                        "Mandatory": el.Mandatory,
                                        "Holiday": el.Holiday,
                                        "Personal_Choice": el.Personal_Choice,
                                        "Description": el.Description,
                                        "Custom_Group_Id": el.Custom_Group_Id,
                                        "Added_By": loginEmployeeId,
                                        "Added_Date": loginEmployeeCurrentDateTime
                                    })
                                    el.Start_Date = moment(el.Start_Date).add(1, 'days').format("YYYY-MM-DD");
                                }
                                //get the customGroupIds to get the employeeIds
                                if (!customGroupIds.includes(el.Custom_Group_Id)) {
                                    customGroupIds.push(el.Custom_Group_Id)
                                }
                                return form;
                            })
                            //Getting all the multi dimensional array of objects to single array of object
                            formedData = formedData.flat();
                            //fetch the employeeIds with the customGroupId
                            return (
                                organizationDbConnection(ehrTables.customGroupEmployees)
                                    .select("Employee_Id")
                                    .transacting(trx)
                                    .whereIn('Group_Id', customGroupIds)
                                    .then(async (data) => {
                                        //Merge the employeeId with the formedData
                                        let result = data.map(a => a.Employee_Id);
                                        for (let i = 0; i < formedData.length; i++) {
                                            formedData[i].Employee_Id = result
                                        }

                                        //validation to check if same date is added for the custom group
                                        let holidayCheckResult = await checkSameHolidayExists(formedData, organizationDbConnection)
                                        if (!holidayCheckResult) {
                                            throw 'CGH0114'
                                        }
                                        if (holidayCheckResult.length) {
                                            // It contains same date
                                            throw 'CGH0115'
                                        }

                                        //validation to check if date is in payslip
                                        let paySlipResult = await getPaySlipExist(formedData, organizationDbConnection, orgCode)
                                        if (!paySlipResult) {
                                            throw 'CGH0116'
                                        }
                                        if (paySlipResult.length) {
                                            console.log('Payslip contains date')
                                            throw 'CGH0117'
                                        }

                                        //validation to check if date is in emp_attendance table
                                        let attendanceResult = await getAttendanceExist(formedData, organizationDbConnection);
                                        if (!attendanceResult) {
                                            throw 'CGH0105'
                                        }
                                        if (attendanceResult.length) {
                                            typeOfValidation = "attendance"
                                            //It contains data
                                            throw 'CGH0103'
                                        }

                                        //validation to check if date is in compensatory table
                                        let compensatoryResult = await getCompensatoryOffExist(formedData, organizationDbConnection)
                                        if (!compensatoryResult) {
                                            throw 'CGH0106'
                                        }
                                        if (compensatoryResult.length) {
                                            typeOfValidation = "compensatory"
                                            throw 'CGH0103'
                                        }

                                        //validation to check if date is in leave table
                                        let employeeLeaveResult = await getLeaveExist(formedData, organizationDbConnection)
                                        if (!employeeLeaveResult) {
                                            throw 'CGH0107'
                                        }
                                        if (employeeLeaveResult.length) {
                                            typeOfValidation = "leave"
                                            throw 'CGH0103'
                                        }
                                        //getting deep copy of holidayAssignedData
                                        let holidayAssignedData = JSON.parse(JSON.stringify(formedData))
                                        //Remove Employee_Id and Custom_Group_Id
                                        for (let i = 0; i < holidayAssignedData.length; i++) {
                                            delete holidayAssignedData[i].Custom_Group_Id
                                            delete holidayAssignedData[i].Employee_Id
                                        }
                                        return (
                                            organizationDbConnection(ehrTables.holidayCustomGroup)
                                                .insert(holidayAssignedData)
                                                .transacting(trx)
                                                .then(async(data) => {
                                                    let parent = data[0];
                                                    if(parent){
                                                        //Update the datasetup_dashboard status
                                                        await updateDataSetupDashboard(organizationDbConnection, trx, formIds.holidayTypes, 'Completed')
                                                    }
                                                    let holidayCustomGroupData = []
                                                    //Get the array of Holiday_Assign_ids
                                                    for (let i = 0; i < formedData.length; i++) {
                                                        holidayCustomGroupData[i] = {}
                                                        holidayCustomGroupData[i].Custom_Group_Id = formedData[i].Custom_Group_Id
                                                        holidayCustomGroupData[i].Form_Id = formIds.holidays
                                                        holidayCustomGroupData[i].Parent_Id = parent;
                                                        parent += 1
                                                    }
                                                    return (
                                                        organizationDbConnection(ehrTables.customGroupAssociated)
                                                            .insert(holidayCustomGroupData)
                                                            .transacting(trx)
                                                            .then((data) => {
                                                                if (!data) {
                                                                    console.log('Error while inserting data to custom group associated forms');
                                                                    throw 'CGH0110'
                                                                }
                                                                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                                                return { errorCode: "", message: "Custom group holidays have been inserted successfully." };
                                                            })
                                                    )
                                                })
                                        )
                                    })
                            )
                        })
                        .catch((catchError) => {
                            console.log('Error in addCustomGroupHolidays .catch() block', catchError);
                            let errResult = commonLib.func.getError(catchError, 'CGH0111');
                            //Destroy DB connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            //Return error response
                            throw new ApolloError(errResult.message, errResult.code, {typeOfValidation});
                        })
                );
            }
            else {
                throw 'IVE0000';
            }
        } else {
            console.log('No rights to add custom group holidays');
            throw '_DB0101';
        }
    } catch (mainCatchError) {
        console.log('Error in addCustomGroupHolidays function main block', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        if (mainCatchError === 'IVE0000') {
            let errResult = commonLib.func.getError('', 'IVE0000');
            console.log('Validation error in addCustomGroupHolidays function - ', validationError);
            // return response
            throw new UserInputError(errResult.message, { validationError: validationError });
        } else {
            let errResult = commonLib.func.getError(mainCatchError, 'CGH0003');
            // return response
            throw new ApolloError(errResult.message, errResult.code, {typeOfValidation});
        }
    }
}



