// Required dependencies
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const knex = require('knex');
const { ApolloError } = require('apollo-server-lambda');

const ALLOWED_FORM_IDS = [178, 16, 311];

async function getFormDetails(templateId, organizationDbConnection) {
 
  const forms = await organizationDbConnection('custom_email_templates')
    .select('Form_Id', 'To_Emails', 'CC_Emails', 'Bcc_Emails', 'Additional_Emails')
    .where('Template_Id', templateId).first();

  if (!forms) {
    throw 'CET00006';
  }

  return {
    Form_Id: forms.Form_Id,
    To_Emails: forms.To_Emails,
    CC_Emails: forms.CC_Emails,
    Bcc_Emails: forms.Bcc_Emails,
    Additional_Emails: forms.Additional_Emails
  };
}

module.exports.listEmailTemplatePlaceHolderValues = async (parent, args, context, info) => {
  let organizationDbConnection;

  try {
    console.log('Inside listEmailTemplatePlaceHolderValues function');
    // Input validation
    if (!args.templateId || args.templateId < 1 || !args.candidateId) {
      throw 'IVE0528';
    }
    
    organizationDbConnection = knex(context.connection.OrganizationDb);

    // Check access rights
    const checkRights = await commonLib.func.checkEmployeeAccessRights(
      organizationDbConnection,
      context.Employee_Id,
      null,
      '',
      'UI',
      false,
      args.accessformId
    );

    if (!checkRights || (checkRights.Role_Add !== 1 && checkRights.Role_Update !== 1)) {
      console.error('Login employee id does not have add or update access');
      throw '_DB0111';
    }

    return await organizationDbConnection.transaction(async (trx) => {
      const { Form_Id } = await getFormDetails(args.templateId, organizationDbConnection, trx);

      if (ALLOWED_FORM_IDS.includes(Form_Id)) {
       
        const experiencePortalUrl = process.env.experiencePortalUrl ? process.env.experiencePortalUrl.replace(
          '#REPLACE_ORG_CODE_DOMAIN_NAME#',`${context.Org_Code}.${process.env.leaveStatusDomainName}`) : '';

        const { emailResult } = await commonLib.func.listEmailTemplatePlaceHolderValues(
          { templateId: args.templateId, candidateId: args.candidateId },
          organizationDbConnection,
          context,
          null,
          [],
          { experiencePortalUrl: experiencePortalUrl }
        );

        const { jobCandidate, emails } = emailResult;
        return {
          errorCode: '',
          message: 'Candidate place holder values retrieved successfully.',
          jobCandidate,
          emails
        };
      }

      return { errorCode: '', message: 'No place holder values found.'};
    }).catch((err) => {
      console.error('Error in listEmailTemplatePlaceHolderValues function catch block:', err);
      throw err;
    });

  } catch (error) {
    console.error('Error in listEmailTemplatePlaceHolderValues function main catch block:', error);
    const errResult = commonLib.func.getError(error, 'CET00005');
    throw new ApolloError(errResult.message, errResult.code);
  } finally {
    if (organizationDbConnection) {
      await organizationDbConnection.destroy();
    }
  }
};
