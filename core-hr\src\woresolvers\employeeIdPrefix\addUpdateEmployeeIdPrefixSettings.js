const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const knex = require('knex');
const { ApolloError,UserInputError } = require('apollo-server-lambda');
const { ehrTables } = require('../../../common/tablealias');
const { formName} = require('../../../common/appconstants');
const moment = require('moment');
const { validateCommonRuleInput } = require('../../../common/inputValidations');

// Validate inputs
async function validateEmployeeIdPrefixInputs(args, organizationDbConnection) {
    try{
    let validationError = {};

    const configDetails = await organizationDbConnection(ehrTables.empPrefixConfig)
        .select('Config_Level', 'Is_Enabled')
        .first();

    if (configDetails && configDetails.Is_Enabled) {
        if (configDetails.Config_Level === 'ServiceProvider' && !args.serviceProviderId) {
            validationError['IVE0602'] = commonLib.func.getError('IVE0602', '').message ;
            return validationError;
        }

        if (configDetails.Config_Level === 'Organization' && args.serviceProviderId) {
            validationError['IVE0603'] = commonLib.func.getError('IVE0603', '').message;
            return validationError;
        }
    }
    if (!args.serviceProviderId) {
        const existingOrgSetting = await organizationDbConnection(ehrTables.empPrefixSettings)
            .where('Status', 'Active')
            .whereNull('Service_Provider_Id')
            .whereNot('Emp_Prefix_Setting_Id', args.empPrefixSettingId)
            .first();

        if (existingOrgSetting) {
            validationError['IVE0590'] = commonLib.func.getError('IVE0590', '').message;
            return validationError;
        }
    } else {
        const existingSetting = await organizationDbConnection(ehrTables.empPrefixSettings)
            .where('Status', 'Active')
            .where('Service_Provider_Id', args.serviceProviderId)
            .whereNot('Emp_Prefix_Setting_Id', args.empPrefixSettingId)
            .first();

        if (existingSetting) {
            validationError['IVE0591'] = commonLib.func.getError('IVE0591', '').message;
            return validationError;
        }
    }
    const prefixSuffixQuery = organizationDbConnection(ehrTables.empPrefixSettings)
        .where('Status', 'Active')
        .whereNot('Emp_Prefix_Setting_Id', args.empPrefixSettingId);

    if (args.prefix) {
        prefixSuffixQuery.where('Prefix', args.prefix);
    } else {
        prefixSuffixQuery.whereNull('Prefix');
    }

    if (args.suffix) {
        prefixSuffixQuery.where('Suffix', args.suffix);
    } else {
        prefixSuffixQuery.whereNull('Suffix');
    }

    const existingPrefixSuffix = await prefixSuffixQuery.first();

    if (existingPrefixSuffix) {
        validationError['IVE0592'] = commonLib.func.getError('IVE0592', '').message;
        throw 'IVE0000';
    }
    const prefix = args.prefix || '';
    const suffix = args.suffix || '';
    const noOfDigits = args.noOfDigits;
    const nextNumber = args.nextNumber;

    // Escape special regex characters in prefix and suffix
    const escapeRegExp = (string) => {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    };

    const escPrefix = prefix ? escapeRegExp(prefix) : '';
    const escSuffix = suffix ? escapeRegExp(suffix) : '';

    const result = await organizationDbConnection(ehrTables.empJob)
        .select(organizationDbConnection.raw(`
        MAX(
            CASE
                WHEN ? IS NOT NULL AND ? IS NOT NULL
                    AND User_Defined_EmpId REGEXP ?
                    AND LENGTH(
                        REGEXP_REPLACE(User_Defined_EmpId, CONCAT('^', ?, '([0-9]+)', ?), '$1')
                    ) = ?
                    THEN CAST(
                        REGEXP_REPLACE(User_Defined_EmpId, CONCAT('^', ?, '([0-9]+)', ?), '$1')
                        AS UNSIGNED
                    )

                WHEN ? IS NOT NULL
                    AND User_Defined_EmpId REGEXP ?
                    AND LENGTH(
                        REGEXP_REPLACE(User_Defined_EmpId, CONCAT('^', ?, '([0-9]+)$'), '$1')
                    ) = ?
                    THEN CAST(
                        REGEXP_REPLACE(User_Defined_EmpId, CONCAT('^', ?, '([0-9]+)$'), '$1')
                        AS UNSIGNED
                    )

                WHEN ? IS NOT NULL
                    AND User_Defined_EmpId REGEXP ?
                    AND LENGTH(
                        REGEXP_REPLACE(User_Defined_EmpId, CONCAT('^([0-9]+)', ?), '$1')
                    ) = ?
                    THEN CAST(
                        REGEXP_REPLACE(User_Defined_EmpId, CONCAT('^([0-9]+)', ?), '$1')
                        AS UNSIGNED
                    )

                WHEN User_Defined_EmpId REGEXP ?
                    AND LENGTH(User_Defined_EmpId) = ?
                    THEN CAST(User_Defined_EmpId AS UNSIGNED)

                ELSE 0
            END
        ) as max_number
        `, [
            // Both prefix and suffix
            prefix, suffix, `^${escPrefix}[0-9]+${escSuffix}$`,
            escPrefix, escSuffix, noOfDigits,
            escPrefix, escSuffix,

            // Only prefix
            prefix, `^${escPrefix}[0-9]+$`,
            escPrefix, noOfDigits,
            escPrefix,

            // Only suffix
            suffix, `^[0-9]+${escSuffix}$`,
            escSuffix, noOfDigits,
            escSuffix,

            // Only number
            `^[0-9]+$`, noOfDigits
        ]))
        .whereNotNull('User_Defined_EmpId')
        // Add initial filtering to reduce the number of rows processed
        .where(function() {
            if (prefix && suffix) {
                this.whereRaw(`User_Defined_EmpId REGEXP ?`, [`^${escPrefix}[0-9]+${escSuffix}$`]);
            } else if (prefix) {
                this.whereRaw(`User_Defined_EmpId REGEXP ?`, [`^${escPrefix}[0-9]+$`]);
            } else if (suffix) {
                this.whereRaw(`User_Defined_EmpId REGEXP ?`, [`^[0-9]+${escSuffix}$`]);
            } else {
                this.whereRaw(`User_Defined_EmpId REGEXP ?`, [`^[0-9]{${noOfDigits}}$`]);
            }
        })
        .first();
    const maxNumber = result ? result.max_number || 0 : 0;

    if (maxNumber > 0 && nextNumber <= maxNumber) {
        validationError['IVE0604'] = `The next number must be greater than ${maxNumber}. Please use at least ${maxNumber + 1}.`;
        return validationError;
    }

    return validationError;
    } catch (error) {
        console.log('Error in validateEmployeeIdPrefixInputs function', error);
        throw error;
    }
}

module.exports.addUpdateEmployeeIdPrefixSettings = async (_, args, context) => {
    let organizationDbConnection;
    let errResult;
    let validationError = {};
    try {

        let loginEmployeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);

        // Check access rights
        let checkRights = await commonLib.func.checkEmployeeAccessRights(
            organizationDbConnection,
            loginEmployeeId,
            formName.employeeIdPrefix,
            '',
            'UI',
            false,
            args.formId
        );
        if (Object.keys(checkRights).length > 0 &&
            ((args.empPrefixSettingId === 0 && checkRights.Role_Add === 1) ||
             (args.empPrefixSettingId > 0 && checkRights.Role_Update === 1)) &&
            checkRights.Employee_Role && checkRights.Employee_Role.toLowerCase() === 'admin') {
            // Define field validations
            const fieldValidations = {
                prefix: 'IVE0598',
                suffix: 'IVE0599',
                noOfDigits: 'IVE0600',
                nextNumber: 'IVE0601',
                status: 'IVE0595'
            };

            // Validate the input fields
            validationError = await validateCommonRuleInput(args, fieldValidations);
            if (Object.keys(validationError).length > 0) {
                throw 'IVE0000';
            }

            // Then, if status is Active, validate business rules (duplicates, etc.)
            if (args.status === 'Active') {
                const businessValidationErrors = await validateEmployeeIdPrefixInputs(args, organizationDbConnection);
                if (Object.keys(businessValidationErrors).length > 0) {
                    validationError = {...businessValidationErrors};
                    throw 'IVE0000';
                }
            }

            if (Object.keys(validationError).length === 0) {
                const currentTimestamp = moment().utc().format('YYYY-MM-DD HH:mm:ss');

                const empPrefixData = {
                    Service_Provider_Id: args.serviceProviderId || null,
                    Prefix: args.prefix || null,
                    Suffix: args.suffix || null,
                    No_Of_Digits: args.noOfDigits,
                    Next_Number: args.nextNumber,
                    Status: args.status
                };

                // Set up system log parameters
                let systemLogParams = {
                    userIp: context.User_Ip,
                    employeeId: loginEmployeeId,
                    organizationDbConnection,
                    message: args.empPrefixSettingId == 0
                        ? 'Employee ID prefix setting added successfully'
                        : 'Employee ID prefix setting updated successfully'
                };

                return (
                    await organizationDbConnection.transaction(async (trx) => {
                        if (args.empPrefixSettingId === 0) {
                            // Insert new record
                            empPrefixData.Added_By = loginEmployeeId;
                            empPrefixData.Added_On = currentTimestamp;

                            await organizationDbConnection(ehrTables.empPrefixSettings)
                                .insert(empPrefixData)
                                .transacting(trx);

                            // Log system activity
                            await commonLib.func.createSystemLogActivities(systemLogParams);

                            return {
                                errorCode: '',
                                message: 'Employee ID prefix setting added successfully.'
                            };
                        } else {
                            // Update existing record
                            empPrefixData.Updated_By = loginEmployeeId;
                            empPrefixData.Updated_On = currentTimestamp;

                            const updateResult = await organizationDbConnection(ehrTables.empPrefixSettings)
                                .where('Emp_Prefix_Setting_Id', args.empPrefixSettingId)
                                .update(empPrefixData)
                                .transacting(trx);

                            if (!updateResult) {
                                throw 'EMP0001';
                            }

                            // Log system activity
                            await commonLib.func.createSystemLogActivities(systemLogParams);

                            return {
                                errorCode: '',
                                message: 'Employee ID prefix setting updated successfully.'
                            };
                        }
                    })
                );
            } else {
                // Return validation errors
                throw 'IVE0000';
            }
        } else {
            if (!checkRights || !checkRights.Employee_Role || checkRights.Employee_Role.toLowerCase() !== 'admin') {

                throw ('_DB0109');
            } else {

              if(args.empPrefixSettingId === 0){
                throw ('_DB0101');
              }else{
                throw ('_DB0102');
              }
            }
        }
    } catch (error) {
        console.log('Error in addUpdateEmployeeIdPrefixSettings function', error);
       if (error === 'IVE0000') {
         errResult = commonLib.func.getError('', 'IVE0000')
         console.log(
           'Validation error in addUpdateEmployeeIdPrefixSettings function - ',
           validationError
         )
         throw new UserInputError(errResult.message, {
           validationError: validationError
         })
       }
       else {
         errResult = commonLib.func.getError(error, 'EMP0002')
         throw new ApolloError(errResult.message, errResult.code)
       }
     }
     finally {
       organizationDbConnection ? organizationDbConnection.destroy() : null
     }
};
