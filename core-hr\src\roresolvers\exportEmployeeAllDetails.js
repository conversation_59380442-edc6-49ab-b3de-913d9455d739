// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../common/tablealias');
//Require apollo server to return error message
const { ApolloError, UserInputError } = require('apollo-server-lambda');
const moment = require('moment');

module.exports.exportEmployeeAllDetails = async (parent, args, context, info) => {

    let organizationDbConnection, validationError = {};
    try {
        console.log("Inside exportEmployeeAllDetails function.")
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, context.Employee_Id, null, '', 'UI', false, args.formId);
        
        if (Object.keys(checkRights).length === 0 || checkRights.Role_View !== 1) {
            throw ('_DB0100');
        }
        
        if(!args.filterBy || args.filterBy.length === 0){
            validationError['IVE0001'] =  'The filterBy is required and cannot be empty.';
        }

        if(args.toDate && args.fromDate && moment(args.fromDate).isAfter(moment(args.toDate))){
            validationError['IVE0003'] =  'Invalid date range. The selected From date must be earlier than or equal to the To date.';
        }

        if (Object.keys(validationError).length > 0) 
            throw 'IVE0000';

        args.typeOfEmployeeDetails = args.typeOfEmployeeDetails && args.typeOfEmployeeDetails.length ? args.typeOfEmployeeDetails : ["All"];
        args.typeOfEmployeeDetails =  args.typeOfEmployeeDetails.map(function(item) { return item.toLowerCase();});

        let employeeDetails = {};

        getPersonalInfo(organizationDbConnection, args, employeeDetails);
        getJobInfo(organizationDbConnection, args, employeeDetails);
        getCareerInfo(organizationDbConnection, args, employeeDetails);
        getOtherInfo(organizationDbConnection, args, employeeDetails);
        getContactInfo(organizationDbConnection, args, employeeDetails);
        getDocumentInfo(organizationDbConnection, args, employeeDetails);
        getAirTicketInfo(organizationDbConnection, args, employeeDetails);
        getCustomeAdditionalInfo(organizationDbConnection, args, employeeDetails);
        getSunfishInfo(organizationDbConnection, args, employeeDetails);
        
         // Execute all queries in parallel
        const results = await Promise.all(
            Object.values(employeeDetails).map(query => query.catch(error => ({  error }))) // Catch individual errors
        );

        // Map results back to keys
        const response = await Promise.all(Object.keys(employeeDetails).map(async (key, index) => {
            if(results[index].error){
                throw results[index].error;
            }
            if(key.toLowerCase() === 'jobinfodetails'){
                return { key, value: await getPositionMasterDetails(results[index], organizationDbConnection) };
            }
            return { key, value: results[index] };
            
        })).then(data => {
            // Combine all results into a single object
            const employeeAllResults = data.reduce((acc, { key, value }) => {
                acc[key] = value;
                return acc;
            }, {});

            const allEmpty = Object.values(employeeAllResults).every(value => 
                value === null || value === undefined || (Array.isArray(value) && value.length === 0) || (typeof value === 'object' && Object.keys(value).length === 0)
            );

            return allEmpty ? {} : employeeAllResults;

        }).catch(error => {
            console.error('Error occurred exportEmployeeAllDetails catch block ', error);
            throw error; 
        });

        if(Object.keys(response).length === 0){
            return { errorCode: "_EC0001", message: "No records found. Please adjust the filter criteria.", exportEmployeeDetails: null};
        }

        return { errorCode: "", message: "Export employee details retrieved successfully.", exportEmployeeDetails: JSON.stringify(response) };
    } catch (error) {
        console.error('Error in exportEmployeeAllDetails main catch() block ', error);
        let errResult = commonLib.func.getError(error, 'EDM0101');
        if (error === 'IVE0000') {
            throw new UserInputError(errResult.message, { validationError });
        }else{
            throw new ApolloError(errResult.message, errResult.code);
        }
    } finally {
        organizationDbConnection ? organizationDbConnection.destroy() : null;
    }
}


function getPersonalInfo (organizationDbConnection, args, employeeDetails) {
    
    try {
        let entities = args.typeOfEmployeeDetails;
       
        if (entities.includes("all") || entities.includes("personal")) {
            let empPersonalInfoQuery = organizationDbConnection(ehrTables.empPersonalInfo + " as EMPPI")
                .select("EMPPI.*", "EJ.User_Defined_EmpId", "EJ.External_EmpId", "EJ.Pf_PolicyNo", "MS.Marital_Status as Marital_Status_Name", 
                organizationDbConnection.raw(`GROUP_CONCAT(L.Language_Name) as Languages`), organizationDbConnection.raw("CONCAT_WS(' ',EMPPI.Emp_First_Name,EMPPI.Emp_Middle_Name,EMPPI.Emp_Last_Name) as Employee_Name"), 
                organizationDbConnection.raw('CASE EMPPI.Is_Manager WHEN 1 THEN "Yes" ELSE "No" END as Is_Manager'), 
                organizationDbConnection.raw('CASE EMPPI.Smoker WHEN 1 THEN "Yes" ELSE "No" END as Smoker'), 
                organizationDbConnection.raw('CASE EMPPI.Military_Service WHEN 1 THEN "Yes" ELSE "No" END as Military_Service'), 
                organizationDbConnection.raw('CASE EMPPI.Physically_Challenged WHEN 1 THEN "Yes" ELSE "No" END as Physically_Challenged'), 
                organizationDbConnection.raw('CASE EMPPI.Is_Illiterate WHEN 1 THEN "Yes" ELSE "No" END as Is_Illiterate'), 
                organizationDbConnection.raw('CASE EMPPI.Enable_Sign_In_With_Mobile_No WHEN 1 THEN "Yes" ELSE "No" END as Enable_Sign_In_With_Mobile_No'), 
                organizationDbConnection.raw('CASE EMPPI.Allow_User_Signin WHEN 1 THEN "Yes" ELSE "No" END as Allow_User_Signin'))
                .leftJoin(ehrTables.empLanguages + " as EL", "EL.Employee_Id", "EMPPI.Employee_Id")
                .leftJoin(ehrTables.languages + " as L", "L.Lang_Id", "EL.Lang_Known")
                .leftJoin(ehrTables.maritalStatus + " as MS", "MS.Marital_Status_Id", "EMPPI.Marital_Status")
                .innerJoin(ehrTables.empJob + " as EJ", "EJ.Employee_Id", 'EMPPI.Employee_Id')
                .groupBy("EMPPI.Employee_Id");
            employeeDetails.personalInfoDetails = commonWhereFunction(empPersonalInfoQuery, null, args, organizationDbConnection);
        }

        if (entities.includes("all") || entities.includes("license")) {
            
            let empDrivingLicenseQuery = organizationDbConnection(ehrTables.empDrivingLicense + " as DL").select("DL.*", "C.Country_Name", "EJ.User_Defined_EmpId")
                .leftJoin(ehrTables.country + " as C", "C.Country_Code", "DL.Issuing_Country");
            employeeDetails.drivingLicenseDetails = commonWhereFunction(empDrivingLicenseQuery, "DL", args, organizationDbConnection);
        }

        if (entities.includes("all") || entities.includes("dependent")) {
            let empDependentQuery = organizationDbConnection(ehrTables.empDependent + ' as ED').select("ED.*");
            employeeDetails.dependentDetails = commonWhereFunction(empDependentQuery, "ED", args, organizationDbConnection);
        }

        if (entities.includes("all") || entities.includes("passport")) {
            let empPassportQuery = organizationDbConnection(ehrTables.empPassport + " as EP").select("EP.*", "C.Country_Name")
            .leftJoin(ehrTables.country + " as C", "C.Country_Code", "EP.Issuing_Country");
            employeeDetails.passportDetails = commonWhereFunction(empPassportQuery, "EP", args, organizationDbConnection);
        }
    } catch (error) {
        throw error;
    }

}

function getJobInfo(organizationDbConnection, args, employeeDetails) {

    try {
        let entities = args.typeOfEmployeeDetails;

        if (entities.includes("all") || entities.includes("job")) {
            
            let empJobQuery = organizationDbConnection(ehrTables.empJob + " as EJ").select("EJ.*","ER.ESIC_Reason as Relieving_Reason","R.Roles_Name", "SP.Service_Provider_Code", "SP.Service_Provider_Name", "BU.Business_Unit as Business_Unit_Name", "EO.Field_Force",
             "DES.Designation_Name", "DES.Designation_Code", "DEP.Department_Code", "DEP.Department_Name", "L.Location_Code", "L.Location_Name", "WS.WorkSchedule_Code as Work_Schedule_Code", "WS.Title as Work_Schedule_Name", "ET.Employee_Type", "EMP.Profession_Name", organizationDbConnection.raw('CONCAT_WS(" ",EP.Emp_First_Name, EP.Emp_Middle_Name, EP.Emp_Last_Name) as Manager_Name'), "EJ2.User_Defined_EmpId as Manager_User_Defined_EmpId",
             "OG.Organization_Group_Code","OG.Organization_Group", 'TKP.Timekeeping_Name', 'CER.Career_Name','BEA.Branch_Email_Address', 'SFWPOS.Pos_Code as Position_Code', 'SFWPOS.Job_Title_Code', 'SFWPOS.Lst_Grade_Code', 'SFWPOS.Global_Grade',
            'SFWPOS.Cost_Code as Cost_Center_Code', 'SFWPOS.Department_Code as Organization_Unit_Code', 'JF.Job_Family_Level_Code', 'JF.Job_Family_Code', 'JF.Job_Family_Grade_Code', 'SFWPOS.Parent_Path', 'ET.Employee_Type_Code',
            organizationDbConnection.raw('CONCAT_WS(" ",SECOND_MANAGER.Emp_First_Name, SECOND_MANAGER.Emp_Middle_Name, SECOND_MANAGER.Emp_Last_Name) as Second_Manager_Name'), "EJ3.User_Defined_EmpId as Second_Manager_User_Defined_EmpId", "SIL.Candidate_Id",
            organizationDbConnection.raw("CONCAT_WS(' ',EMPPI.Emp_First_Name,EMPPI.Emp_Middle_Name,EMPPI.Emp_Last_Name) as Employee_Name"), 
            organizationDbConnection.raw("CASE WHEN SFWPOS.Cost_Code IS NOT NULL THEN SFWPOS.Cost_Code ELSE BU.Business_Unit END as Business_Unit"), "BU.Business_Unit_Code")
            .innerJoin(ehrTables.empPersonalInfo + " as EMPPI", "EMPPI.Employee_Id", "EJ.Employee_Id")
            .leftJoin(ehrTables.roles + " as R", "R.Roles_Id", "EJ.Roles_Id")
            .leftJoin(ehrTables.designation + " as DES", "DES.Designation_Id", "EJ.Designation_Id")
            .leftJoin(ehrTables.department + " as DEP", "DEP.Department_Id", "EJ.Department_Id")
            .leftJoin(ehrTables.location + " as L", "L.Location_Id", "EJ.Location_Id")
            .leftJoin(ehrTables.employeeType + " as ET", "ET.EmpType_Id", "EJ.EmpType_Id")
            .leftJoin(ehrTables.workSchedule + " as WS", "WS.WorkSchedule_Id", "EJ.Work_Schedule")
            .leftJoin(ehrTables.empPersonalInfo + " as EP", "EP.Employee_Id", "EJ.Manager_Id")
            .leftJoin(ehrTables.empProfession + " as EMP", "EMP.Profession_Id", "EJ.Emp_Profession")
            .leftJoin(ehrTables.serviceProvider + " as SP", "SP.Service_Provider_Id", "EJ.Service_Provider_Id")
            .leftJoin(ehrTables.businessUnit + " as BU", "BU.Business_Unit_Id", "EJ.Business_Unit_Id")
            .leftJoin(ehrTables.esicReason + " as ER", "ER.Reason_Id", "EJ.Reason_Id")
            .leftJoin(ehrTables.organizationGroup + " as OG", "OG.Organization_Group_Id", "EJ.Organization_Group_Id")
            .leftJoin(ehrTables.careerPIC + ' as CER', 'CER.Career_Id', 'EJ.Career_Id')
            .leftJoin(ehrTables.timekeepingPIC + ' as TKP', 'TKP.Timekeeping_Id', 'EJ.Timekeeping_Id')
            .leftJoin(ehrTables.branchEmailAddress + ' as BEA', 'BEA.Location_Id', 'L.Location_Code')
            .leftJoin(ehrTables.SFWPOrganizationStructure + ' as SFWPOS', 'SFWPOS.Pos_Code', 'DES.Designation_Code')
            .leftJoin(ehrTables.sfwpJobFamily + ' as JF', 'JF.Job_Title_Code', 'SFWPOS.Job_Title_Code')
            .leftJoin(ehrTables.empJob + " as EJ2", "EJ2.Employee_Id", "EJ.Manager_Id")
            .leftJoin(ehrTables.empPersonalInfo + " as SECOND_MANAGER", "SECOND_MANAGER.Employee_Id", "EJ2.Manager_Id")
            .leftJoin(ehrTables.empJob + " as EJ3", "EJ3.Employee_Id", "SECOND_MANAGER.Employee_Id")
            .leftJoin(ehrTables.externalApiIntegrationLog + " as SIL", "SIL.Employee_Id", "EMPPI.Employee_Id")
            .innerJoin(ehrTables.orgDetails + " as EO");
            
            employeeDetails.jobInfoDetails = commonWhereFunction(empJobQuery, null, args, organizationDbConnection);
        }

        if (entities.includes("all") || entities.includes("experience")) {

           let empExperienceQuery = organizationDbConnection(ehrTables.empExperience + " as EE")
                .select("EE.*", "EED.File_Name", "EED.File_Size")
                .leftJoin(ehrTables.empExperienceDocuments + " as EED", "EED.Experience_Id", "EE.Experience_Id");
            employeeDetails.experienceDetails = commonWhereFunction(empExperienceQuery, "EE",  args, organizationDbConnection);       
        }

        if (entities.includes("all") || entities.includes("assets")) {
            let empAssetsQuery = organizationDbConnection(ehrTables.empAssets + ' as EA').select('EA.*');
            employeeDetails.assetDetails = commonWhereFunction(empAssetsQuery, "EA",  args, organizationDbConnection);
        }
    } catch (error) {
        throw error;
    }
}


function getContactInfo(organizationDbConnection, args, employeeDetails){

    try {
        let entities = args.typeOfEmployeeDetails;

        if (entities.includes("all") || entities.includes("contact")) {
            
            let contactDetailsQuery = organizationDbConnection(ehrTables.contactDetails + " as CD")
            .select("CD.*", "EJ.User_Defined_EmpId",  organizationDbConnection.raw("CONCAT_WS(' ',EMPPI.Emp_First_Name,EMPPI.Emp_Middle_Name,EMPPI.Emp_Last_Name) as Employee_Name"),
            "LO.Location_Name", "LO.Location_Code", "LO.Location_Type", "LO.Street1", "LO.Street2", "LO.Pincode", "LO.Phone", "LO.Fax", "LO.Currency_Symbol",  "CI.City_Name", "ST.State_Name", "CO1.Country_Name as Country_Name", 
            "CO2.Country_Name as pCountry_Name","CO3.Country_Name as cCountry_Name", "CO4.Country_Name as oCountry_Name")
            .innerJoin(ehrTables.empJob + " as EJ", "EJ.Employee_Id", "CD.Employee_Id")
            .innerJoin(ehrTables.empPersonalInfo + " as EMPPI", "EMPPI.Employee_Id", "EJ.Employee_Id")
            .leftJoin(ehrTables.location + " as LO", "LO.Location_Id", "EJ.Location_Id")
            .leftJoin(ehrTables.city + " as CI", "CI.City_Id", "LO.City_Id")
            .leftJoin(ehrTables.state + " as ST", "ST.State_Id", "LO.State_Id")
            .leftJoin(ehrTables.country + " as CO1", "CO1.Country_Code", "LO.Country_Code")
            .leftJoin(ehrTables.country + " as CO2", "CO2.Country_Code", "CD.pCountry")
            .leftJoin(ehrTables.country + " as CO3", "CO3.Country_Code", "CD.cCountry")
            .leftJoin(ehrTables.country + " as CO4", "CO4.Country_Code", "CD.oCountry");

            employeeDetails.contactInfoDetails = commonWhereFunction(contactDetailsQuery, null,  args, organizationDbConnection);
        }
    } catch (error) {
        throw error;
    }
}


function getCareerInfo(organizationDbConnection, args, employeeDetails) {

    try {
        let entities = args.typeOfEmployeeDetails;

        if (entities.includes("all") || entities.includes("education")) {
            let empEducationQuery = organizationDbConnection(ehrTables.empEducation + " as EE")
                    .select("EE.Employee_Id", "EE.University", "EE.Year_Of_Start", "EE.Year_Of_Passing", "EE.Percentage", "EE.Grade", 
                        "EE.Start_Date", "EE.End_Date", "EE.City", "EE.State",  "EE.Country", "CD.Course_Name",
                        organizationDbConnection.raw(`CASE WHEN ES.Specialization IS NOT NULL AND ES.Specialization!="" 
                            THEN ES.Specialization ELSE EE.Specialisation END AS Specialization_Name`), 'ES.Specialization_Code',
                        organizationDbConnection.raw(`CASE WHEN EI.Institution IS NOT NULL AND EI.Institution!="" 
                            THEN EI.Institution ELSE EE.Institute_Name END AS Institute_Name`), 'EI.Institution_Code')
                    .leftJoin(ehrTables.courseDetails + " as CD", "CD.Course_Id", "EE.Education_Type")
                    .leftJoin(ehrTables.eduSpecialization + " as ES", "ES.Specialization_Id", "EE.Specialization_Id")
                    .leftJoin(ehrTables.eduInstitution + " as EI", "EI.Institution_Id", "EE.Institution_Id");
            employeeDetails.educationalInfoDetails = commonWhereFunction(empEducationQuery,  "EE",  args, organizationDbConnection);       
        }

        if (entities.includes("all") || entities.includes("certifications")) {
            let empCertificationsQuery =organizationDbConnection(ehrTables.empCertifications + " as EC").select("EC.*");
            employeeDetails.certificateInfoDetails = commonWhereFunction(empCertificationsQuery, "EC", args, organizationDbConnection);
        }

        if (entities.includes("all") || entities.includes("training")) {
            let empTrainingQuery = organizationDbConnection(ehrTables.empTraining + " as ET").select("ET.*"); 
            employeeDetails.trainingInfoDetails = commonWhereFunction(empTrainingQuery, "ET", args, organizationDbConnection);   
        }

        if (entities.includes("all") || entities.includes("awards")) {
            let empAwardsQuery = organizationDbConnection(ehrTables.empAwards + " as EA").select("EA.*");
            employeeDetails.awardDetails = commonWhereFunction(empAwardsQuery, "EA", args, organizationDbConnection);
        }

        if (entities.includes("all") || entities.includes("skills")) {
            let empSkillsQuery = organizationDbConnection(ehrTables.empSkills + " as ES").select("ES.*");
            employeeDetails.skillDetails = commonWhereFunction(empSkillsQuery, "ES", args, organizationDbConnection);     
        }   
    } catch (error) {
        throw error;
    }

}


function getOtherInfo(organizationDbConnection, args, employeeDetails) {
    
    try {
        let entities = args.typeOfEmployeeDetails;

        if (entities.includes("all") || entities.includes("bank")) {
           let empBankDetailsQuery = organizationDbConnection(ehrTables.empBankDetails + " as EBD")
                .select("EBD.*", "AT.Account_Type", "BD.Bank_Name as bankName")
                .leftJoin(ehrTables.accountType + " as AT", "AT.Account_Type_Id", "EBD.Account_Type_Id")
                .leftJoin(ehrTables.bankDetails + " as BD", "BD.Bank_Id", "EBD.Emp_Bank_Id");

            employeeDetails.bankDetails = commonWhereFunction(empBankDetailsQuery, "EBD", args, organizationDbConnection);
        }

        if (entities.includes("all") || entities.includes("insurance")) {
            let empInsuranceQuery = organizationDbConnection(ehrTables.empInsurance + " as EI")
                .select("EI.*", "IT.Insurance_Name")
                .leftJoin(ehrTables.insuranceType + " as IT", "IT.InsuranceType_Id", "EI.InsuranceType_Id");
            employeeDetails.insuranceDetails = commonWhereFunction(empInsuranceQuery, "EI", args, organizationDbConnection);
        }
    } catch (error) {
        throw error;
    }  
}

function getDocumentInfo(organizationDbConnection, args, employeeDetails) {
    
    try {
        let entities = args.typeOfEmployeeDetails;

        if (entities.includes("all") || entities.includes("professional license")) {
            let empAccreditationQuery = organizationDbConnection(ehrTables.employeeAccreditationDetails + " as EAD")
                    .select("EAD.Received_Date", "EAD.Expiry_Date", "EAD.Verified", "EAD.Verified_Date", "EAD.Identifier", "EAD.Last_Reminded_Date",  "EAD.Exam_Rating", "EAD.Exam_Date_Year", "EAD.Exam_Date_Month",
                        "ACT.Accreditation_Category", "ACT.Accreditation_Type", organizationDbConnection.raw('CONCAT_WS(" ",EP.Emp_First_Name, EP.Emp_Middle_Name, EP.Emp_Last_Name) as Verified_By'))
                    .leftJoin(ehrTables.accreditationCategoryAndType + " as ACT", "ACT.Accreditation_Category_And_Type_Id", "EAD.Accreditation_Category_And_Type_Id")
                    .leftJoin(ehrTables.empPersonalInfo + " as EP", "EP.Employee_Id", "EAD.Verified_By");
            employeeDetails.accreditationDetails = commonWhereFunction(empAccreditationQuery, "EAD", args, organizationDbConnection);        
        }     

        if (entities.includes("all") || entities.includes("documents")) {
            let empDocumentQuery = organizationDbConnection(ehrTables.empDocumentCategory + " as EDC")
            .select( 'EDC.Document_Name', 'DST.Document_Sub_Type', 'DT.Document_Type', 'DT.Document_Type_Id', 'DC.Category_Fields')
            .leftJoin(ehrTables.documentSubType + ' as DST', 'DST.Document_Sub_Type_Id', 'EDC.Document_Sub_Type_Id')
            .leftJoin(ehrTables.documentType + ' as DT', 'DT.Document_Type_Id', 'DST.Document_Type_Id')
            .leftJoin(ehrTables.documentCategory + ' as DC', 'DC.Category_Id', 'DT.Category_Id')
            .whereNot('EDC.Document_Sub_Type_Id', 0).whereNotNull('EDC.Document_Sub_Type_Id');
            employeeDetails.documentDetails = commonWhereFunction(empDocumentQuery, "EDC", args, organizationDbConnection);
        }
    } catch (error) {
        throw error;
    }
}

function getAirTicketInfo(organizationDbConnection, args, employeeDetails) {

    try {

        let entities = args.typeOfEmployeeDetails;
        if (entities.includes("all") || entities.includes("air ticket policy")) {
            let empAirTicketPolcyQuery = organizationDbConnection(ehrTables.empAirTicketPolicy + " as EAP").select("EAP.*", "ATS.*")
            .innerJoin(ehrTables.airTicketSettings + ' as ATS', 'ATS.Air_Ticket_Setting_Id', 'EAP.Air_Ticket_Setting_Id')
          
            employeeDetails.airTiketPolicy = commonWhereFunction(empAirTicketPolcyQuery, "EAP", args, organizationDbConnection);
        }

        if (entities.includes("all") || entities.includes("air ticket settlement summary")) {
            let airTicketSettlementSummaryQuery = organizationDbConnection(ehrTables.airTicketSettlementSummary + " as ATSS")
            .select("ATSS.*", organizationDbConnection.raw("ATSS.No_Of_Dependents + 1 as No_Of_Dependents") );
            employeeDetails.airTiketSettlementSummary = commonWhereFunction(airTicketSettlementSummaryQuery, "ATSS", args, organizationDbConnection);
        }

    } catch (error) {
        throw error;
    }
}

function getCustomeAdditionalInfo(organizationDbConnection, args, employeeDetails) {

    try {

        let entities = args.typeOfEmployeeDetails;
        if (entities.includes("all") || entities.includes("custom fields")) {
            let empAirTicketPolcyQuery = organizationDbConnection(ehrTables.teamSummaryCustomFieldValues + " as TSCF ")
            .select("TSCF.*","EJ.User_Defined_EmpId", organizationDbConnection.raw("CONCAT_WS(' ',EMPPI.Emp_First_Name,EMPPI.Emp_Middle_Name,EMPPI.Emp_Last_Name) as Employee_Name"))
            .innerJoin(ehrTables.empJob + " as EJ", "EJ.Employee_Id", 'TSCF.Primary_Id')
            .innerJoin(ehrTables.empPersonalInfo + " as EMPPI", "EMPPI.Employee_Id", "EJ.Employee_Id")

            employeeDetails.customAdditional = commonWhereFunction(empAirTicketPolcyQuery, null, args, organizationDbConnection);
        }


    } catch (error) {
        throw error;
    }
}

function getSunfishInfo(organizationDbConnection, args, employeeDetails) {
    
    try {
        let entities = args.typeOfEmployeeDetails;

        if (entities.includes("all") || entities.includes("data sync status")) {
            let empSunfishQuery = organizationDbConnection(ehrTables.externalApiIntegrationLog + " as SIL")
            .select("SIL.*", "EJ2.User_Defined_EmpId as Updated_By_User_Defined_EmpId", organizationDbConnection.raw("CONCAT_WS(' ',EMP2.Emp_First_Name, EMP2.Emp_Middle_Name, EMP2.Emp_Last_Name) as Updated_By_Employee_Name"))
            .leftJoin(ehrTables.empPersonalInfo + " as EMP2", "EMP2.Employee_Id", "SIL.Updated_By")
            .leftJoin(ehrTables.empJob + " as EJ2", "EJ2.Employee_Id", "EMP2.Employee_Id");
            employeeDetails.sunfishIntegrationDetails = commonWhereFunction(empSunfishQuery, "SIL", args, organizationDbConnection);        
        } 
    } catch (error) {
        throw error;
    }    
}



function commonWhereFunction(query, alias, args, organizationDbConnection) {

    try {

        if(alias){
            query.innerJoin(ehrTables.empJob + " as EJ", "EJ.Employee_Id", alias+'.Employee_Id')
            .innerJoin(ehrTables.empPersonalInfo + " as EMPPI", "EMPPI.Employee_Id", "EJ.Employee_Id");
            query.select("EJ.User_Defined_EmpId", organizationDbConnection.raw("CONCAT_WS(' ',EMPPI.Emp_First_Name,EMPPI.Emp_Middle_Name,EMPPI.Emp_Last_Name) as Employee_Name"))
        }
        
       
        // Whitelist of valid columns for sorting
        const validSortColumns = ['User_Defined_EmpId', 'Employee_Id', 'Employee_Name'];

        let sortby = validSortColumns.includes(args.sortBy) ? args.sortBy === 'Employee_Name' ? 'Employee_Name' : 'EJ.'+args.sortBy : 'EJ.User_Defined_EmpId';
        query = query.orderBy(sortby, 'asc')

        if(args.fromDate && args.toDate)
            query.whereBetween('EJ.Date_Of_Join', [args.fromDate, args.toDate])

        if(args.limit)
            query.limit(args.limit).offset(args.offset || 0);

        const filters = {
            'Active Employees': () => query.where('EJ.Emp_Status', 'Active'),
            'Inactive Employees': () => query.where('EJ.Emp_Status', 'InActive'),
        };

        //It applies filters to the query based on the filterBy parameter in args, which can be one of ActiveEmployees, InactiveEmployees, or DateOfJoinEmployees
        if(args.filterBy && args.filterBy.length){
            args.filterBy.forEach(filter => {
                if(filters[filter]){
                    filters[filter]();
                }
            });
        }

        const filterEmpJobFields = [
            { key: 'location', column: 'EJ.Location_Id' },
            { key: 'designation', column: 'EJ.Designation_Id' },
            { key: 'department', column: 'EJ.Department_Id' }
        ];

        // It applies additional filters to the query based on the location, designation, and department parameters in args, if they are provided.
        filterEmpJobFields.forEach(field => {
            if (args[field.key] && args[field.key].length) {
                query.whereIn(field.column, args[field.key]);
            }
        });

        return query;
    } catch (error) {
        throw error;
    }
    
}
  

async function getPositionMasterDetails(jobInfo, organizationDbConnection) {

    try{
        if(jobInfo && jobInfo.length){
            const parentStructureDetails = await organizationDbConnection(ehrTables.SFWPOrganizationStructure)
            .select('Pos_Code', 'Org_Level', 'Pos_Name', 'Originalpos_Id').whereIn('Originalpos_Id', jobInfo.flatMap(item => item.Parent_Path?.split(',') || []));

            const candidateIds = jobInfo.map(item => item.Candidate_Id).filter(id => id !== null && id !== undefined); 
            const employeeSalaryDetails = await organizationDbConnection('emp_generated_documents').select('Additional_Details', 'Candidate_Id').whereNotNull('Additional_Details')
            .whereIn('Candidate_Id', candidateIds).andWhere('Additional_Details', 'like', '%basicPay%');
            
            jobInfo = jobInfo.map(item => {
                if(item.Parent_Path){
                    const parentStructure = parentStructureDetails.filter(grp => item.Parent_Path.split(',').includes(grp.Originalpos_Id));
                    item.Pos_Group_Name = parentStructure.find(grp => grp.Org_Level === 'GRP')?.Pos_Name || '';
                    item.Pos_Group_Code = parentStructure.find(grp => grp.Org_Level === 'GRP')?.Pos_Code || '';
                    item.Pos_Division_Name = parentStructure.find(div => div.Org_Level === 'DIV')?.Pos_Name || '';
                    item.Pos_Division_Code = parentStructure.find(div => div.Org_Level === 'DIV')?.Pos_Code || '';
                    item.Pos_Section_Name = parentStructure.find(sec => sec.Org_Level === 'SEC')?.Pos_Name || '';
                    item.Pos_Section_Code = parentStructure.find(sec => sec.Org_Level === 'SEC')?.Pos_Code || '';
                    item.Department_Name = parentStructure.find(dept => dept.Org_Level === 'DEPT')?.Pos_Name || item.Department_Name;
                    item.Department_Code = parentStructure.find(dept => dept.Org_Level === 'DEPT')?.Pos_Code || item.Department_Code;
                }

                if(item.Candidate_Id){
                    const salaryDetails = employeeSalaryDetails.find(grp => item.Candidate_Id === grp.Candidate_Id);
                    if(salaryDetails && salaryDetails.Additional_Details){
                        let additional = JSON.parse(salaryDetails.Additional_Details);
                        if(typeof additional === 'string'){
                            additional = JSON.parse(additional)
                        }
                        item.Salary = additional.basicPay || '';
                    }
                }
                return item;
            });
        }
        return jobInfo;
    } catch(error){
        console.error("Error in getPositionMasterDetails function", error);
        throw error;
    }
   
}
