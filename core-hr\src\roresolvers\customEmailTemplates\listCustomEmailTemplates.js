// Required dependencies
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib
const knex = require('knex')
const { ehrTables } = require('../../../common/tablealias')
const { ApolloError } = require('apollo-server-lambda')
const { formIds } = require('../../../common/appconstants')

module.exports.listCustomEmailTemplates = async (
  parent,
  args,
  context,
  info
) => {
  let organizationDbConnection

  try {
    console.log('Inside listCustomEmailTemplates function.')

    // Initialize the database connection
    organizationDbConnection = knex(context.connection.OrganizationDb)
    let loginEmployeeId = context.Employee_Id;
    let formId = args.formId ? args.formId : formIds.customEmailTemplate;
    let checkRights = await commonLib.func.checkEmployeeAccessRights(
      organizationDbConnection,
      loginEmployeeId,
      null,
      '',
      'UI',
      false,
      formId
    )
    if (Object.keys(checkRights).length > 0 && checkRights.Role_View === 1) {
      // Query to fetch email templates with category and form details
      let result = await organizationDbConnection(
        ehrTables.customEmailTemplates + ' as CET'
      )
        .select(
          'CET.Template_Id',
          'CET.Template_Name',
          'CET.Template_Content',
          'CET.Template_Fields',
          'CET.Subject_Content',
          'CET.Subject_Fields',
          'CET.Category_Id',
          'CTC.Category_Name',
          'CET.Form_Id',
          'CET.Visibility',
          'CET.To_Emails',
          'CTCT.Category_Type_Name',
          'CET.Category_Type_Id',
          'CET.CC_Emails',
          'CET.Bcc_Emails',
          'CET.Attachments',
          'CET.Default_Template',
          'CET.Additional_Emails',
          'CET.External_Emails',
          'CET.Sender_Name',
          'CET.Added_On',
          'CET.Added_By',
          'CET.Updated_On',
          'CET.Updated_By',
          'EF.Form_Name',
          organizationDbConnection.raw("CONCAT_WS(' ',emp.Emp_First_Name,emp.Emp_Middle_Name, emp.Emp_Last_Name) as Added_By_Name"),
          organizationDbConnection.raw("CONCAT_WS(' ',emp1.Emp_First_Name,emp1.Emp_Middle_Name, emp1.Emp_Last_Name) as Updated_By_Name"),
          'CTC.Category_Name',
          'EF.Form_Name',
          'CET.Form_Id',
        )
        .innerJoin(
          ehrTables.customTemplateForms + ' as CTF',
          'CTF.Form_Id',
          'CET.Form_Id'
        )
        .leftJoin(
          ehrTables.empPersonalInfo + ' as emp',
          'emp.Employee_Id',
          'CET.Added_By'
        )
        .leftJoin(
          ehrTables.empPersonalInfo + ' as emp1',
          'emp1.Employee_Id',
          'CET.Updated_By'
        )
        .leftJoin(
          ehrTables.customTemplateCategory + ' as CTC',
          'CTC.Category_Id',
          'CET.Category_Id'
        )
        .leftJoin(
            ehrTables.customTemplateCategoryType + ' as CTCT',
            'CTCT.Category_Type_Id',
            'CET.Category_Type_Id'
          )
        .leftJoin(ehrTables.ehrForms + ' as EF', 'EF.Form_Id', 'CET.Form_Id')
        .modify(function (queryBuilder) {
          if (args.formId) {
            queryBuilder.where('CET.Form_Id', args.formId)
          }
          if (args.templateId) {
            queryBuilder.where('CET.Template_Id', args.templateId)
          }
          if (args.categoryId) {
            queryBuilder.where('CET.Category_Id', args.categoryId)
          }
          if (args.categoryId && args.categoryTypeId) {
            queryBuilder.where('CTCT.Category_Type_Id', args.categoryTypeId)
          }

        })
        if(!result){
          throw 'CET00001'
        }
        if(result.length <0){
          return {
            errorCode: null,
            message: 'Custom email templates retrieved successfully',
            emailTemplates: result
          }
        }
        result = await Promise.all(
          result.map(async (data) => {
            let attachmentLinks = [];
            if (data.Attachments) {
              try {
                const attachments = JSON.parse(data.Attachments);
                if (Array.isArray(attachments)) {
                  attachmentLinks = await Promise.all(
                    attachments.map(async (fileName) => {
                      if (!fileName || typeof fileName !== 'string') return null;
                      try {
                        const formedFileName = `${process.env.domainName}/${context.Org_Code}/Email Template Document Upload/${fileName}`;
                        let encodedFileName = encodeURIComponent(formedFileName);
                        const path = await commonLib.func.getSignedUrl(encodedFileName, process.env.documentsBucketCloudFront);
                        return path;
                      } catch (error) {
                        return null;
                      }
                    })
                  );
                  attachmentLinks = attachmentLinks.filter(link => link !== null);
                }
              } catch (error) {
                attachmentLinks = [];
              }
            }

            if (!data.Additional_Emails) {
              return {
                ...data,
                Attachments: JSON.stringify(attachmentLinks),
                AttachmentsFileNames: data.Attachments
              };
            }
            const additionalEmailIdArray = await attachEmailsToAdditionalEmails(
                data.Additional_Emails,
                organizationDbConnection
              );
              return {
                ...data,
                Additional_Emails: additionalEmailIdArray,
                Attachments: JSON.stringify(attachmentLinks),
                AttachmentsFileNames: data.Attachments
              };

          })
        );

      return {
        errorCode: null,
        message: 'Custom email templates retrieved successfully',
        emailTemplates: result
      }
    } else {
      console.log('Employee do not have view access rights')
      throw '_DB0100'
    }
  } catch (e) {
    if (organizationDbConnection) {
      organizationDbConnection.destroy()
    }
    console.log(
      'Error in listCustomEmailTemplates function main catch block.',
      e
    )
    const errResult = commonLib.func.getError(e, 'CET00001')
    throw new ApolloError(errResult.message, errResult.code)
  }
}

async function attachEmailsToAdditionalEmails(additionalEmails, organizationDbConnection) {
  try {
    const additionalEmailIdArray = JSON.parse(additionalEmails);

    if (!Array.isArray(additionalEmailIdArray) || additionalEmailIdArray.length === 0) {
     return "[]"
    }
    const initiatorQuery = await organizationDbConnection
      .select('Employee_Id', 'Emp_Email')
      .from('emp_job')
      .andWhereNot('Emp_Email', '')
      .andWhereNot('Emp_Email', null)
      .whereIn('Employee_Id', additionalEmailIdArray);

      const emailMap = initiatorQuery.reduce((map, row) => {
        map[row.Employee_Id] = row.Emp_Email;
        return map;
      }, {});
      const updatedEmails = additionalEmailIdArray
        .map((id) => ({
          id,
          email: emailMap[id] || null,
        }))
        .filter((entry) => entry.email);

        return JSON.stringify(updatedEmails);

  } catch (error) {
    console.error("Error attaching emails:", error);
    throw error;
  }
}

