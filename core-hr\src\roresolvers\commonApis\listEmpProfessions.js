// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');

let organizationDbConnection;
module.exports.listEmpProfession = async (parent, args, context, info) => {
    try {
        console.log("Inside listEmpProfession function.")
        organizationDbConnection = knex(context.connection.OrganizationDb);
        return (
             organizationDbConnection(ehrTables.empProfession)
                .select("*")
                .then((data) => {
                    //destroy the connection
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    return { errorCode: "", message: "Employee profession retrieved successfully.", professions: data };
                })
                .catch((err) => {
                    console.log('Error in listEmpProfession .catch() block', err);
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    let errResult = commonLib.func.getError(err, 'CCH0103');
                    throw new ApolloError(errResult.message, errResult.code);
                })
        )
    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in listEmpProfession function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'CCH0003');
        throw new ApolloError(errResult.message, errResult.code);
    }
}
