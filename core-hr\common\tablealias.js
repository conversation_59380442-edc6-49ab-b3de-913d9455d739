let ehrTables = {
    workSchedule: 'work_schedule',
    workscheduleWeekoff: 'workschedule_weekoff',
    weekdays: 'weekdays',
    timezone: 'timezone',
    empPersonalInfo: 'emp_personal_info',
    empJob: 'emp_job',
    shiftType: 'shift_type',
    empShiftType: 'emp_shift_type',
    employeeMonitorSettings: 'employee_monitor_settings',
    empAttendance: 'emp_attendance',
    shiftEmpMapping: 'shift_emp_mapping',
    leaveSettings: 'leave_settings',
    empLeaves: 'emp_leaves',
    leaveType: 'leave_types',
    workflows: 'workflows',
    shiftEmpMapping: 'shift_emp_mapping',
    customGroupRefreshStatus: 'custom_group_refresh_status',
    cusEmpGroup: 'custom_employee_group',
    workflowModule: 'workflow_module',
    unitData: 'unit_data',
    esicReason: 'esic_reason',
    holidayCustomGroup: 'holiday_customgroup_assignment',
    holiday: 'holiday',
    customGroupAssociated: 'custom_group_associated_forms',
    compensatoryOff: 'compensatory_off',
    shortTimeOff: 'short_time_off',
    holidaySettings: 'holiday_settings',
    customGroupEmployees: 'custom_employee_group_employees',
    notificationEngineConfiguration: 'notification_engine_configuration',
    notificationTriggerTime: 'notification_trigger_time',
    notificationEngineGroup: 'notification_engine_group',
    notificationEngineEventMaster: 'notification_engine_event_master',
    attendanceSettings: 'attendance_settings',
    salaryPayslip: 'salary_payslip',
    bwdSalaryPayslip: 'bwd_salary_payslip',
    projectDetails: 'project_details',
    empProject: 'emp_project',
    location: 'location',
    projectSettings: 'project_settings',
    projectActivities: 'project_activities',
    assignment: 'assignment',
    auditAssignment: 'audit_assignment',
    empTimesheet: 'emp_timesheet',
    timesheetHoursTracking: 'timesheet_hours_tracking',
    projectAccreditationCategoryTypeMapping: "project_accreditation_category_type_mapping",
    designation: 'designation',
    empDesignationHistory: 'emp_designation_history',
    empGrade: 'emp_grade',
    ehrRoles: 'ehr_roles',
    designationAccreditationCategoryTypeMapping: 'designation_accreditation_category_type_mapping',
    holidayAssignment: 'holiday_assignment',
    orgDetails: 'org_details',
    assetManagement: 'asset_management',
    employeeLevelInsightsNotificationSettings: 'employee_level_insights_notification_settings',
    weekoffDates: 'weekoff_dates',
    employeeActivityDetails: 'employee_activity_details',
    empEligibleLeave: 'emp_eligible_leave',
    encashedLeaves: 'encashed_leaves',
    taUserTask: 'ta_user_task',
    taUserTaskHistory: 'ta_user_task_history',
    empResignation: 'emp_resignation',
    auditEmpEligibleLeave: 'audit_emp_eligible_leave',
    empDependent: 'emp_dependent',
    payrollRoundOffSettings: 'payroll_round_off_settings',
    leaveQuarter: 'leave_quarter',
    leaveJoinQuarter: 'leave_join_quarter',
    empServiceLeave: 'emp_service_leave',
    maternitySlab: 'maternity_slab',
    empExperienceLeave: 'emp_experience_leave',
    leavetypeGrade: 'leavetype_grade',
    contactDetails: 'contact_details',
    empUser: 'emp_user',
    employeeDataImport: 'employee_data_import',
    candidateJob: 'candidate_job',
    employeeLog: 'employee_info_timestamp_log',
    empJobAuditHistory: 'emp_job_audit_history',
    employeeType: 'employee_type',
    salaryDetails: 'salary_details',
    employeeInfoTimestampLog: 'employee_info_timestamp_log',
    department: 'department',
    country: 'country',
    empInsurancePolicyNo: 'emp_insurancepolicyno',
    insuranceType: 'insurance_type',
    serviceProvider: 'service_provider',
    skillLevels: 'skill_levels',
    labourWelfareFundDesignation: 'labour_welfare_fund_designation',
    expenseTypeGrade: 'expense_type_grade',
    shortTimeOffSettings: 'short_time_off_settings',
    onDutySettings: 'on_duty_settings',
    preApprovalSettings: 'pre_approval_settings',
    preApprovalRequests: 'pre_approval_requests',
    holidaySettings: 'holiday_settings',
    languages: 'languages',
    empProfession: 'emp_profession',
    courseDetails: 'course_details',
    documentCategory: 'document_category',
    documentType: 'document_type',
    documentSubType: 'document_sub_type',
    accreditationCategoryType: 'accreditation_category_and_type',
    bankDetails: 'bank_details',
    rosterManagementSettings: 'roster_management_settings',
    formLevelCoverage: 'form_level_coverage',
    compoffConfiguration: 'compoff_configuration',
    specialWages: 'specialwage_configuration',
    accountType: 'account_type',
    insuranceTypeGrade: 'insurancetype_grade',
    insuranceGrade: 'insurance_grade',
    fixedHealthInsurance: 'fixed_health_insurance',
    fixedHealthInsuranceType: 'fixed_health_insurance_type',
    businessUnit: 'business_unit',
    specialWages: 'specialwage_configuration',
    overtimeConfiguration: 'overtime_configuration',
    preApprovalDocuments: 'pre_approval_documents',
    compensatoryOffBalance: 'compensatory_off_balance',
    dataSetupDashboard: 'datasetup_dashboard',
    lopRecoverySettings: "lop_recovery_settings",
    activitiesMaster: 'activities_master',
    activityDetailsBytime: "activity_details_bytime",
    timesheetHoursTracking: "timesheet_hours_tracking",
    empBankDetails: 'emp_bankdetails',
    activityDetailsBytimeArchive: "activity_details_bytime_archive",
    empTimesheetArchive: "emp_timesheet_archive",
    timesheetHoursTrackingArchive: "timesheet_hours_tracking_archive",
    timesheetHoursTracking: "timesheet_hours_tracking",
    organizationGroup: "organization_group",
    city: 'city',
    state: 'state',
    currency: 'currency',
    room: 'room',
    roomArchive: 'room_archive',
    orgpolicyLocDept: 'org_policy_multi_loc_dept',
    recruitment: 'recruitment',
    allowances: 'allowances',
    empTransfer: 'emp_transfer',
    auditSalary: 'audit_salary',
    jobPostLocation: 'job_post_location',
    candidateUrl: 'candidate_url',
    archiveEmpAttendance: 'archive_emp_attendance',
    archiveEmpAttendanceImport: 'archive_emp_attendance_import',
    auditHourlyWages: 'audit_hourly_wages',
    backupHolidayAssignment: 'backup_holiday_assignment',
    branchEmailAddress: 'branch_email_address',
    employeeHistoryDetails: 'employee_history_details',
    employeeSalaryDetails: 'employee_salary_details',
    hourlyWages: 'hourly_wages',
    invitedVendors: 'invited_vendors',
    ipAddressWhitelisting: 'ip_address_whitelisting',
    orgPolicyMultiLocDept: 'org_policy_multi_loc_dept',
    professionalTax: 'professional_tax',
    whitelistedIpAddresses: 'whitelisted_ip_addresses',
    compoffConfigurationHistory: 'compoff_configuration_history',
    empLanguages: 'emp_language',
    empDrivingLicense: 'emp_drivinglicense',
    empPassport: 'emp_passport',
    maritalStatus: 'marital_status',
    roles: 'roles',
    empExperience: 'emp_experience',
    empExperienceDocuments: 'emp_experience_documents',
    empAssets: 'emp_assets',
    empEducation: 'emp_education',
    empCertifications: 'emp_certifications',
    empTraining: 'emp_training',
    empSkills: 'emp_skillset',
    empAwards: 'emp_awards',
    empInsurance: 'emp_insurancepolicyno',
    accreditationCategoryAndType: 'accreditation_category_and_type',
    employeeAccreditationDetails: 'employee_accreditation_details',
    empDocumentCategory: 'emp_document_category',
    empDocuments: 'emp_documents',
    externalApiIntegrationLog: 'external_api_integration_log',
    externalApiSyncDetails: 'external_api_sync_details',
    eduSpecialization: 'edu_specialization',
    eduInstitution: 'edu_institution',
    taxDetails: 'tax_details',
    careerPIC: 'career_pic',
    timekeepingPIC: 'timekeeping_pic',
    SFWPOrganizationStructure: 'SFWP_Organization_Structure',
    sfwpJobFamily: 'SFWP_job_family',
    customFields: 'custom_fields',
    customFieldAssociatedForms: 'custom_field_associated_forms',
    ehrForms: 'ehr_forms',
    inputValidations: 'input_validations',
    customFieldTables: 'custom_field_tables',
    customEmailTemplates: 'custom_email_templates',
    customTemplateCategory: 'custom_template_category',
    customTemplateCategoryType: 'custom_template_category_type',
    customTemplateForms: 'custom_template_forms',
    empAirTicketPolicy: 'emp_air_ticket_policy',
    airTicketSettings: 'air_ticket_settings',
    airTicketSettlementSummary: 'air_ticket_settlement_summary',
    payrollGeneralSettings: 'payroll_general_settings',
    employeeTravelSetting: 'employee_travel_setting',
    teamSummaryCustomFieldValues: 'team_summary_custom_field_values',
    empdependent: 'emp_dependent',
    empPrefixSettings: 'emp_prefix_settings',
    empPrefixConfig: 'emp_prefix_config',
    employeeSalaryConfiguration: 'employee_salary_configuration',
    payslipTemplate: 'payslip_template',
    monthlyPayslip: 'salary_payslip'
};
let appManagerTables = {
    notificationEngineManager: 'notification_engine_manager',
    leaveClosureManager: 'leave_closure_manager',
    systemProcessManager: 'system_process_manager',
    orgRateChoice: 'org_rate_choice',
    orgRateChoiceAuthenticationMapping: 'org_rate_choice_authentication_mapping',
    orgAuthenticationMethods: 'org_authentication_methods',
    hrappRegisteruser: 'hrapp_registeruser',
    billingRate: 'billing_rate',
    hrappPlanDetails: 'hrapp_plan_details',
}

/** Alias name for ehr tables */
module.exports = {
    ehrTables,
    appManagerTables
}