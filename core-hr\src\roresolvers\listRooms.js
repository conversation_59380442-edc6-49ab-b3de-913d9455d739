// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formIds } = require('../../common/appconstants');

//function to list the rooms
let organizationDbConnection;
module.exports.listRooms = async (parent, args, context, info) => {
    try {
        console.log("Inside listRooms function.")
        let employeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);

        // check their rights
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, employeeId, null, 'Role_View', 'Back-end', null, formIds.room);
        if (!checkRights) {
            throw '_DB0100';
        }

        //Retrieve room details
        let data = await organizationDbConnection(ehrTables.room + " as RO")
            .select("RO.*", organizationDbConnection.raw("CONCAT_WS(' ',EPI.Emp_First_Name,EPI.Emp_Middle_Name, EPI.Emp_Last_Name) as Added_By_Name"), organizationDbConnection.raw("CONCAT_WS(' ',EPI2.Emp_First_Name,EPI2.Emp_Middle_Name, EPI2.Emp_Last_Name) as Updated_By_Name"))
            .leftJoin(ehrTables.empPersonalInfo + " as EPI", "EPI.Employee_Id", "RO.Added_By")
            .leftJoin(ehrTables.empPersonalInfo + " as EPI2", "EPI2.Employee_Id", "RO.Updated_By")

        return { errorCode: "", message: "Room details retrieved successfully.", rooms: data };

    }
    catch (e) {
        console.log('Error in listRooms function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'CHR00106');
        throw new ApolloError(errResult.message, errResult.code);
    }
    finally {
        //Destroy DB connection
        if (organizationDbConnection) {
            organizationDbConnection.destroy();
        }
    }
}
