const { ApolloError } = require('apollo-server-lambda');
const { systemLogs, formIds } = require('../../../common/appconstants');
const { ehrTables } = require('../../../common/tablealias');
const moment = require('moment');
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
module.exports.addTimeSheetPrevWeek = async (parent, args, context) => {
    let organizationDbConnection;
    try {
        const loginEmployee_Id = context.Employee_Id;
        const formId = args.selfService === 1 ? formIds.timeSheet : formIds.timeSheetMyTeam;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployee_Id, '', '', 'UI', false, formId);
        if(args.selfService === 0 && Object.keys(checkRights).length >= 0 && checkRights.Is_Manager !== 1 && checkRights.Employee_Role.toLowerCase() !== 'admin'){
            throw '_DB0114'
        }
        if(Object.keys(checkRights).length <= 0 || (checkRights.Role_Add === 0)){
            
           throw '_DB0101';
           
            
          }
        const { prevWeekEndingDate, employeeId, weekEndingDate,timesheetType} = args;
        let empTimesheetData = {
            "Employee_Id": employeeId,
            "Week_Ending_Date": weekEndingDate
        };
        return(
        await organizationDbConnection.transaction(async (trx) => {
          
           
                 let existingMainRecord=await organizationDbConnection(ehrTables.empTimesheet)
                .transacting(trx)
                .where("Week_Ending_Date",weekEndingDate).whereIn("Approval_Status", ["Applied", "Draft", 'Approved','Returned'])
                .where('Employee_Id',employeeId);
                if(existingMainRecord && existingMainRecord.length > 0){
                    throw 'CHR0129'
                }
                else{
                    empTimesheetData.Approval_Status = 'Draft'; 
                    let newRequestReturnedId=
                    await organizationDbConnection(ehrTables.empTimesheet)
                    .transacting(trx)
                    .insert(empTimesheetData).catch((err)=>{
                        console.log("Error in addTimeSheetPrevWeek function .catch block.",err);
                        throw err;
                    });
                    if(!newRequestReturnedId || !newRequestReturnedId[0]){
                       throw 'CHR0130'
                    }
                    let newRequestId=newRequestReturnedId[0];
                    let timesheetPrevRecordData=await getTimesheetData(organizationDbConnection,trx,prevWeekEndingDate,employeeId);
                    if(!timesheetPrevRecordData || !timesheetPrevRecordData.length){
                        throw 'CHR0131'
                    }
                    timesheetPrevRecordData=timesheetPrevRecordData.map(item=>({...item,Request_Id:newRequestId,Added_Date:moment().utc().format('YYYY-MM-DD HH:mm:ss'),Added_By:loginEmployee_Id,Timesheet_Type:timesheetType}));
                    await organizationDbConnection(ehrTables.timesheetHoursTracking)
                    .transacting(trx)
                    .insert(timesheetPrevRecordData).catch((err)=>{
                        console.log("Error in addTimeSheetPrevWeek function .catch block.",err);
                        throw err;
                    })

            }

        })
            .then(async(result) => {
                const systemLogParams = {
                    action: systemLogs.roleAdd,
                    userIp: context.User_Ip,
                        employeeId: loginEmployee_Id,
                    organizationDbConnection: organizationDbConnection,
                    message: ` The timesheet from the previous week of ${prevWeekEndingDate} has been added to current week successfully`,
            
                };
                await commonLib.func.createSystemLogActivities(systemLogParams);
                organizationDbConnection ? organizationDbConnection.destroy() : null;
            return { errorCode: "", message: "timesheet activity data details from previous week added successfully." };
            }).catch((error) => {
                console.log("Error in addTimeSheetPrevWeek function .catch block.",error);
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                    //Destroy DB connection
                                  throw error;
            }));
        
    } catch (error) {
        console.log("Error in addTimeSheetPrevWeek function main catch block.", error);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
            let errorCode = error.code === 'ER_DUP_ENTRY'?'CHR0090': error
            errResult = commonLib.func.getError(errorCode, 'CHR00105');
            throw new ApolloError(errResult.message, errResult.code);
    }
};
 
async function getTimesheetData(organizationDbConnection,trx,prevWeekEndingDate,employeeId){
    try{
    return(
    await organizationDbConnection(ehrTables.timesheetHoursTracking + " as THR")
    .transacting(trx)
    .leftJoin(ehrTables.empTimesheet + " as ETS", "ETS.Request_Id", "THR.Request_Id")
    .where("ETS.Week_Ending_Date",prevWeekEndingDate).whereIn("ETS.Approval_Status", ["Applied", "Draft", 'Approved','Returned'])
    .where('ETS.Employee_Id',employeeId).select('THR.Project_Id','THR.Project_Activity_Id').then((data)=>{
        return data;
                  
                }).catch((err)=>{
                    throw err;
                })
    )
            }
            catch(error){
                throw error;
            }
}


