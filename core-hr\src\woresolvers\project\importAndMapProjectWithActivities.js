// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formName, formIds, systemLogs } = require('../../../common/appconstants');
const { validateCommonRuleInput } = require('../../../common/inputValidations');
const moment = require('moment-timezone');
const formatMinMaxDate = (date) => moment(date).format('YYYY-MM-DD');

module.exports.importAndMapProjectWithActivities = async (parent, args, context, info) => {
    let organizationDbConnection;
    try {
        console.log("Inside importAndMapProjectWithActivities function.")
        let employeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);

        // check their rights
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, employeeId, null, '', 'UI', false, formIds.projects);
        if (Object.keys(checkRights).length <= 0 || checkRights.Role_Add !== 1) {
            throw '_DB0101';
        } else {
            let projectAndActivityData = args.projectAndActivityData
            // validating the activities for their from-date and to-date
            const sameDataFails = await validateProjectActivitySameData(organizationDbConnection, projectAndActivityData)
            if (sameDataFails.length) {
                projectAndActivityData = projectAndActivityData.filter((data) => {
                    let existingData = {
                        "projectId": data["projectId"],
                        "activityId": data["activityId"],
                        "isBillable": data["isBillable"],
                        "activityFrom": data["activityFrom"],
                        "activityTo": data["activityTo"]
                    };
                    if(sameDataFails.length){
                        var isSameData = sameDataFails[0].failedArrays.some((failedData) => {
                            const isEqual = areObjectsEqual(failedData, existingData);
                            return isEqual;
                        });
                    }
                    if (!isSameData) {
                        return data;
                    }
                });
            }
            if (projectAndActivityData.length) {
                let existingActivityData = await checkSameActivityData(organizationDbConnection, projectAndActivityData);
                const projectActivityIds = existingActivityData.map(obj => obj.projectActivityId);
                
                var modifiedData = projectAndActivityData.map(activity => {
                    const {projectId,activityId, isBillable, activityFrom, activityTo} = activity;
                    return {
                        Project_Id: projectId,
                        Activity_Id: activityId,
                        Is_Billable:isBillable,
                        Activity_From: activityFrom,
                        Activity_To: activityTo
                    };
                });
                modifiedData = modifiedData.map(activity => ({
                    ...activity,
                    Added_Date: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                    Added_By: employeeId
                }));
                
                const queryResult = await organizationDbConnection.transaction(async (trx) => {
                    // Conditionally execute the delete query only if projectActivityIds is not empty
                    if (projectActivityIds.length > 0) {
                        const ifDeleted = await organizationDbConnection(ehrTables.projectActivities)
                            .whereIn('Project_Activity_Id', projectActivityIds)
                            .del()
                            .transacting(trx);
                        if (!ifDeleted) {
                            throw 'CHR0119';
                        }
                    }
                
                    // Insert the data into the table
                    const declarations = await organizationDbConnection(ehrTables.projectActivities)
                        .insert(modifiedData)
                        .transacting(trx);
                    const systemLogParam = {
                        userIp: context.User_Ip,
                        employeeId: employeeId,
                        organizationDbConnection: organizationDbConnection,
                        message: `Project activity was imported ${declarations.toString()}`
                    };
                    await commonLib.func.createSystemLogActivities(systemLogParam);
                
                    return true;
                });                

                if (!queryResult) {
                    throw 'CHR0119';
                }
            }
            // Form the failed data
            let failedData = [...sameDataFails];

            organizationDbConnection ? organizationDbConnection.destroy() : null;
            return { errorCode: "", message: "Project activities was imported successfully", validationError: JSON.stringify(failedData) };
        }

    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in importAndMapProjectWithActivities function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'CHR0088');
        throw new ApolloError(errResult.message, errResult.code);
    }
}

async function validateProjectActivitySameData(organizationDbConnection, projectAndActivityData) {
    try {
        let failedData = [];
        let sameActivityData = await checkSameActivityData(organizationDbConnection, projectAndActivityData);

        // Group objects by 'projectActivityId' and 'projectId' and combine 'Week_Ending_Date' values
        const groupedData = sameActivityData.reduce((acc, curr) => {
            const key = `${curr.projectId}_${curr.projectActivityId}`;
            if (!acc[key]) {
                acc[key] = { 
                    projectActivityId: curr.projectActivityId, 
                    projectId: curr.projectId, 
                    activityId: curr.activityId, 
                    Week_Ending_Date: [] 
                };
            }
            acc[key].Week_Ending_Date.push({ Week_Ending_Date: curr.Week_Ending_Date });
            return acc;
        }, {});

        // Convert the grouped data object into an array of objects
        const result = Object.values(groupedData);

        //If same data already exists
        if (result.length) {
            var validationFailedData;
            // for each activity getting their from-date and to-date.
            // from-date should always be less than current min-date.
            // to-date should always be greater than current max-ate.
            await Promise.all(result.map(async (element) => {
                if(element.Week_Ending_Date && element.Week_Ending_Date.length && element.Week_Ending_Date[0].Week_Ending_Date != null){
                    const resultArray = processActivityData(element.Week_Ending_Date);
                    const minDate = new Date(resultArray[0].minDate);
                    const maxDate = new Date(resultArray[0].maxDate);
                    let data = projectAndActivityData.filter(el => (el.projectId === element.projectId && el.activityId === element.activityId));
                    if (data && data.length){
                        const activityFromDate = new Date(data[0].activityFrom);
                        const activityToDate = new Date(data[0].activityTo);
                        if (activityFromDate > minDate || activityToDate < maxDate){
                            if (!validationFailedData) {
                                validationFailedData = {
                                    Message: 'The given activities could not be added with the project.',
                                    failedArrays: []
                                };
                            }
                            validationFailedData.failedArrays = validationFailedData.failedArrays.concat(data);
                        }
                    }
                }
            }));
            
            if(validationFailedData){
                failedData.push(validationFailedData);
            }
        }
        return failedData

    } catch (err) {
        console.log("Error in validateProjectActivitySameData function", err)
        throw err
    }
}

const checkSameActivityData = async (organizationDbConnection, projectAndActivityData) => {
    try {
        const activityData = await organizationDbConnection(ehrTables.projectActivities + ' as PA')
            .select('PA.Project_Activity_Id as projectActivityId', 'PA.Project_Id as projectId', 'PA.Activity_Id as activityId', 'TH.Week_Ending_Date')
            .leftJoin(ehrTables.timesheetHoursTracking + ' as TH', 'PA.Project_Activity_Id', 'TH.Project_Activity_Id')
            .whereIn(
                ['PA.Project_Id', 'PA.Activity_Id'],
                projectAndActivityData.map(entry => [entry.projectId, entry.activityId])
            );

        return activityData;
    } catch (error) {
        console.error('Error while retrieving and checking activity data:', error);
        throw error; // Rethrow the error to be handled by the caller
    }
};


const processActivityData = (data) => {
    let resultArray = [];
    if (data && data.length) {
        const dates = data.map(obj => moment(obj.Week_Ending_Date));
        let maxDate = moment.max(dates).clone();
        let minDate = moment.min(dates).subtract(6, 'days');
        resultArray = [{ minDate: formatMinMaxDate(minDate), maxDate: formatMinMaxDate(maxDate) }];
    }
    return resultArray;
};

// Function to compare objects
function areObjectsEqual(obj1, obj2) {
    const keys1 = Object.keys(obj1);
    const keys2 = Object.keys(obj2);

    const commonKeys = keys1.filter(key => keys2.includes(key));

    for (let key of commonKeys) {
        if (obj1[key] !== obj2[key]) {
            return false;
        }
    }

    return true;
}
