// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../../common/tablealias');
//Require apollo server to return error message
const { UserInputError, ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formName, systemLogs, formIds } = require('../../../../common/appconstants');
const { insertDataInTable, deleteDataInCustomGroupAssosiateTable } = require('../../../../common/commonfunctions');
const { validateLopRequestsInputs } = require('../../../../common/inputValidations');
const moment = require('moment');
module.exports.addUpdateLopRecoverySettings = async (parent, args, context, info) => {
    let organizationDbConnection;
    let errResult;
    let validationError = {};
    try {
        let loginEmployeeId = context.Employee_Id;
        let formId = formIds.lopRecoverySettings;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        const Lop_Coverage_array = await commonLib.func.getCoverage(organizationDbConnection, formId, ehrTables.formLevelCoverage);
        const Lop_Coverage = Lop_Coverage_array[0].Coverage
        const { Lop_Settings_Id, CustomGroup_Id, Auto_LOP_Applicable, Attendance_Shortage_Applicable, Workflow_Id, Late_Attendance_Applicable, Configuration_Status } = args;
        let systemLogParams = {
            userIp: context.User_Ip,
            employeeId: loginEmployeeId,
            formName: formName.lopRecovery,
            trackingColumn: 'Lop_Settings_Id',
            organizationDbConnection: organizationDbConnection,
            uniqueId: Lop_Settings_Id
        };
        // check their rights
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, '', '', 'UI', false, formId);
        if (Object.keys(checkRights).length > 0 && ((Lop_Settings_Id === 0 && checkRights.Role_Add === 1) || (Lop_Settings_Id > 0 && checkRights.Role_Update === 1))) {

            validationError = await validateLopRequestsInputs(args, Lop_Coverage);
            if (Object.keys(validationError).length == 0) {
                return (
                    await organizationDbConnection
                        .transaction(function (trx) {
                            return (
                                organizationDbConnection(ehrTables.lopRecoverySettings + " as LRS")
                                    .transacting(trx)
                                    .select('LRS.Lop_Settings_Id', 'LRS.Late_Attendance_Applicable', 'FLC.Coverage')
                                    .leftJoin(ehrTables.formLevelCoverage + " as FLC", "FLC.Form_Id", formId)
                                    .where("LRS.Configuration_Status", "Active")
                                    .modify(function (queryBuilder) {


                                        // Conditionally add the 'Lop_Settings_Id' filter
                                        if (Lop_Settings_Id !== 0) {
                                            queryBuilder.whereNot('LRS.Lop_Settings_Id', Lop_Settings_Id);
                                        }
                                        if ((CustomGroup_Id && Lop_Coverage.toLowerCase() === "custom group") || (CustomGroup_Id && Lop_Settings_Id)) {
                                            queryBuilder.leftJoin(ehrTables.customGroupAssociated + " as CGA", "LRS.Lop_Settings_Id", "CGA.Parent_Id");
                                            queryBuilder.where("CGA.Custom_Group_Id", CustomGroup_Id);
                                            queryBuilder.where("CGA.Form_Id", formId);
                                            queryBuilder.select('CGA.Custom_Group_Id');
                                        }
                                    })
                                    .then(async (customGroupSettings) => {

                                        if
                                            (customGroupSettings.length > 0) {
                                            console.log("duplicate record exist")
                                            throw 'SCL0104';
                                        }

                                        else {
                                            let currentDateTime = moment().utc().format('YYYY-MM-DD HH:mm:ss');
                                            let lopRecoverySettingData = {
                                                "Late_Attendance_Applicable": Late_Attendance_Applicable,
                                                "Attendance_Shortage_Applicable": Attendance_Shortage_Applicable,
                                                "Auto_LOP_Applicable": Auto_LOP_Applicable,
                                                "Configuration_Status": Configuration_Status,
                                                "Workflow_Id": Workflow_Id,
                                            }

                                            if (Lop_Settings_Id) {

                                                lopRecoverySettingData.Updated_On = currentDateTime;
                                                lopRecoverySettingData.Updated_By = loginEmployeeId;
                                                return (
                                                    organizationDbConnection(ehrTables.lopRecoverySettings)
                                                        .transacting(trx)
                                                        .where("Lop_Settings_Id", Lop_Settings_Id)
                                                        .update(lopRecoverySettingData)
                                                        .then(async (updateResult) => {

                                                            if (updateResult) {
                                                                if (CustomGroup_Id) {
                                                                    let customGroupData = [{ "Parent_Id": Lop_Settings_Id, "Form_Id": formId, "Custom_Group_Id": CustomGroup_Id }];
                                                                    /**Delete the custom group association */
                                                                    await commonLib.func.deleteDataInCustomGroupAssosiateTable(organizationDbConnection, trx, formId, Lop_Settings_Id)
                                                                    // insert into table
                                                                    await insertDataInTable(organizationDbConnection, trx, customGroupData, ehrTables.customGroupAssociated);
                                                                }
                                                                systemLogParams.action = systemLogs.roleUpdate;

                                                                return true;
                                                            }
                                                            else {
                                                                throw 'SCL0102'
                                                            }
                                                        })
                                                )

                                            } else {
                                                //If it is a add action then update the Added_on and Added_By
                                                lopRecoverySettingData.Added_On = currentDateTime;
                                                lopRecoverySettingData.Added_By = loginEmployeeId;
                                                return (
                                                    organizationDbConnection(ehrTables.lopRecoverySettings)
                                                        .transacting(trx)
                                                        .insert(lopRecoverySettingData)
                                                        .then(async (insertData) => {
                                                            if (insertData && insertData.length) {
                                                                if (CustomGroup_Id) {
                                                                    /** Insert the custom group association */
                                                                    let customGroupData = [{ "Parent_Id": insertData[0], "Form_Id": formId, "Custom_Group_Id": CustomGroup_Id }];
                                                                    insertDataInTable(organizationDbConnection, trx, customGroupData, ehrTables.customGroupAssociated);
                                                                }
                                                                lopRecoverySettingData.Lop_Settings_Id = insertData[0];
                                                                systemLogParams.action = systemLogs.roleAdd;
                                                                return true;
                                                            }
                                                            else {
                                                                throw 'SCL0102'
                                                            }

                                                        })
                                                )
                                            }
                                        }
                                    })
                                    .catch((err) => {
                                        throw err;
                                    })
                            )

                        }
                        ).then(async (data) => {
                            if (data) {
                                await commonLib.func.createSystemLogActivities(systemLogParams);
                                //destroy the connection
                                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                if (Lop_Settings_Id) {
                                    return { errorCode: "", message: "lop recovery setting data details updated successfully." };
                                }
                                return { errorCode: "", message: "lop recovery setting data details added successfully." };
                            }
                            else {
                                throw 'SCL0103'
                            }
                        })
                        .catch((catchError) => {

                            throw catchError;
                        })
                )
            }
            else {
                throw 'IVE0000';
            }


        }

        else {
            if (Lop_Settings_Id) {
                console.log("The employee does not have edit access.");
                throw '_DB0102';
            } else {
                console.log("The employee does not have add access.");
                throw '_DB0101';
            }
        }
    }
    catch (e) {
        console.log('Error in addUpdateLopRecoverySettings  function main catch block.', e);
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        //If validation error exist
        if (e === 'IVE0000') {
            console.log('Validation error in the addUpdateLopRecoverySettings  function', validationError);
            errResult = commonLib.func.getError('', 'IVE0000');
            throw new UserInputError(errResult.message, { validationError: validationError });//Return error response
        }
        else {
            errResult = commonLib.func.getError(e, 'SCL0002');
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
}