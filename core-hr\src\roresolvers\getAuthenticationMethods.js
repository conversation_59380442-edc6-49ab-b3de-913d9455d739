// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { appManagerTables } = require('../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');


let appManagerDbConnection;
module.exports.getAuthenticationMethods = async (parent, args, context, info) => {
    try {
        console.log("Inside getAuthenticationMethods function.")
        // variable declaration
        let orgCode = context.Org_Code;
        let planStatus = ['Active', 'NonComplaint']
        //Get app manager db connection
        appManagerDbConnection = knex(context.connection.AppManagerDb);
        return (
            appManagerDbConnection(appManagerTables.orgRateChoice + ' as ORC')
                .select('OAM.*')
                .innerJoin(appManagerTables.orgRateChoiceAuthenticationMapping + " as OCAM", "OCAM.Org_Plan_Id", "ORC.Org_Plan_Id")
                .innerJoin(appManagerTables.orgAuthenticationMethods + " as OAM", "OAM.Authentication_Method_Id", "OCAM.Authentication_Method_Id")
                .whereIn('ORC.Plan_Status', planStatus)
                .where('ORC.Org_Code', orgCode)
                .then((data) => {
                    if(data && data.length){
                        //Converting it to unique rows
                        const uniqueAuthenticationMethods = [...new Set(data.map(JSON.stringify))].map(JSON.parse);
                        //Destroy DB connection
                        appManagerDbConnection ? appManagerDbConnection.destroy() : null;
                        return { errorCode: "", message: "Authentication methods retrieved successfully.", authenticationMethods: uniqueAuthenticationMethods };
                    }else{
                        throw 'BB0122'
                    }
                })
                .catch((err) => {
                    console.log('Error in getAuthenticationMethods .catch() block', err);
                    appManagerDbConnection ? appManagerDbConnection.destroy() : null;
                    let errResult = commonLib.func.getError(err, 'BB0122');
                    throw new ApolloError(errResult.message, errResult.code);
                })
        )
    }
    catch (e) {
        //Destroy DB connection
        appManagerDbConnection ? appManagerDbConnection.destroy() : null;
        console.log('Error in getAuthenticationMethods function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'BB0014');
        throw new ApolloError(errResult.message, errResult.code);
    }
}
