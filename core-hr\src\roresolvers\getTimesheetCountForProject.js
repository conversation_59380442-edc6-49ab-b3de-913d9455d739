// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const {formIds } = require('../../common/appconstants');
const moment = require('moment');

let organizationDbConnection;
module.exports.getTimesheetCountForProject = async (parent, args, context, info) => {
    console.log('Inside getTimesheetCountForProject function');
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let loginEmployeeId = context.Employee_Id;
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, null, '', 'UI', false, formIds.projects);
            if (Object.keys(checkRights).length > 0 && checkRights.Role_View === 1) {
                return(
                    await organizationDbConnection(ehrTables.timesheetHoursTracking + " as TSHT")
                    .leftJoin(ehrTables.empTimesheet + " as ET", "ET.Request_Id", "TSHT.Request_Id")
                    .count('ET.Approval_Status as timesheetCount')
                    .where('TSHT.Project_Id', args.projectId)
                    .where('ET.Approval_Status', 'Approved')
                    .then(async (data) => {
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        return { errorCode: "", message: "Timesheet count details for project has been retrieved successfully.", timesheetCount: data && data.length ? data[0].timesheetCount: 0};
                    })
                    .catch((catchError) => {
                        let errResult = commonLib.func.getError(catchError, 'CHR0117');
                        //Destroy DB connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        throw new ApolloError(errResult.message, errResult.code);
                    })
                )
            }
            else {
                console.log('Employee do not have view access rights');
                throw '_DB0100';
            }
    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in getTimesheetCountForProject function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'CHR0085');
        throw new ApolloError(errResult.message, errResult.code)
       
    }
}
