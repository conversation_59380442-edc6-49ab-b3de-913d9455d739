// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');

let organizationDbConnection;
module.exports.listBankDetails = async (parent, args, context, info) => {
    try {
        console.log("Inside listBankDetails function.")
        organizationDbConnection = knex(context.connection.OrganizationDb);
        return (
            organizationDbConnection(ehrTables.bankDetails + " as BD")
                .select("*")
                .innerJoin(ehrTables.payrollGeneralSettings + " as PGS", 'PGS.Payroll_Country', 'BD.Country_Code')
                .then((data) => {
                    //destroy the connection
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    return { errorCode: "", message: "Bank details retrieved successfully.", bankDetails: data };
                })
                .catch((err) => {
                    console.log('Error in listBankDetails .catch() block', err);
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    let errResult = commonLib.func.getError(err, 'CCH0109');
                    throw new ApolloError(errResult.message, errResult.code);
                })
        )
    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in listBankDetails function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'CCH0009');
        throw new ApolloError(errResult.message, errResult.code);
    }
}
