//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib
//Require knex to make DB connection
const knex = require('knex')
//Require constants
const { ehrTables } = require('../../../common/tablealias')
const { formIds } = require('../../../common/appconstants')
const { ApolloError } = require('apollo-server-lambda')

module.exports.listOrganizationGroup = async (parent, args, context, info) => {
  let organizationDbConnection
  try {
    console.log('Inside listOrganizationGroup function.')
    let employeeId = context.Employee_Id
    organizationDbConnection = knex(context.connection.OrganizationDb)
    let formId = args.formId ? args.formId : formIds.organizationGroup;
    // check their rights
    let checkRights = await commonLib.func.checkEmployeeAccessRights(
      organizationDbConnection,
      employeeId,
      null,
      '',
      'UI',
      false,
      formId
    )
    if (Object.keys(checkRights).length <= 0 || checkRights.Role_View !== 1) {
      throw '_DB0100'
    } else {
      return organizationDbConnection(ehrTables.organizationGroup + ' as OG')
        .select(
          'OG.Organization_Group_Id as organizationGroupId',
          'OG.Organization_Group_Code as organizationGroupCode',
          'OG.Level as level',
          'OG.Organization_Group as organizationGroup',
          organizationDbConnection.raw(
            "CASE WHEN OG.Organization_Group_Code IS NOT NULL THEN CONCAT(OG.Organization_Group_Code, ' - ', OG.Organization_Group) ELSE OG.Organization_Group END AS organizationGroupFullName"
        ),
          'OG.Status as status',
          'OG.Description as description',
          'OG.Added_On as addedOn',
          'OG.Updated_On as updatedOn',
          organizationDbConnection.raw(
            "CONCAT_WS(' ',EPI.Emp_First_Name, EPI.Emp_Middle_Name, EPI.Emp_Last_Name) as updatedByName"
          ),
          organizationDbConnection.raw(
            "CONCAT_WS(' ',EPI2.Emp_First_Name,EPI2.Emp_Middle_Name, EPI2.Emp_Last_Name) as addedByName"
          )
        )
        .orderBy('OG.Organization_Group_Code', 'desc')
        .leftJoin(
          ehrTables.empPersonalInfo + ' as EPI',
          'EPI.Employee_Id',
          'OG.Updated_By'
        )
        .leftJoin(
          ehrTables.empPersonalInfo + ' as EPI2',
          'EPI2.Employee_Id',
          'OG.Added_By'
        )
        .then((result) => {
          let updatedObj = {}
          if (result && result.length) {
            updatedObj.organizationGroupList = result
            updatedObj.maxOrgCode = result[0].organizationGroupCode
          } else {
            updatedObj.organizationGroupList = []
            updatedObj.maxOrgCode = 0
          }
          //Destroy DB connection
          organizationDbConnection ? organizationDbConnection.destroy() : null
          return {
            errorCode: '',
            message: 'Organization Group list was retrieved successfully',
            organizationGroupObject: updatedObj
          }
        })
        .catch((e) => {
          //Destroy DB connection
          organizationDbConnection ? organizationDbConnection.destroy() : null
          console.log(
            'Error in listOrganizationGroup function .catch block.',
            e
          )
          let errResult = commonLib.func.getError(e, 'CHR0132')
          throw new ApolloError(errResult.message, errResult.code)
        })
    }
  } catch (e) {
    //Destroy DB connection
    organizationDbConnection ? organizationDbConnection.destroy() : null
    console.log('Error in listOrganizationGroup function main catch block.', e)
    let errResult = commonLib.func.getError(e, 'CHR0134')
    throw new ApolloError(errResult.message, errResult.code)
  }
}
