//Require common validation
const commonValidation = require('./commonvalidation');
//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;

//Function to validate the custom group holiday inputs.
async function validateHolidayInputs(args, isAdd, isLocation) {
    try {
        let validationError = {};

        if (!isAdd) {

            Holiday_Assign_Id = args.Holiday_Assign_Id;
            //Validate the holiday assign id
            if (!args.Holiday_Assign_Id) {
                validationError['IVE0268'] = commonLib.func.getError('', 'IVE0268').message;
            } else if (!commonValidation.numberValidation(args.Holiday_Assign_Id)) {
                validationError['IVE0268'] = commonLib.func.getError('', 'IVE0268').message1;
            }

            //Validate the holiday date
            if (!args.Holiday_Date) {
                validationError['IVE0274'] = commonLib.func.getError('', 'IVE0274').message;
            }

            //Validate the old date
            if (!args.Old_Date) {
                validationError['IVE0275'] = commonLib.func.getError('', 'IVE0275').message;
            }

            //Validate the holiday type
            if (!(args.Mandatory || args.Holiday || args.Personal_Choice)) {
                validationError['IVE0272'] = commonLib.func.getError('', 'IVE0272').message;
            }

            //Validate the custom group Id
            if (!isLocation && !args.Custom_Group_Id) {
                validationError['IVE0273'] = commonLib.func.getError('', 'IVE0273').message;
            }

            //Validate the location Id
            if(isLocation && !args.Location_Id){
                validationError['IVE0290'] = commonLib.func.getError('', 'IVE0290').message;
            }

            //validate the description
            if (args.Description) {
                if (!commonValidation.descriptionValidation(args.Description)) {
                    validationError['IVE0062'] = commonLib.func.getError('', 'IVE0062').message1;
                }
                else if (!commonValidation.checkLength(args.Description, 1, 500)) {
                    validationError['IVE0062'] = commonLib.func.getError('', 'IVE0062').message2;
                }
            }
        } else {

            //Validate the holidayData
            if (!args.holidayData && !args.holidayDataLocation) {
                validationError['IVE0269'] = commonLib.func.getError('', 'IVE0269').message;
            }

            let holidayData = []
            if(args.holidayData){
                holidayData = JSON.parse(JSON.stringify(args.holidayData))
            }else{
                holidayData = JSON.parse(JSON.stringify(args.holidayDataLocation))
            }

            for (let i = 0; i < holidayData.length; i++) {
                //Validate the holiday Id
                if (!holidayData[i].Holiday_Id) {
                    validationError['IVE0270'] = commonLib.func.getError('', 'IVE0270').message;
                } else if (!commonValidation.numberValidation(holidayData[i].Holiday_Id)) {
                    validationError['IVE0270'] = commonLib.func.getError('', 'IVE0270').message1;
                }
                //Validate the holiday Date
                if (!(holidayData[i].Start_Date && holidayData[i].End_Date)) {
                    validationError['IVE0271'] = commonLib.func.getError('', 'IVE0271').message;
                }
                //Validate the holiday type
                if (!(holidayData[i].Mandatory || holidayData[i].Holiday || holidayData[i].Personal_Choice)) {
                    validationError['IVE0272'] = commonLib.func.getError('', 'IVE0272').message;
                }
                //Validate the custom group Id
                if(args.holidayData && !holidayData[i].Custom_Group_Id){
                    validationError['IVE0273'] = commonLib.func.getError('', 'IVE0273').message;
                }

                //Validate the location id
                if(args.holidayDataLocation && !holidayData[i].Location_Id){
                    validationError['IVE0290'] = commonLib.func.getError('', 'IVE0290').message;
                }

                //validate the description
                if (holidayData[i].Description) {
                    if (!commonValidation.descriptionValidation(holidayData[i].Description)) {
                        validationError['IVE0062'] = commonLib.func.getError('', 'IVE0062').message1;
                    }
                    else if (!commonValidation.checkLength(holidayData[i].Description, 1, 500)) {
                        validationError['IVE0062'] = commonLib.func.getError('', 'IVE0062').message2;
                    }
                }
            }
        }
        return validationError;
    } catch (err) {
        console.log('Error in the customGroupHolidayValidation() function in the main catch block.', err);
        throw err;
    }
}

module.exports = {
    validateHolidayInputs,
};