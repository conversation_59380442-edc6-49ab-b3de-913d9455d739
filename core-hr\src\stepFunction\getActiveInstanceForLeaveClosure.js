// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//get tablealias
const{appManagerTables}=require("../../common/tablealias")
// Organization database connection
const knex = require('knex');
//Require moment
const moment = require('moment-timezone');
const{defaultValues}=require('../../common/appconstants')
// Function to get the active subscribed instances for leave closure.
module.exports.getActiveInstanceForLeaveClosure = async(event,context) =>{
    console.log('Inside getActiveInstanceForLeaveClosure function',event);
    let appmanagerDbConnection;
    // get input data
    let inputStatus=event.status;
    try{
        let masterTable=appManagerTables.leaveClosureManager;
        let currentDateTime=moment.utc().format('YYYY-MM-DD hh:mm:ss');
        if(inputStatus && (inputStatus.toLowerCase()==='open' || inputStatus.toLowerCase()==='failed'))
        {
            /** We limit the number of execution at a particular time so event will be triggered for executing remaining records.*/
            console.log('Event triggered to process remaining records so move to step2');
            let response={
                nextStep:'Step2',
                input:{'status':inputStatus},
                message:'Event triggered to process next set of instances.'          
            }
            return response;
        }
        else{
            // make database connection
            let databaseConnection=await commonLib.stepFunctions.getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,'',1);
            // check whether data exist or not
            if(Object.keys(databaseConnection).length){
                // form app manager database connection
                appmanagerDbConnection=knex(databaseConnection.AppManagerDb);
                //get all the active instances
                let activeSubscribedUsers= await commonLib.func.getInstancesBasedOnPlanStatus(appmanagerDbConnection,defaultValues.activeStatus);
                //remove inactive instances from master table
                let instanceRemoved=await  commonLib.func.removeAllData(appmanagerDbConnection,masterTable);
                if(!instanceRemoved)
                {
                    console.log('Error while removing the old instances from master table.');
                    appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
                    let response ={
                        nextStep:'End',
                        input:{'status':inputStatus},
                        message:'Error occured while removing the old instances.'
                    };
                    return response;
                }
                let inputData=[];
                for(let i=0;i<activeSubscribedUsers.length;i++)
                {
                    inputParams={
                        Org_Code: activeSubscribedUsers[i],
                        Status: "Open",
                        Load_TimeStamp: currentDateTime
                    }
                    inputData.push(inputParams);  
                }
                console.log("currentDateTime",currentDateTime);
                let insertStatus=await commonLib.func.insertIntoTable(appmanagerDbConnection,masterTable,inputData);
                appmanagerDbConnection?appmanagerDbConnection.destroy():null;
                if(insertStatus)
                {
                    let response={
                        nextStep:'Step2',
                        input:{'status':inputStatus},
                        message:'Event triggered to process next set of instances.'          
                    }
                    return response;  
                }
                else
                {
                    let response ={
                        nextStep:'End',
                        input:{'status':inputStatus},
                        message:'Error Occured while Updating the table.'
                    };
                    return response;
                }
              
            }
            else{
                console.log('Error while creating app manager database connection in step1');
                appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
                let response ={
                    nextStep:'End',
                    input:{'status':inputStatus},
                    message:'Error Occured while Updating the table.'
                };
                return response;
            }
        }
    }
    catch(e)
    {
        console.log("Error in getActiveInstanceForLeaveClosure function main catch block.",e);
        appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
        let response ={
            nextStep:'End',
            input:{'status':inputStatus},
            message:'Error Occured while Updating the table.'
        };
        return response;
    }
}

