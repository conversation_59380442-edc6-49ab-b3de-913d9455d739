// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../common/tablealias');
//Require apollo server to return error message
const { UserInputError, ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formName, formIds, systemLogs } = require('../../../common/appconstants');
//Require validation function
const { numberValidation } = require('../../../common/commonvalidation');

module.exports.deleteProjectDetails = async (parent, args, context, info) => {
    let organizationDbConnection;
    let errResult;
    let validationError = {};
    try {
        console.log("Inside deleteProjectDetails function.");
        let employeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        // check their rights
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, employeeId, formName.projects, '', 'UI');
        if (Object.keys(checkRights).length > 0 && checkRights.Role_Delete === 1) {
            let projectId = args.projectId;
            if(args.projectId){
                if(args.projectId < 1 || !(numberValidation(args.projectId)) ){
                    validationError['IVE0278'] = commonLib.func.getError('', 'IVE0278').message1;
                }
            } else{
                validationError['IVE0278'] = commonLib.func.getError('', 'IVE0278').message;
            }
            
            //Check validation error exist or not
            if (Object.keys(validationError).length === 0) {
                let deleteProject = await organizationDbConnection
                    .transaction(function (trx) {
                        return(
                            organizationDbConnection(ehrTables.projectActivities)
                            .select('Project_Id')
                            .where('Project_Id',projectId)
                            .then((result) => {
                                if(result.length === 0){
                                    return(
                                        organizationDbConnection(ehrTables.assignment)
                                        .select('Project_Id')
                                        .where('Project_Id',projectId)
                                        .then((result) => {
                                            if(result.length === 0){
                                                return(
                                                    organizationDbConnection(ehrTables.auditAssignment)
                                                    .select('Project_Id')
                                                    .where('Project_Id',projectId)
                                                    .then((result) => {
                                                        if(result.length === 0){
                                                            return(
                                                                organizationDbConnection(ehrTables.timesheetHoursTracking)
                                                                .select('Project_Id')
                                                                .where('Project_Id',projectId)
                                                                    .then((result) => {
                                                                        if(result.length === 0){
                                                                            return (
                                                                                organizationDbConnection(ehrTables.projectDetails)
                                                                                .delete()
                                                                                .where('Project_Id', projectId)
                                                                                .transacting(trx)
                                                                                .then((result) => {
                                                                                    console.log(result);
                                                                                    if(result){
                                                                                        //Delete the additional details added for the project
                                                                                        return (
                                                                                            organizationDbConnection(ehrTables.empProject)
                                                                                            .delete()
                                                                                            .where('Project_Id', projectId)
                                                                                            .transacting(trx)
                                                                                            .then(() => {
                                                                                                return (
                                                                                                    organizationDbConnection(ehrTables.customGroupAssociated)
                                                                                                        .delete()
                                                                                                        .where('Parent_Id', projectId)
                                                                                                        .where('Form_Id', formIds.projects)
                                                                                                        .transacting(trx)
                                                                                                        .then(() => {
                                                                                                            return (
                                                                                                                organizationDbConnection(ehrTables.projectAccreditationCategoryTypeMapping)
                                                                                                                    .delete()
                                                                                                                    .where('Project_Id', projectId)
                                                                                                                    .transacting(trx)
                                                                                                                    .then(() => {
                                                                                                                        return 'success';
                                                                                                                    })
                                                                                                            )
                                                                                                        })
                                                                                                )
                                                                                            })
                                                                                        )
                                                                                    } else {
                                                                                        console.log('Project details already deleted');
                                                                                        throw('CHR0024');
                                                                                    }
                                                                                })
                                                                            )
                                                                        } else {
                                                                            console.log("Unable to delete the project as the employee timesheet data exists for the project.");
                                                                            throw('CHR0013');
                                                                        }
                                                                    })
                                                            )
                                                        } else {
                                                            console.log("Unable to delete the project as the audit assignment exists for the project.");
                                                            throw('CHR0012');
                                                        }
                                                    })
                                                )
                                            } else {
                                                console.log("Unable to delete the project as the assignment exists for the project.");
                                                throw('CHR0011');
                                            }
                                        })
                                    )                                           
                                } else {
                                    console.log("Unable to delete the project as the Timesheet activity exists for the project.");
                                    throw('CHR0010');
                                }
                            })
                        )
                    }).then(() => {
                        return 'success';
                    })
                    .catch((e) => {
                        console.log('Error while deleting the project details .catch block', e);
                        throw(commonLib.func.getError(e, 'CHR0008'));
                    })
            
                if(deleteProject === 'success'){
                    //Log message: Delete projects Project_Id - 1
                    let systemLogParams = {
                        action: systemLogs.roleDelete,
                        userIp: context.User_Ip,
                        employeeId: employeeId,
                        formName: formName.projects,
                        trackingColumn: 'Project_Id',
                        organizationDbConnection: organizationDbConnection,
                        uniqueId: projectId
                    };
                    //Call function to add the system log
                    await commonLib.func.createSystemLogActivities(systemLogParams);
                    //destroy the connection
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    return { errorCode: "", message: "Project details deleted successfully."};
                } else {
                    throw('CHR0009');
                }
            } else {
                throw 'IVE0000';
            } 
        }
        else {
            console.log("The employee does not have delete access.");
            throw '_DB0103';
        }

    }
    catch (e) {
        console.log('Error in deleteProjectDetails function main catch block.', e);
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        //If validation error exist
        if (e === 'IVE0000') {
            console.log('Validation error in the deleteWorkScheduleAndWeekOff() function', validationError);
            errResult = commonLib.func.getError('', 'IVE0000');
            throw new UserInputError(errResult.message, { validationError: validationError });//Return error response
        } else {
            errResult = commonLib.func.getError(e, 'CHR0009');
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
}