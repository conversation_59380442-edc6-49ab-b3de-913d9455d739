// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../common/tablealias');
//Require apollo server to return error message
const { UserInputError, ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formName, formIds, systemLogs } = require('../../../common/appconstants');
//Require validation function
const { numberValidation } = require('../../../common/commonvalidation');
const { validateDesignationAssociated, validateDesignationAssociatedToJobRole } = require('../../../common/designationValidation');

module.exports.deleteDesignationDetails = async (parent, args, context, info) => {
    let organizationDbConnection;
    let errResult;
    let validationError = {};
    let desAssociatedForms = '';
    let desAssociatedFormsErrorCode = '';
    let existingRecord = null
    try {
        console.log("Inside deleteDesignationDetails function...");
        let loginEmployeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        const { designationId } = args;

        // check their rights
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, formName.designationOrPosition, '', 'UI');
        if (Object.keys(checkRights).length > 0 && checkRights.Role_Delete === 1) {

            if (designationId) {
                if (designationId < 1 || !(numberValidation(designationId))) {
                    validationError['IVE0280'] = commonLib.func.getError('', 'IVE0280').message1;
                }
            } else {
                validationError['IVE0280'] = commonLib.func.getError('', 'IVE0280').message;
            }

            //Check validation error exist or not
            if (Object.keys(validationError).length === 0) {
                let designationName = await organizationDbConnection(ehrTables.designation)
                    .select('Designation_Name as Name', 'Designation_Code as Old_Code')
                    .where('Designation_Id', designationId)
                    .then((result) => {
                        return result;
                    })
                    .catch((e) => {
                        console.log('Error in getting the designation name in .catch block', e);
                        //destroy the connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        throw (commonLib.func.getError(e, 'CHR0017'));
                    });

                if (designationId && designationName) {
                    existingRecord = designationName
                    desAssociatedForms = await validateDesignationAssociated(organizationDbConnection, designationId, designationName, 'delete');
                    if (desAssociatedForms) {
                        desAssociatedFormsErrorCode = 'CHR0032';
                    }
                    desAssosiateToJobRole =  await validateDesignationAssociatedToJobRole(organizationDbConnection, designationId);
                    if (desAssosiateToJobRole?.isAssociated) {
                       throw 'CHR00116'
                    }
                }

                if (desAssociatedFormsErrorCode) {
                    throw desAssociatedFormsErrorCode;
                } else {
                    return (
                        organizationDbConnection
                            .transaction(function (trx) {
                                return (
                                    organizationDbConnection(ehrTables.designation)
                                        .delete()
                                        .where('Designation_Id', designationId)
                                        .transacting(trx)
                                        .then(async (deleteResult) => {
                                            if (deleteResult) {
                                                return (
                                                    organizationDbConnection(ehrTables.ehrRoles)
                                                        .delete()
                                                        .where('Designation_Id', designationId)
                                                        .transacting(trx)
                                                        .then(async (deleteRoles) => {
                                                            // Log message: Delete designation Designation_Id - 1
                                                            let systemLogParams = {
                                                                action: systemLogs.roleDelete,
                                                                userIp: context.User_Ip,
                                                                employeeId: loginEmployeeId,
                                                                formName: formName.designationOrPosition,
                                                                trackingColumn: 'Designation_Id',
                                                                organizationDbConnection: organizationDbConnection,
                                                                uniqueId: designationId
                                                            };
                                                            //Call function to add the system log
                                                            await commonLib.func.createSystemLogActivities(systemLogParams);
                                                            return true;
                                                        })
                                                )
                                            } else {
                                                console.log('Designation details already deleted.');
                                                throw ('CHR0023');
                                            }
                                        })
                                )
                            }).then(async (data) => {

                                if (data && context.partnerid && context.partnerid.toLowerCase() === 'entomo') {
                                    existingRecord = existingRecord[0]
                                    let inputData = {
                                        'orgCode': context.Org_Code, 'partnerId': context.partnerid, 'inputParams': {
                                            entityId: designationId,
                                            entityType: 'Designation',
                                            functionName: 'entomoSync',
                                            action: 'delete',
                                            formData: existingRecord
                                        }
                                    }
                                    //Trigger Entomo Sync
                                    await commonLib.stepFunctions.triggerStepFunction(process.env.commonStepFunction, 'commonStepFunction', '', inputData);

                                }

                                //destroy the connection
                                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                return { errorCode: "", message: "Designation details deleted successfully." };

                            }).catch((e) => {
                                console.log('Error while deleting the designation details .catch block', e);
                                //Destroy DB connection
                                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                errResult = commonLib.func.getError(e, 'CHR0017');
                                throw new ApolloError(errResult.message, errResult.code);
                            })
                    )
                }
            } else {
                throw 'IVE0000';
            }
        } else {
            console.log("The employee does not have delete access.");
            throw '_DB0103';
        }
    }
    catch (e) {
        console.log('Error in deleteDesignationDetails function main catch block.', e);
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        //If validation error exist
        if (e === 'IVE0000') {
            console.log('Validation error in the deleteDesignationDetails function', validationError);
            errResult = commonLib.func.getError('', 'IVE0000');
            throw new UserInputError(errResult.message, { validationError: validationError });//Return error response
        } else if (e == 'CHR0032') {
            throw new ApolloError('Unable to delete designation as it is associated with ' + desAssociatedForms + ' forms.', e);//return response
        }
        else if (e === 'CHR00116') {
            throw new ApolloError(`The designation is associated with job roles ${desAssosiateToJobRole.associatedJobRoles.join(',')}`);//return response   
        }
        else {
            errResult = commonLib.func.getError(e, 'CHR0020');
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
}
