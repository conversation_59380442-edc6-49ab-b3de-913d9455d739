const listWorkScheduleDetails = require('./roresolvers/workschedule/listWorkScheduleDetails');
const listWeekDays = require('./roresolvers/workschedule/listWeekDays');
const retrieveWorkScheduleAndWeekOff = require('./roresolvers/workschedule/retrieveWorkScheduleAndWeekOff');
const getEmployeesDetailsBasedOnRole = require('./roresolvers/getEmployeesDetailsBasedOnRole')
const checkUserDefinedEmployeeExit = require('./roresolvers/checkUserDefinedEmployeeExit')
const retrieveLeaveSettings = require('./roresolvers/retrieveLeaveSettings');
const retrieveLeaveDetails = require('./roresolvers/retrieveLeaveDetails');
const retrieveRosterManagmentSetting = require('./roresolvers/retrieveRosterManagmentSetting');
const retrieveFormLevelCoverage = require('./roresolvers/retrieveFormLevelCoverage');
const retrieveCompoffRules = require('./roresolvers/retrieveCompoffRules');
const retrieveSpecialWages = require('./roresolvers/retrieveSpecialWages');
const retrieveOvertimeConfiguration = require('./roresolvers/retrieveOvertimeConfiguration');
const listCustomGroupHolidays = require('./roresolvers/listCustomGroupHolidays');
const listHolidays = require('./roresolvers/listHolidays');
const listProjectDetails = require('./roresolvers/listProjectDetails');
const initiateRefreshCustomEmpGroups = require('./roresolvers/initiateRefreshCustomEmpGroups')
const listDesignationDetails = require('./roresolvers/listDesignationDetails');
const retrieveHolidaySettings = require('./roresolvers/retrieveHolidaySettings');
const listLocationHolidays = require('./roresolvers/locationHolidays/listLocationHolidays');
const listLeaveTypes = require('./roresolvers/leaves/listLeaveTypes');
const listEmployeeDetails = require('./roresolvers/listEmployeeDetails');
const exportEmployeeDetails = require('./roresolvers/exportEmployeeDetails');
const getAuthenticationMethods = require('./roresolvers/getAuthenticationMethods');
const getEffectiveDate = require('./roresolvers/getEffectiveDate');
const retrieveShortTimeOffSettings = require('./roresolvers/settings/retrieveShortTimeOffSettings');
const retrieveOnDutySettings = require('./roresolvers/settings/retrieveOnDutySettings');
const retrieveGeneralSettings = require('./roresolvers/settings/retrieveGeneralSettings');
const listPreApprovalSettings = require('./roresolvers/listPreApprovalSettings');
const listPreApprovalRequests = require('./roresolvers/listPreApprovalRequests');
const listWorkflowDetails = require('./roresolvers/listWorkflowDetails')
const retrievePreApprovalSettings = require('./roresolvers/retrievePreApprovalSettings');
const validateWfhPreApproval = require('./roresolvers/preApproval/validateWfhPreApproval');
const getPreApprovalEmployeeList = require('./roresolvers/getPreApprovalEmployeeList');
const getEmployeeWeekOffAndHolidayDetails = require('./roresolvers/getEmployeeWeekOffAndHolidayDetails');
const listLanguages = require('./roresolvers/commonApis/listLanguages');
const listCountries = require('./roresolvers/commonApis/listCountries');
const listEmpProfession = require('./roresolvers/commonApis/listEmpProfessions');
const listCourseDetails = require('./roresolvers/commonApis/listCourseDetails');
const listDocumentCategory = require('./roresolvers/commonApis/listDocumentCategory');
const listDocumentType = require('./roresolvers/commonApis/listDocumentType');
const listDocumentSubType = require('./roresolvers/commonApis/listDocumentSubType');
const listBankDetails = require('./roresolvers/commonApis/listBankDetails');
const listAccountType = require('./roresolvers/commonApis/listAccountType');
const retrieveAccreditationCategoryAndType = require('./roresolvers/commonApis/retrieveAccreditationCategoryAndType');
const listInsuranceType = require('./roresolvers/commonApis/listInsuranceType');
const listBusinessUnit = require('./roresolvers/settings/businessUnit/listBusinessUnit');
const retrieveLopRecoverySettingsDetails = require('./roresolvers/settings/lopRecovery/retrieveLopRecoverySettingsDetails')
const retrieveProjectActivities = require('./roresolvers/retrieveProjectActivities');
const listProjectActivities = require('./roresolvers/listProjectActivities');
const retrieveTimesheetEmpLeaveWeekOff = require('./roresolvers/timesheet/retrieveTimesheetEmpLeaveWeekOff');
const retrieveTimeSheetProjectDetails = require('./roresolvers/timesheet/retrieveTimeSheetProjectDetails');
const retrieveMinMaxDateForActivity = require('./roresolvers/retrieveMinMaxDateForActivity');
const getTimesheetCountForProject = require('./roresolvers/getTimesheetCountForProject');
const exportProjectDetailsAndActivities = require('./roresolvers/exportProjectDetailsAndActivities');
const retrieveTimesheetSettings = require('./roresolvers/timesheet/retrieveTimesheetSettings');
const getTotalProjectAndActivityRecords = require('./roresolvers/getTotalProjectAndActivityRecords');
const listOrganizationGroup = require('./roresolvers/organizationGroup/listOrganizationGroup');
const listLeaveOverrideEmployeeDetails = require('./roresolvers/timeOffManagement/leaveOverride/listLeaveOverrideEmployeeDetails');
const retrieveLeaveOverrideHistory = require('./roresolvers/timeOffManagement/leaveOverride/retrieveLeaveOverrideHistory');
const listLocationDetails = require('./roresolvers/listLocationDetails');
const retrieveLogoPath = require('./roresolvers/retrieveLogoPath');
const listRooms = require('./roresolvers/listRooms');
const listCompOffConfigHistory = require('./roresolvers/settings/compOff/listCompOffConfigHistory');
const exportEmployeeAllDetails = require('./roresolvers/exportEmployeeAllDetails');
const retrieveDynamicFormFields = require('./roresolvers/customFields/retrieveDynamicFormFields');
const retrieveDynamicFieldValues = require('./roresolvers/customFields/retrieveDynamicFieldValues');
const listCustomEmailTemplates = require('./roresolvers/customEmailTemplates/listCustomEmailTemplates');
const listEmailTemplatePlaceHolderValues=require('./roresolvers/customEmailTemplates/listEmailTemplatePlaceHolderValues')
const retrieveAirTicketSettings=require('./roresolvers/airTicketing/retrieveAirTicketSettings');
const retrieveAirTicketSettlementSummary=require('./roresolvers/airTicketing/retrieveAirTicketSettlementSummary');
const listEmployeeIdPrefixSettings = require('./roresolvers/employeeIdPrefix/listEmployeeIdPrefixSettings');
const listSalaryPayslip = require('./roresolvers/listSalaryPayslip');
const getCloudFrontUrl = require('./roresolvers/customEmailTemplates/getCloudFrontUrl');
const getDataTableDefaultHeaders = require('./roresolvers/getDataTableDefaultHeaders');

//Mutations
const addUpdateCustomFieldValues = require('./woresolvers/customFields/addUpdateCustomFieldValues');
const getLocationByDesignation = require('./roresolvers/getLocationByDesignation');

// Define resolver
const resolvers = {
    Query: Object.assign({},
        listWorkScheduleDetails,
        listWeekDays,
        retrieveWorkScheduleAndWeekOff,
        getEmployeesDetailsBasedOnRole,
        checkUserDefinedEmployeeExit,
        retrieveLeaveSettings,
        retrieveLeaveDetails,
        retrieveRosterManagmentSetting,
        retrieveFormLevelCoverage,
        retrieveCompoffRules,
        retrieveSpecialWages,
        retrieveOvertimeConfiguration,
        listCustomGroupHolidays,
        listHolidays,
        listProjectDetails,
        initiateRefreshCustomEmpGroups,
        listDesignationDetails,
        retrieveHolidaySettings,
        listLocationHolidays,
        listLeaveTypes,
        listEmployeeDetails,
        exportEmployeeDetails,
        getAuthenticationMethods,
        getEffectiveDate,
        retrieveShortTimeOffSettings,
        retrieveOnDutySettings,
        retrieveGeneralSettings,
        listPreApprovalSettings,
        listPreApprovalRequests,
        listWorkflowDetails,
        retrievePreApprovalSettings,
        validateWfhPreApproval,
        getPreApprovalEmployeeList,
        getEmployeeWeekOffAndHolidayDetails,
        listLanguages,
        listCountries,
        listEmpProfession,
        listCourseDetails,
        listDocumentCategory,
        listDocumentType,
        listDocumentSubType,
        retrieveAccreditationCategoryAndType,
        listBankDetails,
        listAccountType,
        listInsuranceType,
        listBusinessUnit.resolvers.Query,
        retrieveLopRecoverySettingsDetails,
        retrieveProjectActivities,
        listProjectActivities,
        retrieveTimesheetEmpLeaveWeekOff,
        retrieveTimeSheetProjectDetails,
        retrieveMinMaxDateForActivity,
        getTimesheetCountForProject,
        exportProjectDetailsAndActivities,
        retrieveTimesheetSettings,
        getTotalProjectAndActivityRecords,
        listOrganizationGroup,
        listLeaveOverrideEmployeeDetails,
        retrieveLeaveOverrideHistory,
        listLocationDetails,
        retrieveLogoPath,
        listRooms,
        listCompOffConfigHistory,
        exportEmployeeAllDetails,
        retrieveDynamicFormFields,
        retrieveDynamicFieldValues,
        listCustomEmailTemplates,
        listEmailTemplatePlaceHolderValues,
        retrieveAirTicketSettings,
        retrieveAirTicketSettlementSummary,
        listEmployeeIdPrefixSettings,
        getLocationByDesignation,
        listSalaryPayslip,
        getCloudFrontUrl,
        getDataTableDefaultHeaders
    ),
    Mutation: Object.assign({},
        addUpdateCustomFieldValues,
    )
}
exports.resolvers = resolvers;