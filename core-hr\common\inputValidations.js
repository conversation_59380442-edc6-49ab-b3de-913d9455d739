//Require common validation
const commonValidation = require('./commonvalidation');
//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const { validateWithRules } = require('@cksiva09/validationlib/src/validator');
const moment = require('moment');

//Function to validate the add / update project details inputs.
async function validateProjectsInputs(args, coverage) {
    try {
        let validationError = {};
        const { projectId, projectName, clientName, managerId, locationId, status, description, employeeId, customGroupId } = args;
        if (projectId && !(commonValidation.numberValidation(projectId))) {
            validationError['IVE0278'] = commonLib.func.getError('', 'IVE0278').message1;
        }

        if (!projectName) {
            validationError['IVE0279'] = commonLib.func.getError('', 'IVE0279').message;
        } else {
            if (!commonValidation.alphaNumSpCDotHySlashValidation(projectName)) {
                validationError['IVE0279'] = commonLib.func.getError('', 'IVE0279').message2;
            }
            if (!commonValidation.checkLength(projectName, 3, 50)) {
                validationError['IVE0279'] = commonLib.func.getError('', 'IVE0279').message3;
            }
        }

        if (clientName) {
            if (!commonValidation.alphaNumSpaceSymbolValidation(clientName)) {
                validationError['IVE0282'] = commonLib.func.getError('', 'IVE0282').message;
            }
            if (!commonValidation.checkLength(clientName, 3, 100)) {
                validationError['IVE0282'] = commonLib.func.getError('', 'IVE0282').message1;
            }
        }

        if (description) {
            if (!commonValidation.descriptionValidation(description)) {
                validationError['IVE0283'] = commonLib.func.getError('', 'IVE0283').message;
            }
            if (!commonValidation.checkLength(description, 5, 600)) {
                validationError['IVE0283'] = commonLib.func.getError('', 'IVE0283').message1;
            }
        }

        if (managerId && !(commonValidation.numberValidation(managerId))) {
            validationError['IVE0095'] = commonLib.func.getError('', 'IVE0095').message;
        }

        if (locationId && !(commonValidation.numberValidation(locationId))) {
            validationError['IVE0181'] = commonLib.func.getError('', 'IVE0181').message;
        }

        if (!status) {
            validationError['IVE0284'] = commonLib.func.getError('', 'IVE0284').message;
        } else if (status !== "Open" && status !== "Closed") {
            validationError['IVE0284'] = commonLib.func.getError('', 'IVE0284').message1;
        }

        if (coverage === 'Employee' && (!employeeId || employeeId.length === 0)) {
            validationError['IVE0285'] = commonLib.func.getError('', 'IVE0285').message;
        }

        if (coverage === 'CUSTOMGROUP' && (!customGroupId)) {
            validationError['IVE0286'] = commonLib.func.getError('', 'IVE0286').message;
        }
        return validationError;
    } catch (err) {
        console.log('Error in the validateProjectsInputs function in the main catch block.', err);
        throw err;
    }
}

async function validateAddUpdateDesignationInputs(args) {
    try {
        let validationError = {};
        const { designationId, designationCode, level, designationName, description, status, employeeConfirmation } = args;

        if (designationId && !(commonValidation.numberValidation(args.designationId))) {
            validationError['IVE0280'] = commonLib.func.getError('', 'IVE0280').message1;
        }
        if (!designationName) {
            validationError['IVE0281'] = commonLib.func.getError('', 'IVE0281').message;
        } else {
            if (!commonValidation.alphaNumSpCDotHySlashValidation(designationName)) {
                validationError['IVE0281'] = commonLib.func.getError('', 'IVE0281').message2;
            }
            if (!commonValidation.checkLength(designationName, 2, 100)) {
                validationError['IVE0281'] = commonLib.func.getError('', 'IVE0281').message3;
            }
        }
        if(designationCode && designationCode.length > 0){
            if (!commonValidation.commonInputAlphaNumValidation(designationCode)) {
                validationError['IVE0490'] = commonLib.func.getError('', 'IVE0490').message;
            }
            if (!commonValidation.checkLength(designationCode, 1, 100)) {
                validationError['IVE0490'] = commonLib.func.getError('', 'IVE0490').message1;
            }
        }
        if (description) {
            if (!commonValidation.descriptionValidation(description)) {
                validationError['IVE0283'] = commonLib.func.getError('', 'IVE0283').message;
            }
            if (!commonValidation.checkLength(description, 5, 600)) {
                validationError['IVE0283'] = commonLib.func.getError('', 'IVE0283').message1;
            }
        }
        if (employeeConfirmation) {
            if (!commonValidation.alphaNumSpCDotHySlashValidation(employeeConfirmation)) {
                validationError['IVE0288'] = commonLib.func.getError('', 'IVE0288').message;
            }
        }

        if (!status) {
            validationError['IVE0287'] = commonLib.func.getError('', 'IVE0287').message;
        } else if (status !== "Active" && status !== "InActive") {
            validationError['IVE0287'] = commonLib.func.getError('', 'IVE0287').message1;
        }

        if(level !== null && level !== undefined){
            //Validate if it is between 1 and 100
            if(!commonValidation.checkMinMaxValue(level, 1, 100)){
                validationError['IVE0606'] = commonLib.func.getError('', 'IVE0606').message;
            }
        }
        return validationError;

    } catch (err) {
        console.log('Error in the validateAddUpdateDesignationInputs function in the main catch block.', err);
        throw err;
    }
}

//Function to validate the add / update project details inputs.
async function validatePreApprovalSettingsInputs(args) {
    try {
        let validationError = {};
        const { preApprovalConfigurationId, preApprovalType, coverage, customGroupId, period, noOfPreApprovalRequest, restrictSandwich, restrictSandwichFor, advanceNotificationDays, workflowId, status } = args;

        return validationError;
    } catch (err) {
        console.log('Error in the validatePreApprovalSettingsInputs function in the main catch block.', err);
        throw err;
    }
}
async function validateTimeSheetActivityInputs(dayDetails,Week_Ending_Date,description,fieldValidations) {
    try {
        let validationError = {};
           
        dayDetails.forEach((item)=>{
            if(item.startTime && item.endTime && (item.endTime!=='00:00:00'||item.startTime!=='00:00:00')){
            const today = moment(); // Get today's date
            const start = moment(`${today.format('YYYY-MM-DD')}T${item.startTime}`, 'YYYY-MM-DDTHH:mm');
            const end = moment(`${today.format('YYYY-MM-DD')}T${item.endTime}`, 'YYYY-MM-DDTHH:mm');
            if(start > end){
                validationError['IVE0317'] = commonLib.func.getError('', 'IVE0317').message1;
            }
                const diffInHours = (end - start) / (1000 * 60 * 60); // Difference in hours
                // Check if the calculated difference matches Total_Hours (rounded to 2 decimal places)
                if(((diffInHours * 100) / 100).toFixed(2) !== item.totalHours.toFixed(2)){
                    validationError['IVE0433'] = commonLib.func.getError('', 'IVE0433').message;
                }
            }
                if (item.notes && !commonValidation.notesValidation(item.notes)) {
                    validationError['IVE0434'] = commonLib.func.getError('', 'IVE0434').message1;
                    // length between 3 and 100
                  } else if (item.notes && !commonValidation.checkLength(item.notes, 5, 500)) {
                    validationError['IVE0434'] = commonLib.func.getError('', 'IVE0434').message2;
                  }
                
            })
        
        
        for (const field in fieldValidations) {
            let fieldName = field; // By default, use the current field name
            if (args.hasOwnProperty(field)) {
                const validation = validateWithRules(args[field], fieldName);
                if (validation !== 'Validation not found' && (!validation.validation || !validation.length)) {
    
                    validationError[fieldValidations[field]] = validation.length ? commonLib.func.getError('', fieldValidations[field]).message : commonLib.func.getError('', fieldValidations[field]).message1;
                }
            }
        }
        return validationError;
    } catch (err) {
        console.log('Error in the validateTimeSheetActivityInputs function in the main catch block.', err);
        throw err;
    }
}
async function validateCommonRuleInput(args, fieldValidations) {
    try{
    let validationError = {};
    for (const field in fieldValidations) {
        if (!args.hasOwnProperty(field)) continue; // Skip if field doesn't exist

        let ruleField = field;
        let errorCode = fieldValidations[field];

        // If structured metadata is provided
        if (typeof fieldValidations[field] === 'object') {
            ruleField = fieldValidations[field].ruleField;
            errorCode = fieldValidations[field].errorCode;
        }

        const validation = validateWithRules(args[field], ruleField);
        if (validation !== 'Validation not found' && (!validation.validation || !validation.length)) {
            validationError[errorCode] = validation.length ? commonLib.func.getError('', errorCode).message : commonLib.func.getError('', errorCode).message1;
        }
    }
    return validationError;
}
catch (err) {
    console.log('Error in the validateCommonRuleInput function in the main catch block.', err);
    throw err;
}
}
// function to validate start and end date
async function validatePreApprovalRequestsInputs(args) {
    // variable declaration
    let validationError = {};
    try {
        // validate start date
        if (!args.startDate) {
            validationError['IVE0316'] = commonLib.func.getError('', 'IVE0316').message;
        }
        // validate end date
        if (!args.endDate) {
            validationError['IVE0317'] = commonLib.func.getError('', 'IVE0317').message;
        }
        // Check whether both start date and end date exists
        if (args.startDate && args.endDate) {
            // end date should be greater than start date
            if (args.endDate < args.startDate) {
                validationError['IVE0317'] = commonLib.func.getError('', 'IVE0317').message1;
            }
        }
        return validationError;
    }
    catch (error) {
        console.log('Error in validateStartAndEndDate catch block', error);
        throw error;
    }
}
async function validateLopRequestsInputs(args,Lop_Coverage) {
    // variable declaration
    let validationError = {};
    try {
        const { CustomGroup_Id,Auto_LOP_Applicable, Attendance_Shortage_Applicable,Late_Attendance_Applicable, Configuration_Status } = args;

        if ((Lop_Coverage === "Organization" && CustomGroup_Id) || (Lop_Coverage.toLowerCase() === "custom group" && !CustomGroup_Id)) {
            let errorMessage = `lop recovery setting data is set to ${Lop_Coverage === 'Organization' ? 'Custom Group' : 'Organization'}, conflicting with the platform's ${Lop_Coverage} level setting. Please adjust the platform to ${Lop_Coverage} or establish a new configuration.`;
            validationError['IVE0386'] = errorMessage;
        }
        if (
            Attendance_Shortage_Applicable !== 'Yes' && Attendance_Shortage_Applicable !== 'No' ||
            Late_Attendance_Applicable !== 'Yes' && Late_Attendance_Applicable !== 'No' ||
            Auto_LOP_Applicable !== 'Yes' && Auto_LOP_Applicable !== 'No'
            || Configuration_Status !=='Active' && Configuration_Status!=='InActive'
          ) {
            validationError['IVE0386'] = commonLib.func.getError('', 'IVE0386').message;
          }
        // validate start date
     
        return validationError;
    }
    catch (error) {
        console.log('Error in validateStartAndEndDate catch block', error);
        throw error;
    }
}

module.exports = {
    validateProjectsInputs,
    validateAddUpdateDesignationInputs,
    validatePreApprovalSettingsInputs,
    validateCommonRuleInput,
    validatePreApprovalRequestsInputs,
    validateLopRequestsInputs,
    validateTimeSheetActivityInputs
};