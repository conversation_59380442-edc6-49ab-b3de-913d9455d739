//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../../../common/tablealias');
//Require moment
const moment = require('moment');

let responseStartDateTime, responseEndDateTime;
let preApprovalRequestType, preApprovalRequestStatus;
let failedPreApprovals = {
    failedPreApprovals: []
}
//Resolver function to validate whether the pre-approval request are approved or not to allow the employee to check-in and check-out
module.exports.validateWfhPreApproval = async (parent, args, context, info) => {
    console.log('Inside validateWfhPreApproval function', args, context);
    let organizationDbConnection;
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);

        if (args.employeeId) {
            let validationErrorCode = '';
            let employeeId = args.employeeId;
            let punchInDateTime = args.punchInDateTime ? args.punchInDateTime : '';
            let punchOutDateTime = args.punchOutDateTime ? args.punchOutDateTime : '';
            let currentWorkScheduleDetails = args.currentWorkScheduleDetails ? args.currentWorkScheduleDetails : '';
            let attendanceDate = args.attendanceDate ? args.attendanceDate : '';
            let checkInCheckOutWorkPlace = args.checkInCheckoutWorkPlace ? args.checkInCheckoutWorkPlace : '';
            let source = args.source;
            let punchType = args.punchType;
            let employeeCurrentDateTime;

            if (source === 'dashboard') {
                //The date will be send only from attendance regularization
                punchInDateTime = '', punchOutDateTime = '', attendanceDate = '';

                // Call function getEmployeeTimeZone() to get current date and time based on employee time zone 
                // Send employeeId ,dbConnection and another flag as 1(This flag is to get the time zone date with time)
                let employeeCurrentDateTime = await commonLib.func.getEmployeeTimeZone(employeeId, organizationDbConnection, 1);

                /** Check the login employee current date response is string or not to validate whether the current date time 
                 * or the other error response is returned. */
                employeeCurrentDateTime = (typeof (employeeCurrentDateTime) === 'string') ? employeeCurrentDateTime : null;
                console.log(employeeCurrentDateTime, 'employeeCurrentDateTime')

                if (employeeCurrentDateTime) {
                    if (punchType === 'punchin') {
                        punchInDateTime = employeeCurrentDateTime;
                    } else {
                        punchOutDateTime = employeeCurrentDateTime;
                    }
                } else {
                    console.log('Employee current date-time is empty', employeeCurrentDateTime, args);
                    validationErrorCode = 'CHR0068';
                }
            } else if (source === 'attendanceregularization') {
                //The punch-in and punch-out date-time cannot be send because this request will be send before the popup will be opened
                if (attendanceDate) {
                    punchInDateTime = punchOutDateTime = '';
                } else {
                    console.log('Attendance date is empty', attendanceDate, args);
                    validationErrorCode = '_EC0007';
                }
            } else {
                //If the source is attendance form
                if (punchInDateTime || punchOutDateTime) {
                    //The date will be send only from attendance regularization
                    attendanceDate = '';
                } else {
                    console.log('Punch-in and Punch-out is empty', punchInDateTime, punchOutDateTime, args);
                    validationErrorCode = '_EC0007';
                }
            }

            if (validationErrorCode) {
                throw validationErrorCode;
            } else {
                let workScheduleDateTime = '';
                if (source !== 'attendanceregularization') {
                    if (punchInDateTime) {
                        workScheduleDateTime = punchInDateTime;
                    } else if (punchOutDateTime) {
                        workScheduleDateTime = punchOutDateTime;
                    }
                }

                let regularFrom, regularTo, considerationFrom, considerationTo;
                if (workScheduleDateTime) {
                    currentWorkScheduleDetails = await commonLib.employees.getCurrentWorkScheduleDetails(employeeId, workScheduleDateTime, organizationDbConnection);
                    regularFrom = currentWorkScheduleDetails.regularFrom;
                    regularTo = currentWorkScheduleDetails.regularTo;
                    considerationFrom = currentWorkScheduleDetails.considerationFrom;
                    considerationTo = currentWorkScheduleDetails.considerationTo;
                    console.log(currentWorkScheduleDetails, 'currentWorkScheduleDetails')
                }

                if (punchInDateTime || punchOutDateTime || attendanceDate) {
                    // From attendance regularization, the attendance date will be send and date-time will not send
                    if (!attendanceDate) {
                        attendanceDate = moment(regularFrom).format('YYYY-MM-DD');
                    }
                    console.log('attendanceDate', attendanceDate)
                    let dayTypeResponse = await retrieveTypeOfDay(organizationDbConnection, employeeId, attendanceDate)
                    let typeOfDayToValidate = []
                    if (dayTypeResponse.isWeekOffDay === 1) {
                        typeOfDayToValidate.push('Week Off')
                    }
                    else if (dayTypeResponse.isHoliday === 1) {
                        typeOfDayToValidate.push('Holiday')
                    }
                    else {
                        //If the source is attendance and if the work place is not work from home then we do not need to validate the pre-approval request
                        //If the source is attendance regularization or dashboard then work place will not be send. So we need to validate the pre-approval request based on that we need to restrict the work place in UI.
                        if (source === 'attendance' && (checkInCheckOutWorkPlace && checkInCheckOutWorkPlace !== 'workfromhome')) {
                            console.log('pre-approval validate if');
                        } else {
                            console.log('pre-approval validate else', source, checkInCheckOutWorkPlace);
                            typeOfDayToValidate.push('Business Working Day');
                        }
                    }
                    console.log('typeOfDayToValidate', typeOfDayToValidate)
                    if (typeOfDayToValidate && typeOfDayToValidate.length > 0) {
                        return (
                            organizationDbConnection
                                .transaction(function (trx) {
                                    return (
                                        organizationDbConnection(ehrTables.preApprovalSettings + 'as A')
                                            .select('A.Pre_Approval_Type', 'A.Coverage', 'A.Custom_Group_Id', 'A.Type_Of_Day', 'A.Status')
                                            .from(ehrTables.preApprovalSettings + ' as A')
                                            .where('A.Status', 'Active')
                                            .where(function () {
                                                typeOfDayToValidate.forEach((day) => {
                                                    this.orWhere('A.Type_Of_Day', 'LIKE', `%${day}%`);
                                                });
                                            })
                                            .transacting(trx)
                                            .then(async (result) => {
                                                if(result && result.length > 0){
                                                   result=result.filter(item=>item.Pre_Approval_Type.toLowerCase() != 'overtime work');
                                                }
                                                if (result && result.length > 0) {
                                                    let preApprovalExistInputs = {
                                                        result, employeeId, regularFrom, regularTo, considerationFrom, considerationTo, attendanceDate, inDateTime: punchInDateTime, outDateTime: punchOutDateTime, source, typeOfDayToValidate
                                                    }
                                                    let settingsResult = await isPreApprovalRequestExistForEmployee(organizationDbConnection, preApprovalExistInputs);
                                                    if (!settingsResult) {
                                                        throw 'CHR0057'
                                                    }
                                                    return settingsResult;

                                                } else {
                                                    //Attendance can be added for employee as the pre approval is not configured for work from home or inactivated.
                                                    console.log('Input date is either week-off or holiday and pre-approval does not exist or inactivated for week-off or holiday. So attendance can be added with all the work places.');
                                                    return 1;
                                                }
                                            })
                                    )
                                })
                                .then(() => {
                                    console.log('Attendance can be added or updated');

                                    //Destroy DB connection
                                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                                    return { errorCode: "", message: "Attendance can be added or updated as the pre-approval request is approved for the attendance date.", preApprovalRequestType, preApprovalRequestStatus };
                                })
                                .catch(settingsCatchError => {
                                    console.log('Error in validateWfhPreApproval function .catch block', settingsCatchError, responseStartDateTime, responseEndDateTime);
                                    let halfDayDurationErrorCodes = ['CHR0066', 'CHR0056'];
                                    if (halfDayDurationErrorCodes.includes(settingsCatchError)) {
                                        let halfDayPeriodErrorMessage;
                                        if (responseStartDateTime && responseEndDateTime) {
                                            //Response format: 01st Aug 2023 08:00 AM, 01st Aug 2023 1:30 PM
                                            responseStartDateTime = moment(responseStartDateTime).format('Do MMM YYYY hh:mm A');
                                            responseEndDateTime = moment(responseEndDateTime).format('Do MMM YYYY hh:mm A');
                                            halfDayPeriodErrorMessage = 'The request date-time does not fall in between the ' + responseStartDateTime + ' and the ' + responseEndDateTime;
                                        } else {
                                            halfDayPeriodErrorMessage = 'The request date-time does not fall in between the half-day duration';
                                        }
                                        //Response JSON
                                        errResult = {
                                            code: settingsCatchError,
                                            message: halfDayPeriodErrorMessage
                                        };
                                    } else {
                                        errResult = commonLib.func.getError(settingsCatchError, 'CHR0058');
                                    }
                                    //Destroy DB connection
                                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                                    throw new ApolloError(errResult.message, errResult.code, failedPreApprovals);//return response
                                })
                        )
                    } else {
                        console.log('Input date falls on working day and wfh is not selected as the work place from the attendance source')
                        //Destroy DB connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        return { errorCode: "", message: "Attendance can be added or updated as the pre-approval request is approved for the attendance date.", preApprovalRequestType, preApprovalRequestStatus };
                    }
                } else {
                    console.log('Attendance date is empty', employeeCurrentDateTime)
                    throw 'CHR0063';
                }
            }
        } else {
            console.log('Invalid Inputs.', args);
            throw '_EC0007';
        }
    } catch (mainBlockError) {
        console.log('Error in the validateWfhPreApproval() function in the main catch block.', mainBlockError);
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        let errResult = commonLib.func.getError(mainBlockError, 'CHR0101');
        throw new ApolloError(errResult.message, errResult.code, failedPreApprovals);
    }
}

async function retrieveTypeOfDay(organizationDbConnection, employeeId, attendanceDate) {
    try {
        let weekOffHolidayDetails = await commonLib.payroll.getWeekOffAndHolidays(organizationDbConnection, employeeId, attendanceDate, attendanceDate);
        if (weekOffHolidayDetails && weekOffHolidayDetails.length > 0) {
            weekOffHolidayDetails = weekOffHolidayDetails[0];
        } else {
            weekOffHolidayDetails = {}
        }
        return weekOffHolidayDetails;
    } catch (typeOfDayError) {
        console.log('Error in the retrieveTypeOfDay() function in the main catch block.', typeOfDayError);
        throw typeOfDayError;
    }
}

async function isPreApprovalRequestExistForEmployee(organizationDbConnection, validationInputs) {
    try {
        let { employeeId, regularFrom, regularTo, considerationFrom, considerationTo, attendanceDate, inDateTime, outDateTime, source, typeOfDayToValidate } = validationInputs;
        if (attendanceDate) {
            let whereCondition = organizationDbConnection.raw('( ? BETWEEN PR.Start_Date AND PR.End_Date)', attendanceDate);

            return (
                organizationDbConnection(ehrTables.preApprovalRequests + 'as PR')
                    .select('PR.Pre_Approval_Id', 'PR.Duration', 'PR.Status', 'PR.Period', 'PR.Pre_Approval_Type')
                    .from(ehrTables.preApprovalRequests + ' as PR')
                    .where('PR.Employee_Id', employeeId)
                    .whereIn('PR.Status', ['Approved'])
                    .where(whereCondition)
                    .where(function () {
                        typeOfDayToValidate.forEach((day) => {
                            this.orWhere('PR.Type_Of_Day', 'LIKE', `%${day}%`);
                        });
                    })
                    .then(async (preApprovalRequestResult) => {
                        if (preApprovalRequestResult && preApprovalRequestResult.length > 0) {
                            preApprovalRequestType = preApprovalRequestResult[0].Pre_Approval_Type;
                            preApprovalRequestStatus = preApprovalRequestResult[0].Status;
                            /** We will validate the pre-approval request from attendance regularization before
                             * the date-time is selected by the employee. So this block should not be validated
                             * for this source instead it will be validated when the call is triggered before
                             * updating the attendance details for this date. */
                            if (source !== 'attendanceregularization' && preApprovalRequestResult[0].Duration === 'Half Day' && (inDateTime || outDateTime)
                                && regularFrom && regularTo) {
                                regularFrom = moment(regularFrom).format("YYYY-MM-DD HH:mm:ss")
                                regularTo = moment(regularTo).format("YYYY-MM-DD HH:mm:ss")

                                let date = new Date(regularFrom);
                                let milliseconds = date.getTime();
                                let date2 = new Date(regularTo);
                                let milliseconds2 = date2.getTime();
                                let midDateTime = new Date((milliseconds + milliseconds2) / 2);
                                console.log("actual midDateTime", midDateTime)

                                let firstHalfEndDateTime = moment(midDateTime).format('YYYY-MM-DD HH:mm:ss')
                                console.log("Formatted firstHalfEndDateTime", firstHalfEndDateTime)

                                let successResponse = 0;
                                let errorCode = '';

                                if (preApprovalRequestResult[0].Period == 'First Half') {
                                    responseStartDateTime = considerationFrom;
                                    responseEndDateTime = firstHalfEndDateTime;
                                    responseErrorCode = 'CHR0066';
                                } else {
                                    let midDateNewDateTime = new Date(firstHalfEndDateTime);
                                    let secondHalfStartDateTime = new Date(midDateNewDateTime.setMinutes(midDateNewDateTime.getMinutes() + 1));
                                    console.log("secondHalfStartDateTime", secondHalfStartDateTime)

                                    responseStartDateTime = secondHalfStartDateTime;
                                    responseEndDateTime = considerationTo;
                                    responseErrorCode = 'CHR0056';
                                }

                                if (inDateTime) {
                                    if (moment(inDateTime) >= moment(responseStartDateTime)
                                        && moment(inDateTime) <= moment(responseEndDateTime)) {
                                        successResponse = 1;
                                    } else {
                                        successResponse = 0;
                                        errorCode = responseErrorCode;
                                    }
                                }

                                if (outDateTime) {
                                    if (moment(outDateTime) >= moment(responseStartDateTime)
                                        && moment(outDateTime) <= moment(responseEndDateTime)) {
                                        successResponse = 1;
                                    } else {
                                        successResponse = 0;
                                        errorCode = responseErrorCode;
                                    }
                                }
                                console.log(successResponse, responseStartDateTime, responseEndDateTime, 'errorCode', errorCode, 'inDateTime', inDateTime, 'outDateTime', outDateTime)
                                if (errorCode) {
                                    console.log('The request date-time does not fall in the half-day duration');
                                    throw errorCode;
                                } else {
                                    return 1;
                                }
                            } else {
                                console.log('Attendance can be regularized for the attendance date as the pre-approval request is approved for the attendance date')
                                return 1;
                            }
                        } else {
                            const preApprovalSettingsResult = validationInputs.result;
                            console.log('preApprovalSettingsResult', preApprovalSettingsResult)

                            if (preApprovalSettingsResult && preApprovalSettingsResult.length > 0) {
                                let failedApprovals = []
                                for (let i = 0; i < preApprovalSettingsResult.length; i++) {
                                    const selectedSettings = preApprovalSettingsResult[i]
                                    const preApprovalType = selectedSettings.Pre_Approval_Type;
                                    if (selectedSettings.Coverage?.toLowerCase() === 'organization') {
                                        failedApprovals.push(preApprovalType)
                                    } else {
                                        const customGroupIds = selectedSettings.Custom_Group_Id;
                                        console.log('customGroupIds', customGroupIds)
                                        //Validate if the employee exists in the custom group
                                        if (customGroupIds) {
                                            const employeeExistsInCustomGroup = await validateCustomGroupExists(organizationDbConnection, customGroupIds, employeeId)
                                            if (employeeExistsInCustomGroup) {
                                                failedApprovals.push(preApprovalType)
                                            }
                                        }
                                    }
                                }

                                if (failedApprovals.length > 0) {
                                    //Remove duplication
                                    failedApprovals = [...new Set(failedApprovals)];
                                    failedPreApprovals.failedPreApprovals = failedApprovals;
                                    return 0;
                                } else {
                                    return 1;
                                }
                            }
                        }
                    })
                    .catch(catchError => {
                        console.log('Error in the isPreApprovalRequestExistForEmployee() function .catch block.', catchError);
                        throw catchError;
                    })
            )
        } else {
            throw 'CWS0008';//Work schedule does not exist
        }
    } catch (mainError) {
        console.log('Error in the isPreApprovalRequestExistForEmployee() function main catch block.', mainError);
        throw mainError;
    }
}

async function validateCustomGroupExists(organizationDbConnection, customGroupIds, employeeId) {
    try {
        const customGroupResult = await organizationDbConnection(ehrTables.cusEmpGroup + ' as CEG')
            .pluck('CEG.Group_Id')
            .innerJoin(ehrTables.customGroupEmployees + ' as CEGE', 'CEG.Group_Id', 'CEGE.Group_Id')
            .from(ehrTables.cusEmpGroup + ' as CEG')
            .where('CEG.Group_Id', customGroupIds)
            .where('CEGE.Employee_Id', employeeId)
            .whereIn('CEGE.Type', ['Default', 'AdditionalInclusion'])

        if (customGroupResult && customGroupResult.length > 0) {
            return 1
        } else {
            return 0;
        }
    }
    catch (catchError) {
        console.log('Error in the validateCustomGroupExists() function .catch block.', catchError);
        throw catchError;
    }
}