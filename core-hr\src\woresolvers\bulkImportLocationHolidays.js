//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { UserInputError, ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../../common/tablealias');
const { getAttendanceExist, getCompensatoryOffExist, getLeaveExist, checkSameHolidayLocationExists, getPaySlipExist } = require('../../common/commonfunctions');
const { formName } = require('../../common/appconstants');
const { validateHolidayInputs } = require('../../common/holidayInputValidation')
const moment = require('moment');


module.exports.bulkImportLocationHolidays = async (parent, args, context, info) => {
    console.log('Inside bulkImportLocationHolidays function');
    let organizationDbConnection;
    let validationError = {};
    let loginEmployeeId = context.Employee_Id;
    let orgCode = context.Org_Code
    let validationFailed = []
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);

        //Form Access check for adding location holidays
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, formName.holidays, '', 'UI');
        if (Object.keys(checkRights).length > 0 && checkRights.Role_Add === 1) {
            validationError = await validateHolidayInputs(args, 1);
            if (Object.keys(validationError).length == 0) {
                let loginEmployeeCurrentDateTime = await commonLib.func.getEmployeeTimeZone(loginEmployeeId, organizationDbConnection, 1);
                return (
                    organizationDbConnection
                        .transaction(async (trx) => {
                            let updateDetails = args.holidayDataLocation
                            let locationIds = []
                            //With the updateDetails form the updatable data
                            let formedData = updateDetails.map((el) => {
                                let form = []
                                //Get dates between Start_Date and End_Date
                                while (new Date(el.Start_Date) <= new Date(el.End_Date)) {
                                    form.push({
                                        'Index': el.Index,
                                        'Holiday_Id': el.Holiday_Id,
                                        'Holiday_Date': el.Start_Date,
                                        "Mandatory": el.Mandatory,
                                        "Holiday": el.Holiday,
                                        "Personal_Choice": el.Personal_Choice,
                                        "Description": el.Description,
                                        "Location_Id": el.Location_Id,
                                        "Added_By": loginEmployeeId,
                                        "Added_Date": loginEmployeeCurrentDateTime
                                    })
                                    el.Start_Date = moment(el.Start_Date).add(1, 'days').format("YYYY-MM-DD");
                                }
                                //get the locationIds to get the employeeIds
                                if (!locationIds.includes(el.Location_Id)) {
                                    locationIds.push(el.Location_Id)
                                }
                                return form;
                            })
                            //Getting all the multi dimensional array of objects to single array of object
                            formedData = formedData.flat();
                            //fetch the employeeIds with the locationIds
                            return (
                                organizationDbConnection(ehrTables.empJob)
                                    .select("Employee_Id", "Location_Id")
                                    .transacting(trx)
                                    .whereIn('Location_Id', locationIds)
                                    .then(async (data) => {
                                        //Merge the employeeId with the formedData
                                        for (let i = 0; i < formedData.length; i++) {
                                            if (data.length) {
                                                let employeeIds = []
                                                for (let j = 0; j < data.length; j++) {
                                                    if (formedData[i].Location_Id === data[j].Location_Id) {
                                                        employeeIds.push(data[j].Employee_Id)
                                                    }
                                                }
                                                formedData[i].Employee_Id = employeeIds
                                            } else {
                                                formedData[i].Employee_Id = []
                                                //Add directly
                                                let holidayCheckResult = await checkSameHolidayLocationExists(formedData, organizationDbConnection)
                                                if (!holidayCheckResult) {
                                                    throw 'CGH0114'
                                                }
                                                if (holidayCheckResult.length) {
                                                    holidayCheckResult.forEach(function (el) {
                                                        //Check if the employee_id is in formedData array of object if yes push the group id  to validationfailed
                                                        for (let i = 0; i < formedData.length; i++) {
                                                            if (el.Location_Id === formedData[i].Location_Id && el.Holiday_Date === formedData[i].Holiday_Date && el.Holiday_Id === formedData[i].Holiday_Id) {
                                                                validationFailed.push({
                                                                    Location_Id: formedData[i].Location_Id,
                                                                    Holiday_Date: formedData[i].Holiday_Date,
                                                                    Index: formedData[i].Index
                                                                })
                                                                formedData = formedData.filter((ele) => {
                                                                    return ele.Index !== formedData[i].Index
                                                                })
                                                                break;
                                                            }
                                                        }
                                                    })
                                                }
                                                if (formedData.length) {
                                                    let insertedHolidays = await addLocationHolidays(formedData, organizationDbConnection, trx)
                                                    if (insertedHolidays) {
                                                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                                                        return { errorCode: "", message: "Location based holidays have been imported successfully.", locationValidationFailed: validationFailed };
                                                    } else {
                                                        console.log('Error while inserting location based holidays');
                                                        throw 'CGH0125'
                                                    }
                                                } else {
                                                    //Validation Failed
                                                    console.log('All Validation Failed')
                                                    throw 'CGH0103';
                                                }
                                            }
                                        }
                                        //validation to check if date is in emp_attendance table
                                        let attendanceResult = await getAttendanceExist(formedData, organizationDbConnection);
                                        if (!attendanceResult) {
                                            throw 'CGH0105'
                                        }
                                        if (attendanceResult.length) {
                                            attendanceResult.forEach(function (el) {
                                                //Check if the employee_id is in formedData array of object if yes push the group id  to validationfailed
                                                for (let i = 0; i < formedData.length; i++) {
                                                    for (let j = 0; j < formedData[i].Employee_Id.length; j++) {
                                                        if ((el.Employee_Id == formedData[i].Employee_Id[j]) && (formedData[i].Holiday_Date == el.Attendance_Date)) {
                                                            validationFailed.push({
                                                                Location_Id: formedData[i].Location_Id,
                                                                Holiday_Date: el.Attendance_Date,
                                                                Index: formedData[i].Index
                                                            })
                                                            formedData = formedData.filter((ele) => {
                                                                return ele.Index !== formedData[i].Index
                                                            })
                                                            break;
                                                        }
                                                    }
                                                }
                                            })
                                        }

                                        if (formedData.length) {
                                            //validation to check if date is in compensatory table
                                            let compensatoryResult = await getCompensatoryOffExist(formedData, organizationDbConnection)
                                            if (!compensatoryResult) {
                                                throw 'CGH0106'
                                            }
                                            if (compensatoryResult.length) {
                                                compensatoryResult.forEach(function (el) {
                                                    //Check if the employee_id is in formedData array of object if yes push the group id  to validationfailed
                                                    for (let i = 0; i < formedData.length; i++) {
                                                        for (let j = 0; j < formedData[i].Employee_Id.length; j++) {
                                                            if ((el.Employee_Id == formedData[i].Employee_Id[j]) && (formedData[i].Holiday_Date == el.Compensatory_Date)) {
                                                                validationFailed.push({
                                                                    Location_Id: formedData[i].Location_Id,
                                                                    Holiday_Date: el.Compensatory_Date,
                                                                    Index: formedData[i].Index
                                                                })
                                                                formedData = formedData.filter((ele) => {
                                                                    return ele.Index !== formedData[i].Index
                                                                })
                                                                break;
                                                            }
                                                        }
                                                    }
                                                })
                                            }
                                        } else {
                                            throw 'CGH0103';
                                        }

                                        if (formedData.length) {
                                            //validation to check if date is in payslip
                                            let paySlipResult = await getPaySlipExist(formedData, organizationDbConnection, orgCode)
                                            if (!paySlipResult) {
                                                throw 'CGH0116'
                                            }
                                            if (paySlipResult.length) {
                                                paySlipResult.forEach(function (el) {
                                                    //Check if the employee_id is in formedData array of object if yes push the group id  to validationfailed
                                                    for (let i = 0; i < formedData.length; i++) {
                                                        for (let j = 0; j < formedData[i].Employee_Id.length; j++) {
                                                            if ((el.Employee_Id == formedData[i].Employee_Id[j]) && (formedData[i].Holiday_Date < el.maxSalaryMonthYear)) {
                                                                validationFailed.push({
                                                                    Location_Id: formedData[i].Location_Id,
                                                                    Holiday_Date: el.maxSalaryMonthYear,
                                                                    Index: formedData[i].Index
                                                                })
                                                                formedData = formedData.filter((ele) => {
                                                                    return ele.Index !== formedData[i].Index
                                                                })
                                                                break;
                                                            }
                                                        }
                                                    }
                                                })
                                            }
                                        } else {
                                            throw 'CGH0103';
                                        }

                                        if (formedData.length) {
                                            //validation to check if date is in leave table
                                            let employeeLeaveResult = await getLeaveExist(formedData, organizationDbConnection)
                                            if (!employeeLeaveResult) {
                                                throw 'CGH0107'
                                            }
                                            if (employeeLeaveResult.length) {
                                                employeeLeaveResult.forEach(function (el) {
                                                    //Check if the employee_id is in formedData array of object if yes push the group id  to validationfailed
                                                    for (let i = 0; i < formedData.length; i++) {
                                                        for (let j = 0; j < formedData[i].Employee_Id.length; j++) {
                                                            if ((el.Employee_Id == formedData[i].Employee_Id[j]) && (formedData[i].Holiday_Date >= el.EL.Start_Date && formedData[i].Holiday_Date <= el.EL.End_Date)) {
                                                                validationFailed.push({
                                                                    Location_Id: formedData[i].Location_Id,
                                                                    Holiday_Date: formedData[i].Holiday_Date,
                                                                    Index: formedData[i].Index
                                                                })
                                                                formedData = formedData.filter((ele) => {
                                                                    return ele.Index !== formedData[i].Index
                                                                })
                                                                break;
                                                            }
                                                        }
                                                    }
                                                })
                                            }
                                        } else {
                                            throw 'CGH0103';
                                        }

                                        //validation to check if same date is added for the location
                                        if (formedData.length) {
                                            let holidayCheckResult = await checkSameHolidayLocationExists(formedData, organizationDbConnection)
                                            if (!holidayCheckResult) {
                                                throw 'CGH0114'
                                            }
                                            if (holidayCheckResult.length) {
                                                holidayCheckResult.forEach(function (el) {
                                                    //Check if the employee_id is in formedData array of object if yes push the group id  to validationfailed
                                                    for (let i = 0; i < formedData.length; i++) {
                                                        if (formedData[i].Employee_Id.length) {
                                                            for (let j = 0; j < formedData[i].Employee_Id.length; j++) {
                                                                if (el.Location_Id === formedData[i].Location_Id && el.Holiday_Date === formedData[i].Holiday_Date && el.Holiday_Id === formedData[i].Holiday_Id) {
                                                                    validationFailed.push({
                                                                        Location_Id: formedData[i].Location_Id,
                                                                        Holiday_Date: formedData[i].Holiday_Date,
                                                                        Index: formedData[i].Index
                                                                    })
                                                                    formedData = formedData.filter((ele) => {
                                                                        return ele.Index !== formedData[i].Index
                                                                    })
                                                                    break;
                                                                }
                                                            }
                                                        } else {
                                                            if (el.Location_Id === formedData[i].Location_Id && el.Holiday_Date === formedData[i].Holiday_Date && el.Holiday_Id === formedData[i].Holiday_Id) {
                                                                validationFailed.push({
                                                                    Location_Id: formedData[i].Location_Id,
                                                                    Holiday_Date: formedData[i].Holiday_Date,
                                                                    Index: formedData[i].Index
                                                                })
                                                                formedData = formedData.filter((ele) => {
                                                                    return ele.Index !== formedData[i].Index
                                                                })
                                                                break;
                                                            }
                                                        }
                                                    }
                                                })
                                            }
                                        } else {
                                            throw 'CGH0103';
                                        }
                                        if (formedData.length) {
                                            let insertedHolidays = await addLocationHolidays(formedData, organizationDbConnection, trx)
                                            if (insertedHolidays) {
                                                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                                return { errorCode: "", message: "Location based holidays have been imported successfully.", locationValidationFailed: validationFailed };
                                            } else {
                                                console.log('Error while inserting location based holidays');
                                                throw 'CGH0125'
                                            }
                                        } else {
                                            //Validation Failed
                                            console.log('All Validation Failed')
                                            throw 'CGH0103';
                                        }
                                    })
                            )
                        })
                        .catch((catchError) => {
                            console.log('Error in bulkImportLocationHolidays .catch() block', catchError);
                            let errResult = commonLib.func.getError(catchError, 'CGH0125');
                            //Destroy DB connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            //Return error response
                            throw new ApolloError(errResult.message, errResult.code);
                        })
                );
            }
            else {
                throw 'IVE0000';
            }
        } else {
            console.log('No rights to add location holidays');
            throw '_DB0101';
        }
    } catch (mainCatchError) {
        console.log('Error in bulkImportLocationHolidays function main block', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        if (mainCatchError === 'IVE0000') {
            let errResult = commonLib.func.getError('', 'IVE0000');
            console.log('Validation error in bulkImportLocationHolidays function - ', validationError);
            // return response
            throw new UserInputError(errResult.message, { validationError: validationError });
        } else {
            let errResult = commonLib.func.getError(mainCatchError, 'CGH0009');
            // return response
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
}


async function addLocationHolidays(formedData, organizationDbConnection, trx) {
    // getting deep copy of holidayAssignedData
    let holidayAssignedData = JSON.parse(JSON.stringify(formedData))
    //Remove Employee_Id and Location_Id
    for (let i = 0; i < holidayAssignedData.length; i++) {
        delete holidayAssignedData[i].Employee_Id
        delete holidayAssignedData[i].Index
    }
    return (
        organizationDbConnection(ehrTables.holidayAssignment)
            .insert(holidayAssignedData)
            .transacting(trx)
            .then((data) => {
                if (!data) {
                    return false
                } else {
                    return true

                }
            })
    )
}


