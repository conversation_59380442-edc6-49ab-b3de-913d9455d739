{"securityGroupIds": ["sg-04bc41f567ae532b6"], "subnetIds": ["subnet-0cbbb2391d86f1d0e", "subnet-0120a712c5936a292"], "dbSecretName": "PROD/UPSHOT/PGACCESS", "region": "eu-west-2", "lambdaRole": "arn:aws:iam::327313496531:role/lambdaFullAccess", "dbPrefix": "upshothr_", "domainName": "upshothr", "authorizerARN": "arn:aws:lambda:eu-west-2:327313496531:function:ATS-upshothr-firebaseauthorizer", "customDomainName": "api.upshothr.uk", "emailFrom": "<EMAIL>", "emailTo": "<EMAIL>", "sesRegion": "us-west-2", "leaveStatusDomainName": "upshothr.uk", "documentsBucketCloudFront": "documents.upshothr.uk", "logoBucket": "s3.logos.upshothr.uk", "mailNotificationEngine": "arn:aws:states:eu-west-2:327313496531:stateMachine:upshothr-coreHrMailNotificationEngineStepFunction", "refreshCustomGroup": "arn:aws:states:eu-west-2:327313496531:stateMachine:upshothr-refreshCustomEmpGroupsStepFunction", "startUpdateWeekOffDateStepFunction": "arn:aws:states:eu-west-2:327313496531:stateMachine:upshothr-startUpdateWeekOffDateStepFunction", "bulkInviteEmployeesStepFunction": "arn:aws:states:ap-south-1:327313496531:stateMachine:upshothr-bulkInviteEmployeesStepFunction", "leaveClosureStepFunction": "arn:aws:states:eu-west-2:327313496531:stateMachine:upshothr-leaveClosureStepFunction", "webAddress": ".uk", "systemProcessStepFunction": "arn:aws:states:eu-west-2:327313496531:stateMachine:upshothr-systemProcessStepFunction", "supportEmail": "<EMAIL>", "resourceArnPrefix": "arn:aws:lambda:eu-west-2:327313496531:function:COREHR-upshothr", "commonStepFunction": "arn:aws:states:eu-west-2:327313496531:stateMachine:upshothr-commonStepFunction", "asyncSyntrumAPIStepFunction": "arn:aws:states:eu-west-2:327313496531:stateMachine:upshothr-asyncSyntrumAPIFunction", "processAirTicketSummary": "arn:aws:states:eu-west-2:327313496531:stateMachine:upshothr-processAirTicketSummary", "experiencePortalUrl": "https://#REPLACE_ORG_CODE_DOMAIN_NAME#/v3/candidate-portal"}