const { ehrTables } = require('@cksiva09/hrapp-corelib/common/tablealias');

//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// Require knex to get dbconnection
var knex = require('knex');
//Require moment
const moment = require('moment-timezone');

// Function to initiate startUpdateWeekOffDate step function
module.exports.startUpdateWeekOffDate  = async(args, context) =>{
    console.log('Inside startUpdateWeekOffDate function');
    let organizationDbConnection,appmanagerDbConnection;
    let emailFrom=process.env.emailFrom;
    let emailTo=process.env.emailTo;
    let sesRegion=process.env.sesRegion;
    let orgCode=args.orgCode;
    try{
        let databaseConnection=await commonLib.stepFunctions.getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,'',1);
        // check whether data exist or not
        if(Object.keys(databaseConnection).length){
            // form app manager database connection
            appmanagerDbConnection=knex(databaseConnection.AppManagerDb);
            let workScheduleId= args.workScheduleId;
            let orgRegionDetails = await commonLib.func.getOrgRegionDetails(appmanagerDbConnection,orgCode);
            appmanagerDbConnection?appmanagerDbConnection.destroy():null;
            if(orgRegionDetails && Object.keys(orgRegionDetails).length > 0){
                let additionalHeaders = await commonLib.stepFunctions.formAdditionalHeaders(orgRegionDetails);
                //Get database connection
                let connection=await commonLib.stepFunctions.getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,orgCode,0,additionalHeaders);
                if(Object.keys(connection).length>0){
                    organizationDbConnection = knex(connection.OrganizationDb);
                    await updateWeekOffDate(organizationDbConnection,workScheduleId)
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                }
            }
        }
        else{
            console.log("Error while getting database connection.")
        }
    }
    catch(mainCatchError){
        console.log('Error in startUpdateWeekOffDate function main block', mainCatchError);
        await sendErrorEmail(orgCode,emailFrom,emailTo,sesRegion,mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        appmanagerDbConnection?appmanagerDbConnection.destroy():null;
    }
}

async function updateWeekOffDate(organizationDbConnection,workScheduleId)
{
    try{
        let isWorkScheduleUpdated=0;
        let workScheduleDetails= await commonLib.func.getDataFromTableAccordingToStatus(organizationDbConnection,ehrTables.workSchedule,'WorkSchedule_Id',workScheduleId);
        if(workScheduleDetails && workScheduleDetails.length>0)
        {
            if(workScheduleDetails[0]['Updated_On']!=null)
            {
                isWorkScheduleUpdated=1;
            }
        }
        else{
            throw('Error while getting the work schedule details.');
        }
        //gives the timezone according to workschedule id
        let timeZone=await commonLib.func.getWorKScheduleTimeZoneId(organizationDbConnection,workScheduleId);
        if(timeZone && timeZone.length)
        {

            let startDate = moment().tz(timeZone[0]);
            let lastDateOfMonth=moment(startDate).endOf('month').add(1, 'days').format('YYYY-MM-DD');
            if(isWorkScheduleUpdated===0)
            {
                startDate=startDate.subtract(3, 'months');
                let year=startDate.format('YYYY');
                let month=startDate.format('MM');
                startDate=year+'-'+month+'-'+'01';
            }
            else{
                startDate=startDate.format('YYYY-MM-DD');
            }
            //function to get weekoff date in the range of start date and lastdate of month
            let dayWeekOfMonthDate=await commonLib.func.getDayWeekOfMonthFromDate(startDate,lastDateOfMonth);
            //function to delete existing weekoff date greater than or equal to current date i.e startdate
            let delWorkScheduleFromWeekOffDates= await commonLib.func.delWorkScheduleFromWeekOffDates(organizationDbConnection,workScheduleId,startDate);
            if(delWorkScheduleFromWeekOffDates && dayWeekOfMonthDate.length)
            {   
                //function to update the weekoff table
                let check=await commonLib.func.updateWeekOffDate(organizationDbConnection,dayWeekOfMonthDate,workScheduleId);
                if(check)
                {
                    return true;
                }
                else{
                    console.log("Error in updateWeekOffDate fucntion.")
                    throw("Error in updateWeekOffDate fucntion.")
                }

            }
            else{
                console.log("Error in delWorkScheduleFromWeekOffDates")
                throw('Error in delWorkScheduleFromWeekOffDates')
            }
        }
        else{
            console.log('Error Occure While getting timezone.')
            throw("Error Occure While getting timezone")
        }
    }
    catch(e){
        console.log("Error Occured while updating weekOff_Date Table inside addWeekOff",e);
        throw(e);
    }
}

//function to send error mail when error occured while updating the weekofdate table
async function sendErrorEmail(orgCode,emailFrom,emailTo,sesRegion,error)
{
    try{
        notificationParams = {
            'Source': emailFrom,
            'Destination': {
                'ToAddresses': [emailTo]
            },
            ReplyToAddresses: [],
            Message: {
                Body: {
                  Html: {
                    Charset: 'UTF-8',
                    Data: `Workschedule update for weekoff_table job failed for,${orgCode}.Error:${error}`,
                  },
                },
                Subject: {
                  Charset: 'UTF-8',
                  Data: `Workschedule update for weekoff_table job failed for,${orgCode}`,
                }
              }
        };

        let emailSend=await commonLib.func.sendEmailNotificationsWithoutTemplate(notificationParams,sesRegion);
        if(emailSend)
        {
            console.log("Mail is sent from addWeekof");
        }
        else{
            console.log("Error Occured while sending data email from addWeekOff.js");
        }

    }
    catch(e){
        console.log(e)
        console.log("Error Occured while sending data email from addWeekOff.js",e)
    }
}