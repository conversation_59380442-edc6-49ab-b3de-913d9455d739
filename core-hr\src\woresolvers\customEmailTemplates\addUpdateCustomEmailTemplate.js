// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../common/tablealias');
//Require apollo server to return error message
const { ApolloError, UserInputError } = require('apollo-server-lambda');
// require common constant files
const { formIds} = require('../../../common/appconstants');
//require input validator
const { validateCommonRuleInput } = require('../../../common/inputValidations');
//require moment
const moment = require('moment');
module.exports.addUpdateCustomEmailTemplate = async (parent, args, context, info) => {
    let validationError = {};
    let organizationDbConnection;
    try {
      const { Employee_Id: loginEmployeeId, Org_Code: orgCode, User_Ip: userIp } = context;
      organizationDbConnection = knex(context.connection.OrganizationDb);
      const checkRights = await commonLib.func.checkEmployeeAccessRights(
        organizationDbConnection,
        loginEmployeeId,
        '',
        '',
        'UI',
        false,
        formIds.customEmailTemplate
      );
  
      if (Object.keys(checkRights).length <= 0 || (args.templateId && checkRights.Role_Update === 0) || (!args.templateId && checkRights.Role_Add === 0)&& checkRights.Employee_Role.toLowerCase() === 'admin') {
        if(!args.templateId && checkRights.Role_Add === 0){
          throw '_DB0101';
        }
        else if(checkRights.Role_Update === 0 ){
          throw "_DB0102";
        }
        else{
          throw '_DB0109'
        }
      }
  
      const {
        templateId,
        templateName,
        templateContent,
        templateFields,
        categoryId,
        categoryTypeId,
        formId,
        visibility,
        toEmails,
        attachedFiles,
        ccEmails,
        bccEmails,
        subjectContent,
        subjectFields,
        additionalEmails,
        externalEmails,
        defaultTemplate,
        senderName,
      } = args;
      const fieldValidations = {
        templateName: 'IVE0527',
        subjectContent: 'IVE0528',
      };

      validationError = await validateCommonRuleInput(args, fieldValidations);
      if (Object.keys(validationError).length > 0) {
        throw 'IVE0000';
      }
      await validateDuplicateDefaultEntry(args,organizationDbConnection);
      const customEmailTemplateData = {
        Template_Name: templateName,
        Template_Content: templateContent,
        Subject_Content:subjectContent,
        Template_Fields: JSON.stringify(templateFields) || null,
        Subject_Fields: JSON.stringify(subjectFields) || null,
        Category_Id: categoryId,
        Category_Type_Id: categoryTypeId || null,
        Form_Id: formId,
        Visibility: visibility || 'Everyone',
        To_Emails: JSON.stringify(toEmails) || null,
        Attachments: JSON.stringify(attachedFiles) || null,
        CC_Emails: JSON.stringify(ccEmails) || null,
        Bcc_Emails: JSON.stringify(bccEmails) || null,
        Additional_Emails: JSON.stringify(additionalEmails) || null,
        External_Emails: JSON.stringify(externalEmails) || null,
        Default_Template: defaultTemplate? defaultTemplate:'No',
        Sender_Name: senderName
      };

      if (templateId) {
        customEmailTemplateData.Updated_On = moment().utc().format('YYYY-MM-DD HH:mm:ss');
        customEmailTemplateData.Updated_By = loginEmployeeId;
      } else {
        customEmailTemplateData.Added_On = moment().utc().format('YYYY-MM-DD HH:mm:ss');
        customEmailTemplateData.Added_By = loginEmployeeId;
      }
  
      const templateResult =
        templateId
          ? await organizationDbConnection(ehrTables.customEmailTemplates)
              .where('Template_Id', templateId)
              .update(customEmailTemplateData)
          : await organizationDbConnection(ehrTables.customEmailTemplates)
              .insert(customEmailTemplateData);
  
      if (!templateResult) throw 'CET00004';
  
      await commonLib.func.createSystemLogActivities({
        userIp,
        employeeId: loginEmployeeId,
        organizationDbConnection,
        message: `Custom Email Template ${templateName} ${args.templateId ? 'updated' : 'added'} successfully`,
      });
  
      if (organizationDbConnection) organizationDbConnection.destroy();
  
      return {
        errorCode: '',
        message: `Custom Email Template ${args.templateId ? 'updated' : 'added'} successfully`,
        templateId: templateId || templateResult[0],
      };
    } catch (error) {
      if (organizationDbConnection) organizationDbConnection.destroy();
      if (error === 'IVE0000') {
        console.log('Validation error in addUpdateCustomEmailTemplate function', validationError);
        const errResult = commonLib.func.getError('', 'IVE0000');
        throw new UserInputError(errResult.message, { validationError });
      } else {
        let errorCode = error.code === 'ER_DUP_ENTRY'?'CET00003': error
        const errResult = commonLib.func.getError(errorCode, 'CET00002');
        throw new ApolloError(errResult.message);
      }
    }
  };

  const validateDuplicateDefaultEntry = async (data, organizationDbConnection) => {
    try {
      const { categoryId, categoryTypeId ,templateId,defaultTemplate} = data;

      if (defaultTemplate?.toLowerCase() === 'yes') {
        const existingDefault = await organizationDbConnection(ehrTables.customEmailTemplates)
          .where('Category_Id', categoryId)
          .andWhere('Default_Template', 'Yes')
          .modify((query) => {
            if(categoryTypeId){
              query.andWhere('Category_Type_Id',categoryTypeId);
            }
            if (templateId) {
              query.andWhere('Template_Id', '!=', templateId);
            }
          })
          .first();

        if (existingDefault) {
          throw 'CET00008';
        }
      }
    } catch (error) {
      console.log('Error in validateDuplicateDefaultEntry', error);
      throw error;
    }
  };
