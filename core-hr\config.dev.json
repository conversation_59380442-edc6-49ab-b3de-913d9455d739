{"securityGroupIds": ["sg-098a0324de5632c9e", "sg-03b2b753038f67293"], "subnetIds": ["subnet-020dff85cddc0752e", "subnet-0b1a6fbc2c7b782ad"], "dbSecretName": "hrapp-stage", "region": "ap-south-1", "lambdaRole": "arn:aws:iam::692647644057:role/lambdaFullAccess", "dbPrefix": "hrapp_", "domainName": "hrapp", "authorizerARN": "arn:aws:lambda:ap-south-1:692647644057:function:ATS-dev-firebaseauthorizer", "customDomainName": "api.hrapp.co.in", "emailFrom": "<EMAIL>", "emailTo": "<EMAIL>", "sesRegion": "us-east-1", "leaveStatusDomainName": "hrapp.co.in", "documentsBucketCloudFront": "documents.hrapp.co.in", "logoBucket": "s3.hrapp-dev-public-images", "mailNotificationEngine": "arn:aws:states:ap-south-1:692647644057:stateMachine:dev-coreHrMailNotificationEngineStepFunction", "refreshCustomGroup": "arn:aws:states:ap-south-1:692647644057:stateMachine:dev-refreshCustomEmpGroupsStepFunction", "startUpdateWeekOffDateStepFunction": "arn:aws:states:ap-south-1:692647644057:stateMachine:dev-startUpdateWeekOffDateStepFunction", "bulkInviteEmployeesStepFunction": "arn:aws:states:ap-south-1:692647644057:stateMachine:dev-bulkInviteEmployeesStepFunction", "leaveClosureStepFunction": "arn:aws:states:ap-south-1:692647644057:stateMachine:dev-leaveClosureStepFunction", "webAddress": ".co.in", "systemProcessStepFunction": "arn:aws:states:ap-south-1:692647644057:stateMachine:dev-systemProcessStepFunction", "supportEmail": "<EMAIL>", "resourceArnPrefix": "arn:aws:lambda:ap-south-1:692647644057:function:COREHR-dev", "commonStepFunction": "arn:aws:states:ap-south-1:692647644057:stateMachine:dev-commonStepFunction", "asyncSyntrumAPIStepFunction": "arn:aws:states:ap-south-1:692647644057:stateMachine:dev-asyncSyntrumAPIFunction", "processAirTicketSummary": "arn:aws:states:ap-south-1:692647644057:stateMachine:dev-processAirTicketSummary", "experiencePortalUrl": "https://#REPLACE_ORG_CODE_DOMAIN_NAME#/v3/candidate-portal"}