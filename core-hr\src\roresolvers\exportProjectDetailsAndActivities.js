// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const {formIds } = require('../../common/appconstants');
const {getProjectCoverage}=require('../../common/commonfunctions')


const retrieveActivityData = (organizationDbConnection, projectCoverage, args) => {
    try {
        return organizationDbConnection(ehrTables.projectDetails + " as PD")
        .select('PD.Project_Id', 'PD.Project_Name as projectName', 'PD.Description as description', 'PD.Client_Name as clientName', 'PD.Status as status', 'L.Location_Name as locationName', 'PD.Manager_Id as managerId',
        organizationDbConnection.raw("CONCAT_WS(' ',EPI1.Emp_First_Name,EPI1.Emp_Middle_Name, EPI1.Emp_Last_Name) as managerName"),
        'PD.Added_On as addedOn',
        organizationDbConnection.raw("CONCAT_WS(' ',EPI.Emp_First_Name,EPI.Emp_Middle_Name, EPI.Emp_Last_Name) as addedByName"),
        'PD.Updated_On as updatedOn',
        organizationDbConnection.raw("CONCAT_WS(' ',EPI2.Emp_First_Name,EPI2.Emp_Middle_Name, EPI2.Emp_Last_Name) as updatedByName"),
        'AM.Activity_Name as activityName', 'AM.Is_Billable as isBillable', 'AM.Description as activityDescription', 'PA.Activity_From as activityFrom', 'PA.Activity_To as activityTo')
        .whereIn('PD.Project_Id', args.projectId)
        .leftJoin(ehrTables.location + " as L", "PD.Location_Id", "L.Location_Id")
        .leftJoin(ehrTables.empPersonalInfo + " as EPI1", "EPI1.Employee_Id", "PD.Manager_Id")
        .leftJoin(ehrTables.empPersonalInfo + " as EPI", "EPI.Employee_Id", "PD.Added_By")
        .leftJoin(ehrTables.empPersonalInfo + " as EPI2", "EPI2.Employee_Id", "PD.Updated_By")
        .leftJoin(ehrTables.projectActivities + " as PA", "PD.Project_Id", "PA.Project_Id")
        .leftJoin(ehrTables.activitiesMaster + " as AM", "PA.Activity_Id", "AM.Activity_Id")
        .modify(function (queryBuilder) {
            if(args.offset){
                this.offset(args.offset)
            }
            if(args.limit){
                this.limit(args.limit)
            }
           if (projectCoverage && projectCoverage.toLowerCase() === "customgroup") {
                queryBuilder.select('CEG.Group_Name as customGroupName')
                queryBuilder.innerJoin(ehrTables.customGroupAssociated + " as CGAF", "CGAF.Parent_Id", "PD.Project_Id")
                queryBuilder.leftJoin(ehrTables.cusEmpGroup + " as CEG", "CEG.Group_Id", "CGAF.Custom_Group_Id")
                queryBuilder.where("CGAF.Form_Id", formIds.projects)
                queryBuilder.groupBy('PD.Project_Id');
            }
            else{
                queryBuilder.select(organizationDbConnection.raw(" CONCAT('') AS customGroupName"))
            }
        })
        .then(projects => {
            if (projects.length === 0) {
                // Handle the case where no projects are retrieved
                return [];
            }
            return projects;
        });
    } catch (error) {
        console.log('Error in retrieveActivityData function .catch block',error);
        throw error;
    }
};


module.exports.exportProjectDetailsAndActivities = async (parent, args, context, info) => {
    console.log('Inside exportProjectDetailsAndActivities function');
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let loginEmployeeId = context.Employee_Id;
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, null, '', 'UI', false, formIds.projects);
        if (Object.keys(checkRights).length > 0 && checkRights.Role_View === 1) {
            let projectCoverage = await getProjectCoverage(organizationDbConnection);
            if(projectCoverage){
                const data = await retrieveActivityData(organizationDbConnection, projectCoverage, args);
                const result = data.reduce((acc, curr) => {
                    const existingProject = acc.find(proj => proj.Project_Id === curr.Project_Id);
                    if (existingProject) {
                      existingProject.activities.push({
                        activityName: curr.activityName,
                        isBillable: curr.isBillable,
                        activityDescription: curr.activityDescription,
                        activityFrom: curr.activityFrom,
                        activityTo: curr.activityTo
                      });
                    } else {
                      acc.push({
                        Project_Id: curr.Project_Id,
                        projectName: curr.projectName,
                        description: curr.description,
                        clientName: curr.clientName,
                        status: curr.status,
                        locationName: curr.locationName,
                        managerId: curr.managerId,
                        managerName: curr.managerName,
                        addedOn: curr.addedOn,
                        addedByName: curr.addedByName,
                        updatedOn: curr.updatedOn,
                        updatedByName: curr.updatedByName,
                        customGroupName: curr.customGroupName,
                        activities: [{
                          activityName: curr.activityName,
                          isBillable: curr.isBillable,
                          activityDescription: curr.activityDescription,
                          activityFrom: curr.activityFrom,
                          activityTo: curr.activityTo
                        }]
                      });
                    }
                    return acc;
                  }, []);
                // Destroy DB connection
                organizationDbConnection && organizationDbConnection.destroy();
                let jsonData = JSON.stringify(result);
                return {
                    errorCode: '',
                    message: 'Project details and their activities have been retrieved successfully.',
                    projectCoverage: projectCoverage,
                    projectDetailsAndActivities: jsonData
                };
            }else{
                console.log("Project settings data does not exists");
                throw('CHR0002');
            }
        } else {
            console.log('Employee does not have view access rights');
            throw '_DB0100';
        }
    } catch (e) {
        // Destroy DB connection
        organizationDbConnection && organizationDbConnection.destroy();
        console.log('Error in exportProjectDetailsAndActivities function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'CHR0086');
        throw new ApolloError(errResult.message, errResult.code);
    }
};