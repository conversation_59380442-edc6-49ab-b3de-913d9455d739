const { ehrTables } = require('../../common/tablealias');

//this function get all the configuration which need to be processed based on the configuration setting but it will not check for the advance notification.
async function getConfigurationIdBasedOnWeekDayAndMonthDay(organizationDbConnection,triggerTimeMin,triggerTimeMax,weekDay,monthDay)
{
    try{
        return(
            organizationDbConnection(ehrTables.notificationEngineEventMaster)
            .select('NEC.Configuration_Id as configurtionId','NEC.Send_In_Group as sendInGroup','NEC.Group_Id as groupId','NEC.Frequency as frequency',
            'NEC.Advance_Notificaion as advanceNotificaion','NEC.Period as period','NEC.No_Of_Days as noOfDays','NEC.Send_To as sendTo',
            'NEC.Send_To_Other_Email_CC as sendToOtherEmailCC','NEC.Subject as subject','NEC.Body_Info as bodyInfo','NEC.Key_Info as keyInfo',
            'NEC.Template_Name as templateName', 'NEEM.Event_Name as eventName')
            .from(ehrTables.notificationEngineEventMaster +' as NEEM')
            .leftJoin(ehrTables.notificationEngineConfiguration + " as NEC",'NEC.Event_Id','NEEM.Event_Id')
            .leftJoin(ehrTables.notificationTriggerTime + " as NTT",'NEC.Configuration_Id','NTT.Configuration_Id')
            .where('NEEM.Event_Type','Offline')
            .where(function() {
                this.whereBetween('NTT.Trigger_Time',[triggerTimeMin,triggerTimeMax]).orWhereBetween('NEC.Default_Trigger_Time',[triggerTimeMin,triggerTimeMax])
            })
            .where(function() {
                this.where("NEC.Frequency","Daily")
                .orWhere(function() {
                    this.where("NEC.Frequency","Monthly").andWhere("NEC.Month_Day_No",monthDay)
                })
                .orWhere(function() {
                    this.where("NEC.Frequency","Weekly").andWhere("NEC.Week_Day_No",weekDay)
                })
            })
            .where("NEC.Notification_Enabled",'Yes')
            .groupBy('NEC.Configuration_Id')
            .then(data=>{
                return data;
            })
            .catch(e=>{
                console.log("Error in getConfigurationIdBasedOnWeekDayAndMonthDay .catch block",e);
                return false;
            })
        )
    }
    catch(e)
    {
        console.log("Error in getConfigurationIdBasedOnWeekDayAndMonthDay main catch block",e);
        return false;
    }
}

async function getNotificationGroupDetails(organizationDbConnection)
{
    try{
        return(
            organizationDbConnection(ehrTables.notificationEngineGroup)
            .select('*')
            .then(data=>{
                return data;
            })
            .catch(e=>{
                console.log('Error in getNotificationGroupDetails .catch block',e);
                return false;
            })
        )
    }
    catch(e)
    {
        console.log('Error in getNotificationGroupDetails main block',e);
        return false;
    } 
}

module.exports={
    getConfigurationIdBasedOnWeekDayAndMonthDay,
    getNotificationGroupDetails
}