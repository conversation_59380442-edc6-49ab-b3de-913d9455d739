//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const { validateWithRulesAndReturnMessages, validateStaticFieldsAndReturnMessages } = require('@cksiva09/validationlib/src/validator');
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { UserInputError, ApolloError } = require('apollo-server-lambda');
//Require moment
let moment = require('moment');
//Require table alias
const { ehrTables } = require('../../../../common/tablealias');
//Require constants
const { formName, systemLogs } = require('../../../../common/appconstants');

//Function to add/update business unit
module.exports.addUpdateBusinessUnit = async (parent, args, context, info) => {
    console.log('Inside addUpdateBusinessUnit function');
    let organizationDbConnection,
        errResult,
        validationError = {};
    try {
        let loginEmployeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, formName.businessUnit, '', 'UI');
        let isAdd = args.isAdd;
        if (Object.keys(checkRights).length > 0 && ((isAdd === 1 && checkRights.Role_Add === 1)
            || (isAdd === 0 && checkRights.Role_Update === 1))) {
            let businessUnitId = args.businessUnitId;
            let businessUnitName = args.businessUnit;
            let status = args.status;
            let level = args.level;
            let description = args.description ? args.description : '';
            let oldStatus = args.oldStatus ? args.oldStatus : '';
            let businessUnitCode = args.businessUnitCode ? args.businessUnitCode : null;
            let businessUnitParentId = args.businessUnitParentId ? args.businessUnitParentId : 0;

            validationError = await validateInputs(args);
            //Check validation error exist or not
            if (Object.keys(validationError).length === 0) {
                //Validate the business unit name already exist or not
                let validateBusinessNameQuery = organizationDbConnection(ehrTables.businessUnit)
                    .pluck('Business_Unit')
                    .where('Business_Unit', businessUnitName);

                //If the action is edit, then validate for the business unit id other than input business unit record
                if (args.isAdd === 0) {
                    validateBusinessNameQuery = validateBusinessNameQuery.whereNot('Business_Unit_Id', businessUnitId);
                }
                return (
                    organizationDbConnection
                        .transaction(async (trx) => {
                            return (
                                validateBusinessNameQuery
                                    .transacting(trx)
                                    .then(async (settings) => {
                                        //If the business unit name is not duplicated in other records
                                        if (settings && settings.length === 0) {
                                            let businessUnitAssociated = 0;

                                            //Validate the business unit is associated with the employee records while inactivating the business unit
                                            if (status === 'InActive' && oldStatus === 'Active') {
                                                businessUnitAssociated = await isBusinessUnitAssociated(organizationDbConnection, businessUnitId, 'edit');
                                            }
                                            if (businessUnitAssociated === 0) {
                                                let insertUpdateDetails = {
                                                    Business_Unit_Id: businessUnitId,
                                                    Business_Unit: businessUnitName,
                                                    Business_Unit_Status: status,
                                                    Description: description,
                                                    Business_Unit_Code: businessUnitCode,
                                                    Business_Unit_Parent_Id: businessUnitParentId,
                                                    Level: level ? level : null
                                                };

                                                //Log message: `System Log-Action` `Form Name` - `Business Unit Name`
                                                let systemLogParam = {
                                                    userIp: context.User_Ip,
                                                    employeeId: loginEmployeeId,
                                                    formName: formName.businessUnit,
                                                    trackingColumn: '',
                                                    organizationDbConnection: organizationDbConnection,
                                                    uniqueId: businessUnitName
                                                };

                                                if (isAdd === 1) {
                                                    systemLogParam.action = systemLogs.roleAdd;

                                                    insertUpdateDetails.Added_On = moment().utc().format('YYYY-MM-DD HH:mm:ss');
                                                    insertUpdateDetails.Added_By = loginEmployeeId;
                                                    return (
                                                        organizationDbConnection(ehrTables.businessUnit)
                                                            .insert(insertUpdateDetails)
                                                            .then(async (insertSettings) => {
                                                                if (insertSettings) {
                                                                    //Call the function to add the system log
                                                                    await commonLib.func.createSystemLogActivities(systemLogParam);
                                                                    if (context.partnerid && context.partnerid.toLowerCase() === 'entomo') {
                                                                        insertUpdateDetails.Code = insertUpdateDetails.Business_Unit_Code
                                                                        insertUpdateDetails.Name = insertUpdateDetails.Business_Unit

                                                                        let inputData = {
                                                                            'orgCode': context.Org_Code, 'partnerId': context.partnerid, 'inputParams': {
                                                                                entityId: insertSettings,
                                                                                entityType: 'Business Unit',
                                                                                functionName: 'entomoSync',
                                                                                action: 'Add',
                                                                                formData: insertUpdateDetails
                                                                            }
                                                                        }
                                                                        //Trigger Entomo Sync
                                                                        await commonLib.stepFunctions.triggerStepFunction(process.env.commonStepFunction, 'commonStepFunction', '', inputData);
                                                                    }
                                                                    return "success";
                                                                } else {
                                                                    throw 'SBU0101'
                                                                }
                                                            })
                                                    )
                                                } else {
                                                    //Validate the entomo related data update
                                                    if (context.partnerid && context.partnerid.toLowerCase() === 'entomo') {
                                                        isEntomoDataChanged = await isEntomoRelatedDataChanged(organizationDbConnection, insertUpdateDetails);
                                                    }
                                                    systemLogParam.action = systemLogs.roleUpdate;

                                                    insertUpdateDetails.Updated_On = moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                                                        insertUpdateDetails.Updated_By = loginEmployeeId;
                                                    return (
                                                        organizationDbConnection(ehrTables.businessUnit)
                                                            .update(insertUpdateDetails)
                                                            .where('Business_Unit_Id', businessUnitId)
                                                            .then(async (updateSettings) => {
                                                                if (updateSettings) {
                                                                    //Call the function to add the system log
                                                                    await commonLib.func.createSystemLogActivities(systemLogParam);

                                                                    if (context.partnerid && context.partnerid.toLowerCase() === 'entomo' && isEntomoDataChanged) {
                                                                        insertUpdateDetails.Code = insertUpdateDetails.Business_Unit_Code
                                                                        insertUpdateDetails.Name = insertUpdateDetails.Business_Unit
                                                                        insertUpdateDetails.Old_Code = isEntomoDataChanged
                                                                        insertUpdateDetails.Level = insertUpdateDetails.Level
                                                                        
                                                                        let inputData = {
                                                                            'orgCode': context.Org_Code, 'partnerId': context.partnerid, 'inputParams': {
                                                                                entityId: insertUpdateDetails.Business_Unit_Id,
                                                                                entityType: 'Business Unit',
                                                                                functionName: 'entomoSync',
                                                                                action: 'Update',
                                                                                formData: insertUpdateDetails
                                                                            }
                                                                        }
                                                                        //Trigger Entomo Sync
                                                                        await commonLib.stepFunctions.triggerStepFunction(process.env.commonStepFunction, 'commonStepFunction', '', inputData);
                                                                    }
                                                                    return "success";
                                                                } else {
                                                                    throw 'SBU0101'
                                                                }
                                                            })
                                                    )
                                                }
                                            } else {
                                                throw 'SBU0102';
                                            }
                                        } else {
                                            throw 'SBU0103';
                                        }
                                    })
                            )
                        })
                        .then(() => {
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            return { errorCode: "", message: ((isAdd === 1) ? 'Business unit has been added successfully.' : 'Business unit has been updated successfully.') };
                        })
                        .catch((catchError) => {
                            console.log('Error in addUpdateBusinessUnit .catch() block', catchError);
                            let errResult = commonLib.func.getError(catchError, 'SBU0101');
                            //Destroy DB connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            //Return error response
                            throw new ApolloError(errResult.message, errResult.code);
                        })
                )
            } else {
                //Throw validation error
                throw ('IVE0000');
            }
        } else {
            console.log('Login employee id does not have add or update access to business unit form.');
            throw '_DB0111';
        }
    } catch (mainCatchError) {
        console.log('Error in addUpdateBusinessUnit function main block', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        if (mainCatchError === 'IVE0000') {
            errResult = commonLib.func.getError('', 'IVE0000');
            throw new UserInputError(errResult.message, { validationError: validationError });
        } else {
            errResult = commonLib.func.getError(mainCatchError, 'SBU0001');
            throw new ApolloError(errResult.message, errResult.code);//Return response
        }
    }
}

/**
 * Function to check if entomo related data is changed
 * @param {knex} organizationDbConnection - DB connection
 * @param {Object} businessUnitData - businessUnit data
 * @returns {Boolean} - true if data is changed else false
 */
async function isEntomoRelatedDataChanged(organizationDbConnection, businessUnitData) {
    try {
        //Get the current designation details
        let currentBusinessUnitData = await organizationDbConnection
            .select('Business_Unit_Code as Old_Code', 'Business_Unit', 'Description')
            .from(ehrTables.businessUnit)
            .where('Business_Unit_Id', businessUnitData.Business_Unit_Id)
            .first();

        //Check if the data is changed
        if (currentBusinessUnitData.Old_Code !== businessUnitData.Business_Unit_Code
            || currentBusinessUnitData.Business_Unit !== businessUnitData.Business_Unit
            || currentBusinessUnitData.Description !== businessUnitData.Description
        ) {
            return currentBusinessUnitData.Old_Code
        } else {
            return false
        }
    }
    catch (err) {
        console.log('Error in isEntomoRelatedDataChanged function', err);
        return false
    }
}

//Function to validate the business unit is associated with the employee or not
async function isBusinessUnitAssociated(organizationDbConnection, businessUnitId, action) {
    try {
        let businessUnitAssociatedQuery = organizationDbConnection(ehrTables.empJob)
            .pluck('Employee_Id')
            .where("Business_Unit_Id", businessUnitId);

        //While inactivating the status in edit, we need to validate the emp status
        //While deleting the business unit, business unit should not be associated to active and inactive employees
        if (action === 'edit') {
            businessUnitAssociatedQuery = businessUnitAssociatedQuery.where('Emp_Status', 'Active');
        }
        return (
            businessUnitAssociatedQuery
                .then((associatedResult) => {
                    if (associatedResult && associatedResult.length > 0) {
                        return 1;
                    } else {
                        return 0;
                    }
                })
        )
    } catch (err) {
        console.log('Error in isBusinessUnitAssociated function main catch block.', err)
        throw err;
    }
}

//Function to validate the inputs
async function validateInputs(args) {
    let validationError = {};
    try {
        let displayFields = {
            businessUnit: 'Business Unit',
            businessUnitCode: 'Business Unit Code',
            status: 'Status',
            description: 'Description',
            level: 'Level'
        };
        let businessUnitValidation = validateWithRulesAndReturnMessages(args.businessUnit, 'businessUnit', displayFields.businessUnit);
        if (businessUnitValidation !== true && businessUnitValidation !== 'Validation not found') {
            validationError[displayFields.businessUnit] = businessUnitValidation;
        }

        if (args.businessUnitCode) {
            let businessUnitCodeValidation = validateWithRulesAndReturnMessages(args.businessUnitCode, 'businessUnitCode', displayFields.businessUnitCode);
            if (businessUnitCodeValidation !== true && businessUnitCodeValidation !== 'Validation not found') {
                validationError[displayFields.businessUnitCode] = businessUnitCodeValidation;
            }
        }

        let businessUnitStatusValidation = validateStaticFieldsAndReturnMessages(args.status, 'activeInActive', displayFields.status);
        if (businessUnitStatusValidation !== true && businessUnitStatusValidation !== 'Validation not found') {
            validationError[displayFields.status] = businessUnitStatusValidation;
        }

        if (args.description) {
            let descriptionValidation = validateWithRulesAndReturnMessages(args.description, 'description', displayFields.description);
            if (descriptionValidation !== true && descriptionValidation !== 'Validation not found') {
                validationError[displayFields.description] = descriptionValidation;
            }
        }
        if(args.level !== null && args.level !== undefined){
            let levelValidation = validateWithRulesAndReturnMessages(args.level, 'level', displayFields.level, true);
            if (levelValidation !== true && levelValidation !== 'Validation not found') {
                validationError[displayFields.level] = levelValidation;
            }
        }
        return validationError
    } catch (e) {
        console.log('Error in the validateInputs function main catch block.', e);
        throw e;
    }
}