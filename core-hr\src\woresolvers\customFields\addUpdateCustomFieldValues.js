// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../common/tablealias');
//Require apollo server to return error message
const { ApolloError, UserInputError } = require('apollo-server-lambda');
// require common constant files
const { formName, systemLogs } = require('../../../common/appconstants');
//require moment
const moment = require('moment');

//function to add/update the custom field value
let organizationDbConnection;
let inputValidationError = {}
module.exports.addUpdateCustomFieldValues = async (parent, args, context, info) => {
    try {
        console.log("Inside addUpdateCustomFieldValues function.")
        let loginEmployeeId = context?.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);

        // Access Check
        if (args.Form_Id) {
            if (!process.env.endPoint || process.env.endPoint?.toLowerCase() !== 'external') {
                let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, null, '', 'UI', false, args.Form_Id);
                if (Object.keys(checkRights).length === 0 || (checkRights.Role_Add === 0 && checkRights.Role_Update === 0)) {
                    throw ('_DB0111');
                }
            }
        } else {
            //Form Id does not exist
            throw '_DB0104'
        }

        //Input validation
        inputValidationError = performInputValidations(args);
        if (inputValidationError && Object.keys(inputValidationError)?.length) {
            throw 'IVE0000'
        }

        // Parse Field Value
        let parseFieldValue = JSON.parse(args.Field_Value);

        // Step 1: Retrieve dynamic fields data table name
        let dynamicFieldsDataTable = await organizationDbConnection(ehrTables.customFieldTables)
            .where('Form_Id', args.Form_Id)
            .first();

        dynamicFieldsDataTable = dynamicFieldsDataTable ? dynamicFieldsDataTable.Table_Name : null;

        if (!dynamicFieldsDataTable) {
            console.log('No dynamic fields data table found for the form.');
            throw ('CF00004');
        }

        // Step 2: Retrieve previous custom field data
        let customFieldData = await organizationDbConnection(dynamicFieldsDataTable)
            .select('Custom_Field_Value')
            .where('Primary_Id', args.Primary_Id)
            .first();

        if (customFieldData) {
            customFieldData = JSON.parse(customFieldData.Custom_Field_Value);

            parseFieldValue = { ...customFieldData, ...parseFieldValue }

            // Step 3: Update custom field data
            await organizationDbConnection(dynamicFieldsDataTable)
                .where('Primary_Id', args.Primary_Id)
                .update({
                    Custom_Field_Value: JSON.stringify(parseFieldValue),
                    Updated_By: loginEmployeeId,
                    Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss')
                });
        } else {
            // Step 3: Add custom field data
            await organizationDbConnection(dynamicFieldsDataTable)
                .insert({
                    Primary_Id: args.Primary_Id,
                    Custom_Field_Value: JSON.stringify(parseFieldValue),
                    Updated_By: loginEmployeeId,
                    Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss')
                });
        }


        // Add System Log
        let systemLogParams = {
            action: systemLogs.roleUpdate,
            userIp: context.User_Ip,
            employeeId: loginEmployeeId,
            formId: args.Form_Id,
            formName: formName?.customFields,
            organizationDbConnection: organizationDbConnection,
            message: ` ${args.Primary_Id} Custom Field data updated successfully.`,
            isEmployeeTimeZone: 0,
            changedData: parseFieldValue
        };

        //Call function to add the system log
        await commonLib.func.createSystemLogActivities(systemLogParams);

        return { errorCode: "", message: `Custom Field data updated successfully.` };

    }
    catch (e) {
        console.log('Error in addUpdateCustomFieldValues function main catch block.', e);
        if (e === 'IVE0000') {
            let errResult = commonLib.func.getError('', 'IVE0000');
            console.log('Validation error in addUpdateCustomFieldValues function - ', inputValidationError);
            throw new UserInputError(errResult.message, { validationError: inputValidationError });
        } else {
            let errResult = commonLib.func.getError(e, 'CF00004');
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
    finally {
        //Destroy DB connection
        if (organizationDbConnection) {
            organizationDbConnection.destroy();
        }
    }
}


/**
 * Function to perform input validations for add/update custom field value API
 * @param {Object} args - Args passed to the API
 * @returns {Object} errors - A JSON object of errors, if any
 * @throws {Error} - If any error occurs while performing input validations
 */
const performInputValidations = (args) => {
    try {
        let errors = {};

        // Basic Field Validation
        if (!args.Primary_Id) errors['IVE0523'] = 'Primary Id is required';
        if (!args.Form_Id) errors['IVE0524'] = 'Form Id is required';

        // Field Value Validation
        if (!args.Field_Value?.length) errors['IVE0525'] = 'Field Value is required';

        // Validate Parsing
        let parseFieldValue = JSON.parse(args.Field_Value);
        if (!parseFieldValue || !Object.keys(parseFieldValue).length) {
            errors['IVE0525'] = 'Field Value is required';
        }

        return errors;
    } catch (e) {
        console.log('Error in performInputValidations', e);
        throw e;
    }
};
