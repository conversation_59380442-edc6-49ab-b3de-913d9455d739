// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formName, formIds, systemLogs } = require('../../../common/appconstants');
const { validateCommonRuleInput } = require('../../../common/inputValidations');
const moment = require('moment-timezone');


module.exports.importProjectActivities = async (parent, args, context, info) => {
    let organizationDbConnection;
    try {
        console.log("Inside importProjectActivities function.")
        let employeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);

        // check their rights
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, employeeId, null, '', 'UI', false, formIds.projects);
        if (Object.keys(checkRights).length <= 0 || checkRights.Role_Add !== 1) {
            throw '_DB0101';
        } else {
            let projectActivityData = args.projectActivityData

            const [sameDataFails, nameAndDescriptionFails] = await Promise.all([
                validateProjectActivitySameData(organizationDbConnection, projectActivityData),
                validateNameAndDescription(organizationDbConnection, projectActivityData)
            ])
            if (sameDataFails.length || nameAndDescriptionFails.length) {
                projectActivityData = projectActivityData.filter((data) => {
                    let existingData = {
                        "activityName": data["activityName"],
                        "isBillable": data["isBillable"],
                        "description": data["description"],
                    };
                    if(sameDataFails.length){
                        var isSameData = sameDataFails[0].failedArrays.some((failedData) => {
                            const isEqual = areObjectsEqual(failedData, existingData);
                            return isEqual;
                        });
                    }
                    if(nameAndDescriptionFails.length){
                        var isAmount = nameAndDescriptionFails.some((failedData) => {
                            const isEqual = areObjectsEqual(failedData.failedArrays[0], existingData);
                            return isEqual;
                        });
                    }
                    if (!isSameData && !isAmount) {
                        return data;
                    }
                });
            }
            if (projectActivityData.length) {
                var modifiedData = projectActivityData.map(activity => {
                    const {activityName, isBillable, description} = activity;
                    return {
                        Activity_Name: activityName,
                        Is_Billable:isBillable,
                        Description: description,
                    };
                });
                modifiedData = modifiedData.map(activity => ({
                    ...activity,
                    Added_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                    Added_By: employeeId
                }));
                
                //Insert the data into the table
                    let declarations = await organizationDbConnection(ehrTables.activitiesMaster).insert(modifiedData);
                    let systemLogParam = {
                        userIp: context.User_Ip,
                        employeeId: employeeId,
                        organizationDbConnection: organizationDbConnection,
                        message: `Project activity was imported ${declarations.toString()}`
                    };
                    await commonLib.func.createSystemLogActivities(systemLogParam);
            }

            // Form the failed data - Merge sameDataFails and nameAndDescriptionFails fails
           let failedData = [...sameDataFails, ...nameAndDescriptionFails];

            organizationDbConnection ? organizationDbConnection.destroy() : null;
            return { errorCode: "", message: "Project activities was imported successfully", validationError: JSON.stringify(failedData) };

        }

    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in importProjectActivities function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'CHR0079');
        throw new ApolloError(errResult.message, errResult.code);
    }
}

async function validateProjectActivitySameData(organizationDbConnection, projectActivityData) {
    try {
        let failedData = []

        //Validation to check if data already exists
        let sameActivityData = await organizationDbConnection(ehrTables.activitiesMaster)
            .whereIn(
                'Activity_Name',
                projectActivityData.map(entry => [entry.activityName])
            )
        //If same data already exists
        if (sameActivityData.length) {
            var validationFailedData = {
                Message: 'Same project activity already exists.',
                failedArrays: []
            }
            //Get the index of projectActivityData and add it to the array
            sameActivityData.forEach(element => {
                let data = projectActivityData.filter(el => el.activityName === element.Activity_Name);
                if(data && data.length){
                    validationFailedData.failedArrays = validationFailedData.failedArrays.concat(data);
                }
            });
            failedData.push(validationFailedData);
        }
        return failedData

    } catch (err) {
        console.log("Error in validateProjectActivitySameData function", err)
        throw err
    }
}

async function validateNameAndDescription(organizationDbConnection, projectActivityData) {
    try {
        let failedData = []
        let validationError = {};
        const fieldValidations = {};
        for (let i = 0; i < projectActivityData.length; i++) {
            let data = projectActivityData[i];
            fieldValidations.activityName = "IVE0417";
            if (fieldValidations.hasOwnProperty('description')) {
                delete fieldValidations.description;
            }
            if(data.description != null){
                fieldValidations.description = "IVE0415"
            }
            validationError = await validateCommonRuleInput(data, fieldValidations);
            if (Object.keys(validationError).length) {
                let validationFailedData = {
                    Message: validationError,
                    failedArrays: []
                }
                validationFailedData.failedArrays.push(data);
                failedData.push(validationFailedData);
            }
        }
        return failedData;

    } catch (err) {
        console.log("Error in validateNameAndDescription function", err)
        throw err
    }
}

// Function to compare objects
function areObjectsEqual(obj1, obj2) {
    const keys1 = Object.keys(obj1);
    const keys2 = Object.keys(obj2);

    const commonKeys = keys1.filter(key => keys2.includes(key));

    for (let key of commonKeys) {
        if (obj1[key] !== obj2[key]) {
            return false;
        }
    }

    return true;
}