# defining custom data type
scalar Date

type Query {
  listWorkScheduleDetails: listWorkScheduleDetailsResponse!
}

type Mutation {
  addWorkSchedule(
    workScheduleCode: String
    workSchedule: String!
    overlappingDays: Int!
    businessHoursStartTime: String!
    businessHoursEndTime: String!
    breakScheduleStartTime: String!
    breakScheduleEndTime: String!
    shiftMarginStartTimeInHours: Float!
    shiftMarginEndTimeInHours: Float!
    timezoneId: Int!
    targetEffortInHoursPerWeek: Float!
    regularHoursPerDay: Float!
    overtimeHoursPerDay: Float!
    description: String
    overTimeCoolingPeriod: Int!
    allowAttendanceOutsideRegularHours: Int!
    earlyCheckInOverride: Int!
    enableGraceTime: Int!
    gracePeriodForCheckIn: Int!
    thresholdOverTimeHoursPerDay: Float
    checkoutBufferTime: Int!
    status: String!
  ): addWorkScheduleResponse!

  updateWorkSchedule(
    workScheduleCode: String
    workScheduleId: Int!
    workSchedule: String!
    overlappingDays: Int!
    businessHoursStartTime: String!
    businessHoursEndTime: String!
    breakScheduleStartTime: String!
    breakScheduleEndTime: String!
    shiftMarginStartTimeInHours: Float!
    shiftMarginEndTimeInHours: Float!
    timezoneId: Int!
    targetEffortInHoursPerWeek: Float!
    regularHoursPerDay: Float!
    overtimeHoursPerDay: Float!
    description: String
    overTimeCoolingPeriod: Int!
    allowAttendanceOutsideRegularHours: Int!
    earlyCheckInOverride: Int!
    enableGraceTime: Int!
    gracePeriodForCheckIn: Int!
    checkoutBufferTime: Int!
    thresholdOverTimeHoursPerDay: Float
    dashboardType: String!
    onlyNameChanged: Int!
    oldStatus: String!
    status: String!
  ): commonResponse!

  deleteWorkScheduleAndWeekOff(
    workScheduleId: Int!
    dashboardType: String!
  ): commonResponse
  addWeekOff(
    workScheduleId: Int!
    dashboardType: String!
    weekOffList: [weekOffListInput]!
  ): commonResponse!
  updateLeaveSettings(
    Allow_Upline_Managers_Approval: String
    Enable_CAMU_Scheduler: String
    Enable_Workflow: String
    Old_Workflow: String
    Enforce_Comment_For_Approval: String
    Enforce_Comment_For_Leave: String
    Enforce_Alternate_Person_For_Leave: String
    Coverage_For_Alternate_Person: String
  ): commonResponse!
  deleteCustomGroupHolidays(holidayIds: [Int]): commonResponse
  addCustomGroupHolidays(holidayData: [holidayData]): commonResponse
  updateCustomGroupHoliday(
    Holiday_Assign_Id: Int!
    Custom_Group_Id: Int!
    Old_Date: String!
    Holiday_Date: String!
    Mandatory: Int!
    Personal_Choice: Int!
    Holiday: Int!
    Description: String
  ): commonResponse
  deleteProjectDetails(projectId: Int!): commonResponse
  addUpdateProjectDetails(
    projectId: Int!
    projectName: String!
    clientName: String!
    managerId: Int!
    locationId: Int!
    status: String!
    description: String!
    employeeId: [Int!]
    customGroupId: Int!
    accreditationId: [Int!]
  ): addUpdateProjectDetailsResponse
  addUpdatePreApprovalRequests(
      preApprovalId:Int!
      preApprovalType:String!
      formId: Int!
      employeeId:Int
      duration:String!
      period:String!
      startDate:String!
      overTimeHours: Float
      endDate:String!
      totalDays:Float!
      fileName: String
      fileSize: String
      reason:String
      status:String!):commonResponse
      addUpdateCompOffRules(
      CustomGroup_Id: Int
      Configuration_Id: Int
      Comp_Off_Expiry_Type: String!
      Comp_Off_Expiry_Days: Int
      Comp_Off_Encashment: String!
      Comp_Off_Balance_Approval: String
      Encashment_Mode: String
      Comp_Off_Threshold: String
      Allow_Half_Day_Comp_Off_Credit: String
      Fixed_Regular_Hours: Float
      Minimum_Hours_For_Half_Day_Comp_Off: Float
      Salary_Type: String!
      Work_Day_Type: String!
      Status: String!
      Workflow_Approval: Int
      Comp_Off_Applicability_For_Overtime_Hours: String
      Minimum_OT_Hours_For_Full_Day_Comp_Off: Float
      Minimum_OT_Hours_For_Half_Day_Comp_Off: Float
  ):commonResponse
  addUpdateSpecialWages(
    Configuration_Id: Int!
    CustomGroup_Id: Int
    Attendance_Required :String
    Wage_Factor: Float
    Salary_Type: String!
    Special_Work_Days: String!
    Status: String!
  ):commonResponse
  addUpdateOvertimeConfiguration(
    Configuration_Id: Int!
    CustomGroup_Id: Int
    Wage_Factor: Float
    Salary_Type: String!
    Special_Work_Days: String!
    Status: String!
    Overtime_Type: String!
    overtimeFixedAmount: Float
    formId: Int
  ):commonResponse
    addUpdateLopRecoverySettings(
    Lop_Settings_Id: Int!
    CustomGroup_Id: Int
    Attendance_Shortage_Applicable :String!
    Auto_LOP_Applicable: String!
    Workflow_Id: Int!
    Late_Attendance_Applicable: String!
    Configuration_Status: String!
  ):commonResponse
   addUpdateorganizationGroup(
   organizationGroupId: Int!
   description: String
   level: Int
   Status: String
   organizationGroupCode: String
   organizationGroup: String!):commonResponse
   updateOrganizationStatus(
   organizationGroupId: Int!
    Status: String!):commonResponse
  addUpdatePreApprovalSettings(
    preApprovalConfigurationId:Int! 
    preApprovalType:String! 
    coverage:String! 
    customGroupId:Int 
    period:String! 
    noOfPreApprovalRequest:Int!
    restrictSandwich:String!
    restrictSandwichFor:String 
    advanceNotificationDays:Int! 
    workflowId:Int! 
    status:String!
    documentUpload: String!
    maxDaysForDocumentUpload: Float
    maxDaysAllowedPerRequest: Float
    typeOfDay: String!): commonResponse
  deleteDesignationDetails(designationId: Int!): commonResponse
  addUpdateDesignationDetails(
    designationId: Int!
    designationName: String!
    designationCode: String
    level: Int
    probationDays: Int!
    description: String!
    gradeId: Int!
    employeeConfirmation: String!
    attendanceEnforcedPayment: Int!
    status: String!
    attendanceEnforcedGeoLocation: Int!
    noticePeriodDaysWithinProbation: Int!
    noticePeriodDaysAfterProbation: Int!
    noticePeriodPayByEmployer: String
    noticePeriodPayByEmployee: String
    accreditationId: [Int!]
  ): commonResponse
   addUpdateEmployeeTravelSetting(
    employeeTravelSettingId: Int
    emailRecipients: [String]
    additionalRecipients: [String]
    formId: String!
  ): commonResponse
  addUpdateCustomEmailTemplate(templateId: Int!
    templateName: String!
    subjectFields: [String]
    subjectContent:String
    templateContent: String!
    templateFields: [String]
    categoryId: Int!
    categoryTypeId: Int
    formId: Int!
    visibility: String!
    toEmails: [String]
    attachedFiles:[String]
    ccEmails: [String]
    defaultTemplate:String
    bccEmails: [String]
    additionalEmails: [Int]
    externalEmails: [String]
    senderName: String
): commonResponse
  updateHolidaySettings(holidaySettings: String, displayPersonalChoiceHolidays: Int): commonResponse
   addUpdateAirTicketSetting(
    formId: Int!
    airTicketSettingId: Int
    city: String!
    country: String!
    airTicketingCategory: String!
    status: String!
    infantAmount: Float
    childAmount: Float
    adultAmount: Float!
  ): commonResponse!
  updateRosterManagmentSetting(dynamicWeekOff: Int!,swapApprovalRestriction: String!
        maxSwapRequestsPerMonth: Int, allowPastShiftSwaps: String!, maxShiftSwapDays: Int): commonResponse
  updateFormLevelCoverage(Coverage: String!,Coverage_Id: Int!,formName: String!): commonResponse
  bulkImportCustomGroupHolidays(
    holidayData: [holidayDatas]
  ): bulkImportCustomGroupHolidaysResponse
  bulkImportLocationHolidays(
    holidayDataLocation: [holidayDataLocations]
  ): bulkImportLocationHolidaysResponse
  addLocationHolidays(
    holidayDataLocation: [holidayDataLocation]
  ): commonResponse
  updateLocationHoliday(
    Holiday_Assign_Id: Int!
    Holiday_Id: Int!
    Location_Id: Int!
    Old_Date: String!
    Holiday_Date: String!
    Mandatory: Int!
    Personal_Choice: Int!
    Holiday: Int!
    Description: String
  ): commonResponse
  deleteLocationHolidays(holidayIds: [Int!]): commonResponse
  addUpdateHoliday(
    Holiday_Id: Int
    Holiday_Name: String!
    Description: String
  ): commonResponse
  deleteHolidays(holidayIds: [Int!]): commonResponse!
  triggerBulkInviteEmployees(employeeData: [employeeData]): commonResponse
  updateUserAccountDetails(
    email: String
    mobileNumber: String
    mobileNumberCountryCode: String
    employeeId: Int!
    updateInvitationStatus: Int
    type: String
  ): commonResponse
  updateUserAccounts(
    allowUserSignIn: Int!
    enableSignInWithMobileNo: Int!
    revokeRefreshToken: Int
    employeeId: Int!
  ): commonResponse
  importEmployeeData(
    typeOfEmployeeDetails: String!
    typeOfImport: String!
    employeeData: String!
    isAsync: Int!
  ): importEmployeeDataResponse
  updateShortTimeOffSettings(
    Maximum_Limit: Int
    Period: String
    Max_Short_Time_Per_Request: Int
    Min_Short_Time_Per_Request: Int
    Frequency: Int
    Gender: String
    Leave_Activation_Days: Int
    Limit_By: String
    Maximum_Duration: Int
    Minimum_Duration: Int
    Enforce_Alternate_Person: String
    Coverage_For_Alternate_Person: String
  ): commonResponse
  deleteTimesheetActivity(
  timesheetId: Int
  selfService: Int!
  requestId: Int
  parentDelete: Int!
  detailsBytimeId: Int 
):commonResponse
timesheetApprovalWithdraw(requestId: Int! selfService: Int!):commonResponse
timesheetSubmitForApproval(requestId: Int! selfService: Int!):commonResponse
timesheetApprovalReturn(requestId: Int! selfService: Int! returnedComment: String!):commonResponse
  updateOnDutySettings (
    Coverage: String
    Custom_Group_Id: Int
  ): commonResponse
  addUpdateBusinessUnit(
    businessUnitId: Int!
    businessUnit: String!
    status:String!
    level: Int
    description:String
    oldStatus:String
    isAdd:Int!
    businessUnitCode: String
    businessUnitParentId: Int
  ): commonResponse
  sendCommonEmail(
    Template: String
    Destinations: [DestinationEmail]!
    DefaultTemplateData: String
  ):commonResponse!
  addUpdateProjectActivities(
    activityId: Int!
    activityName:String!
    isBillable: String!
    description: String
  ): commonResponse
  deleteProjectActivity(activityId: Int!): commonResponse
  addUpdateActivitiesToProject(
    projectActivityId:Int!
    projectId: Int!
    activityId:Int!
    isBillable: String!
    activityFrom: String
    activityTo: String
    description: String
  ): commonResponse
  deleteActivityAssociatedWithProject(projectActivityId: Int!): commonResponse
  importProjectActivities(
    projectActivityData: [projectActivity]
  ): importProjectActivitiesResponse
  addTimeSheetPrevWeek(
    selfService: Int!
    prevWeekEndingDate: String! 
    employeeId: Int!
    weekEndingDate: String!
    timesheetType: String!
    ): commonResponse
  addUpdateempTimesheet(
    selfService: Int!
    timesheetId: Int!
    requestId: Int
    employeeId: Int!
    weekEndingDate: String!
    projectId: Int!
    timesheetType: String!
    projectActivityId: Int!
    description: String
    day1: Float
    day2: Float
    day3: Float
    day4: Float
    day5: Float
    day6: Float
    day7: Float
    dayValue: Int!
    dayDetails: [dayDetails]
      ): timesheetResponse

  cloneProject(
    cloneOption: String!
    projectId: Int!
    projectName: String!
    ): cloneProjectResponse

  importAndMapProjectWithActivities(
    projectAndActivityData: [projectAndActivity]
  ): importProjectActivitiesResponse

  overrideEmployeeLeaves(
    overrideDetails: [overrideInputs]!
  ):commonResponse!

  addUpdateLocation(
    locationId: Int!
    locationName: String!
    locationCode: String
    locationType: String!
    street1: String!
    street2: String
    cityId: Int!
    stateId: Int!
    countryCode: String!
    pincode: String
    phone: String
    notes: String
    fax: String
    currencySymbol: String!
    description: String
    orgId: Int
    zoneId: Int!
    currencyId: Int
    externalLocationId: Int
    locationStatus: String!
    oldStatus: String
    barangay: String
    barangayId: Int
    region: String
  ):commonResponse!
  addUpdateRoom(
    Room_Id: Int
    Room_No: String!
    Description: String
  ): commonResponse
  deleteRoom(Room_Id: Int!): commonResponse

  deleteLocation(
    locationId: Int!
  ):commonResponse

addUpdateCustomField(
  Custom_Field_Id: Int
  Custom_Field_Name: String
  Custom_Field_Type: String
  Min_Validation: Int
  Max_Validation: Int
  Validation_Id: Int
  Url_Link: String
  Dropdown_Values: [String]
  Roles_Id: [Int]
  Form_Id_Associated: [Form_Id_Associated]
  Visibility_Condition: Visibility_Condition
  ): commonResponse

addUpdateEmployeeIdPrefixSettings(
  empPrefixSettingId: Int!
  serviceProviderId: Int
  prefix: String
  suffix: String
  noOfDigits: Int!
  nextNumber: Int!
  status: String!
  formId:Int!
  ): commonResponse

updateEmployeeIdPrefixConfig(
  empPrefixConfigId: Int!
  isEnabled: Boolean!
  configLevel: String!
  formId: Int!
  ): commonResponse
}

input Visibility_Condition {
  Custom_Field_Id: Int!
  Type: String!
  Value: String
}

input Form_Id_Associated {
  Form_Id: Int!
  Integration_Mapping_Key: String
  Mandatory: String!
}

input overrideInputs{
  employeeId: Int!
  leaveTypeId: Int!
  currentYearEligibleDays: Float!
  coBalance:Float!
  leaveOverrideReason:String
}

input dayDetails{
  detailsBytimeId: Int
  roomId: Int
  day: Int
  notes: String
  totalHours: Float
  startTime: String
  endTime: String
}

input DestinationEmail{
  Destination: EmailAddress!
  ReplacementTemplateData: String
}

input EmailAddress{
  ToAddresses: [String]
  CcAddresses: [String]
  BccAddresses: [String]
}

type importEmployeeDataResponse {
  errorCode: String
  message: String
  validationFails: [validationFail]
}

type validationFail {
  fieldName: String
  field: String
  reason: String
}

input employeeData {
  firstName: String!
  lastName: String
  employeeId: Int!
  email: String
  phone: String
  requestType: String!
}

input holidayDataLocation {
  Holiday_Id: Int!
  Start_Date: String!
  End_Date: String!
  Mandatory: Int!
  Personal_Choice: Int!
  Holiday: Int!
  Location_Id: Int!
  Description: String
}

input holidayDataLocations {
  Index: Int!
  Holiday_Id: Int!
  Start_Date: String!
  End_Date: String!
  Mandatory: Int!
  Personal_Choice: Int!
  Holiday: Int!
  Location_Id: Int!
  Description: String
}

input holidayDatas {
  Index: Int!
  Holiday_Id: Int!
  Start_Date: String!
  End_Date: String!
  Mandatory: Int!
  Personal_Choice: Int!
  Holiday: Int!
  Custom_Group_Id: Int!
  Description: String
}

input holidayData {
  Holiday_Id: Int!
  Start_Date: String!
  End_Date: String!
  Mandatory: Int!
  Personal_Choice: Int!
  Holiday: Int!
  Custom_Group_Id: Int!
  Description: String
}

type listWorkScheduleDetailsResponse {
  errorCode: String
  message: String
  workScheduleDetails: [listWorkScheduleDetails]
}

type bulkImportLocationHolidaysResponse {
  errorCode: String
  message: String
  locationValidationFailed: [locationValidationFailed]
}

type locationValidationFailed {
  Location_Id: Int
  Holiday_Date: String
}

type bulkImportCustomGroupHolidaysResponse {
  errorCode: String
  message: String
  validationFailed: [validationFailed]
}

type validationFailed {
  Custom_Group_Id: Int
  Holiday_Date: String
}

type listWorkScheduleDetails {
  workScheduleId: Int
  workScheduleCode: String
  workSchedule: String
  businessHoursStartTime: String
  businessHoursEndTime: String
  breakScheduleStartTime: String
  breakScheduleEndTime: String
  targetEffortInHoursPerWeek: Float
}

type commonResponse {
  errorCode: String
  message: String
}
type timesheetResponse{
 errorCode: String
  message: String
optionsObject: optionsType
}
type optionsType{
requestId : Int
timesheetId: Int
}
input weekOffListInput {
  dayId: Int!
  enableWeek1: Int!
  durationForWeek1: Float!
  enableWeek2: Int!
  durationForWeek2: Float!
  enableWeek3: Int!
  durationForWeek3: Float!
  enableWeek4: Int!
  durationForWeek4: Float!
  enableWeek5: Int!
  durationForWeek5: Float!
  excludeLastWeek: String!
}

input customComponentsInput {
  componentName: String!
  componentInputs: String!
}

type addWorkScheduleResponse {
  errorCode: String
  message: String
  workScheduleId: Int!
  lateAttendance: String!
}
type addUpdateProjectDetailsResponse{
  errorCode: String
  message: String
  projectId: Int
}
input projectActivity{
  activityName: String!,
  isBillable: String!,
  description: String
}

type importProjectActivitiesResponse{
  errorCode: String
  message: String
  validationError: String
}

type cloneProjectResponse{
  errorCode: String
  message: String
  projectDetails: projectDetails
}
type projectDetails{
  Project_Id: Int 
  Project_Name: String
  Description: String
  Client_Name: String
  Location_Id: Int
  Manager_Id: Int
  Employee_Id: [Int]
  Custom_Group_Id: [Int]
  Accreditation_Id: [Int]
  Status: String
}

input projectAndActivity{
  projectId: Int!,
  activityId: Int!
  isBillable: String!,
  activityFrom: String!
  activityTo: String!
}
schema {
  query: Query
  mutation: Mutation
}
