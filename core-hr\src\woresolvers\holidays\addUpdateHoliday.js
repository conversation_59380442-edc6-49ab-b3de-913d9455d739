//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { UserInputError, ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../../../common/tablealias');
const { formName, formIds } = require('../../../common/appconstants');
const { updateDataSetupDashboard } = require('../../../common/commonfunctions');
const commonValidation = require('../../../common/commonvalidation');

module.exports.addUpdateHoliday = async (parent, args, context, info) => {
  console.log('Inside addUpdateHoliday function');
  let organizationDbConnection;
  let validationError = {};
  let loginEmployeeId = context.Employee_Id;
  try {
    organizationDbConnection = knex(context.connection.OrganizationDb);

    //Form Access check for add and update holiday
    let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, formName.holidays, '', 'UI');

    if ((Object.keys(checkRights).length > 0 && checkRights.Role_Add === 1) || checkRights.Role_Update === 1) {
      //validation input for holidays

      //Holiday Name
      if (args.Holiday_Name) {
        if (!commonValidation.holidayDescription(args.Holiday_Name)) {
          validationError['IVE0295'] = commonLib.func.getError('', 'IVE0295').message1;
          // length between 3 and 100
        } else if (!commonValidation.checkLength(args.Holiday_Name, 3, 100)) {
          validationError['IVE0295'] = commonLib.func.getError('', 'IVE0295').message2;
        }
      } else {
        validationError['IVE0295'] = commonLib.func.getError('', 'IVE0295').message3;
      }

      //Description
      if (args.Description) {
        if (!commonValidation.holidayDescription(args.Description)) {
          validationError['IVE0062'] = commonLib.func.getError('', 'IVE0062').message1;
        } else if (!commonValidation.checkLength(args.Holiday_Name, 1, 500)) {
          validationError['IVE0062'] = commonLib.func.getError('', 'IVE0062').message2;
        }
      }

      if (Object.keys(validationError).length == 0) {
        let loginEmployeeCurrentDateTime = await commonLib.func.getEmployeeTimeZone(loginEmployeeId, organizationDbConnection, 1);

        let holidayId = args.Holiday_Id;
        let updateDetails = {
          Holiday_Name: args.Holiday_Name,
          Description: args.Description,
        };

        if (holidayId && checkRights.Role_Update === 1) {
          updateDetails.Updated_On = loginEmployeeCurrentDateTime;
          updateDetails.Updated_By = loginEmployeeId;
          let sameHolidayExists = await checkIfHolidayExists(organizationDbConnection, args.Holiday_Name, holidayId)
            if(!sameHolidayExists){
              return organizationDbConnection(ehrTables.holiday)
              .update(updateDetails)
              .where('Holiday_Id', holidayId)
              .then(output => {
                if (output) {
                  organizationDbConnection ? organizationDbConnection.destroy() : null;
                  return {
                    errorCode: '',
                    message: 'Holiday was updated successfully.',
                  };
                } else {
                  throw 'CGH0131';
                }
              })
              .catch(catchError => {
                console.log('Error while updating holiday', catchError);
                let errResult = commonLib.func.getError(catchError, 'CGH0131');
                //Destroy DB connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                //Return error response
                throw new ApolloError(errResult.message, errResult.code);
              });
            }else{
              //Holiday Name already exists
              console.log('Holiday name already exists')
              throw 'CGH0134'
            }
        }

        // Insert if there is no holiday id
        else if (checkRights.Role_Add === 1) {
          updateDetails.Added_On = loginEmployeeCurrentDateTime;
          updateDetails.Added_By = loginEmployeeId;
          let sameHolidayExists = await checkIfHolidayExists(organizationDbConnection, args.Holiday_Name)
          if(!sameHolidayExists){
            return organizationDbConnection(ehrTables.holiday)
              .insert(updateDetails)
              .then(async output => {
                if (output) {
                  //Update the datasetup_dashboard status
                  await updateDataSetupDashboard(organizationDbConnection, null, formIds.holidayTypes, 'Completed')
                  organizationDbConnection ? organizationDbConnection.destroy() : null;
                  return {
                    errorCode: '',
                    message: 'Holiday was inserted successfully.',
                  };
                } else {
                  throw 'CGH0131';
                }
              })
              .catch(catchError => {
                console.log('Error while inserting holiday', catchError);
                let errResult = commonLib.func.getError(catchError, 'CGH0131');
                //Destroy DB connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                //Return error response
                throw new ApolloError(errResult.message, errResult.code);
              });
          }else{
            console.log('Holiday name already exists')
            throw 'CGH0134'
          }
        }
      } else {
        throw 'IVE0000';
      }
    } else {
      console.log('No rights to add or update holiday');
      throw '_DB0111';
    }
  } catch (mainCatchError) {
    console.log('Error in addUpdateHoliday function main block', mainCatchError);
    organizationDbConnection ? organizationDbConnection.destroy() : null;
    if (mainCatchError === 'IVE0000') {
      let errResult = commonLib.func.getError('', 'IVE0000');
      console.log('Validation error in addUpdateHoliday function - ', validationError);
      // return response
      throw new UserInputError(errResult.message, {
        validationError: validationError,
      });
    } else {
      let errResult = commonLib.func.getError(mainCatchError, 'CGH0016');
      // return response
      throw new ApolloError(errResult.message, errResult.code);
    }
  }
};


async function checkIfHolidayExists(organizationDbConnection, holidayName, holidayId){
  return(
    organizationDbConnection(ehrTables.holiday)
    .select('Holiday_Name', 'Holiday_Id')
    .where(function(){
      this.where('Holiday_Name', holidayName)
      if(holidayId){
        this.andWhereNot('Holiday_Id', holidayId)
      }
    })
    .then((data)=>{
      if(data && data.length){
        return true
      }else{
        return false
      }
    })
    .catch((err)=>{
      console.log('Error while checking if the holiday name already exist', err)
      throw err
    })
  )
}
