// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formName } = require('../../common/appconstants');


let organizationDbConnection;
module.exports.retrieveHolidaySettings = async (parent, args, context, info) => {
    try {
        console.log("Inside retrieveHolidaySettings function.")
        let employeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        // check their rights
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, employeeId, formName.holidays, '', 'UI');
        if (Object.keys(checkRights).length > 0 && checkRights.Role_View === 1) {
            return (
                organizationDbConnection(ehrTables.holidaySettings)
                    .select('Holiday_Settings_Type', 'Display_Personal_Choice_Holiday_In_Dashboard')
                    .then((data) => {
                        if(data.length){
                            let holidaySettings = data[0].Holiday_Settings_Type;
                            let displayPersonalChoice = data[0].Display_Personal_Choice_Holiday_In_Dashboard;
                            //destroy the connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            return { errorCode: "", message: "Holiday settings retrieved successfully.", holidaySettings: holidaySettings, displayPersonalChoice: displayPersonalChoice };
                        }else{
                            console.log('No holiday settings data found')
                            throw 'CGH0121'
                        }
                    })
                    .catch((err)=>{
                        console.log('Error in retrieveHolidaySettings .catch() block', err);
                        throw 'CGH0121'
                    })
            )
        }
        else {
            throw '_DB0100';
        }

    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in retrieveHolidaySettings function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'CGH0007');
        throw new ApolloError(errResult.message, errResult.code);
    }
}
