// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formName, formIds } = require('../../common/appconstants');
const {getProjectCoverage}=require('../../common/commonfunctions')

let organizationDbConnection;
module.exports.listProjectDetails = async (parent, args, context, info) => {
    try {
        console.log("Inside listProjectDetails function.")
        let employeeId = context.Employee_Id;
        let inputFormId=args.formId?args.formId:formIds.projects;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        // get the access rights of the login employee for the accreditation form
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, employeeId,'','','UI',false,inputFormId);
        if (Object.keys(checkRights).length > 0 && checkRights.Role_View === 1) {
            //Get project coverage and based on the coverage get employeeid or custom group id
            let projectCoverage = await getProjectCoverage(organizationDbConnection);
            let projectQuery =  "";
            if(projectCoverage){
                //If the project coverage is CUSTOMGROUP or Organization
                if(projectCoverage === "Organization"){
                    projectQuery =  organizationDbConnection(ehrTables.projectDetails)
                    .select('P.Project_Id as projectId', 'P.Project_Name as projectName', 'P.Description as description', 'P.Client_Name as clientName', 'P.Location_Id as locationId',
                    'L.Location_Name as locationName','P.Manager_Id as managerId','P.Added_On as addedOn', 'P.Updated_On as updatedOn', 'P.Status as status',
                    organizationDbConnection.raw("CONCAT_WS(' ',EPI.Emp_First_Name,EPI.Emp_Middle_Name, EPI.Emp_Last_Name) as addedByName"),
                    organizationDbConnection.raw("CONCAT_WS(' ',EPI2.Emp_First_Name,EPI2.Emp_Middle_Name, EPI2.Emp_Last_Name) as updatedByName"),
                    organizationDbConnection.raw("CONCAT_WS(' ',EPI3.Emp_First_Name,EPI3.Emp_Middle_Name, EPI3.Emp_Last_Name) as managerName"))
                    .from(ehrTables.projectDetails+" as P")
                    .leftJoin(ehrTables.location+" as L","P.Location_Id","L.Location_Id")
                    .leftJoin(ehrTables.empPersonalInfo + " as EPI", "EPI.Employee_Id", "P.Added_By")
                    .leftJoin(ehrTables.empPersonalInfo + " as EPI2", "EPI2.Employee_Id", "P.Updated_By")
                    .leftJoin(ehrTables.empPersonalInfo + " as EPI3", "EPI3.Employee_Id", "P.Manager_Id")
                    .groupBy('P.Project_Id').modify((queryBuilder)=>{
                        if (args.formId) {
                            queryBuilder.where('P.Status','Open')
                        }
                    })
                    //If the project coverage is Employee
                } else if (projectCoverage === "Employee"){
                    projectQuery =  organizationDbConnection(ehrTables.projectDetails)
                    .select('P.Project_Id as projectId', 'P.Project_Name as projectName', 'P.Description as description', 'P.Client_Name as clientName', 'P.Location_Id as locationId',
                    'L.Location_Name as locationName','P.Manager_Id as managerId','P.Added_On as addedOn', 'P.Updated_On as updatedOn', 'P.Status as status',
                    organizationDbConnection.raw("CONCAT_WS(' ',EPI.Emp_First_Name,EPI.Emp_Middle_Name, EPI.Emp_Last_Name) as addedByName"), 
                    organizationDbConnection.raw("CONCAT_WS(' ',EPI2.Emp_First_Name,EPI2.Emp_Middle_Name, EPI2.Emp_Last_Name) as updatedByName"),
                    organizationDbConnection.raw("CONCAT_WS(' ',EPI3.Emp_First_Name,EPI3.Emp_Middle_Name, EPI3.Emp_Last_Name) as managerName"),
                    organizationDbConnection.raw('GROUP_CONCAT(DISTINCT EP.Employee_Id) as employeeId'),
                    organizationDbConnection.raw('GROUP_CONCAT( Ename.Emp_First_Name, " ", Ename.Emp_Last_Name) AS `employeeName`'))
                    .from(ehrTables.projectDetails+" as P")
                    .leftJoin(ehrTables.location+" as L","P.Location_Id","L.Location_Id")
                    .leftJoin(ehrTables.empPersonalInfo + " as EPI", "EPI.Employee_Id", "P.Added_By")
                    .leftJoin(ehrTables.empPersonalInfo + " as EPI2", "EPI2.Employee_Id", "P.Updated_By")
                    .leftJoin(ehrTables.empPersonalInfo + " as EPI3", "EPI3.Employee_Id", "P.Manager_Id")
                    .leftJoin(ehrTables.empProject+" as EP", "P.Project_Id","EP.Project_Id")
                    .leftJoin(ehrTables.empPersonalInfo+" as Ename", "EP.Employee_Id","Ename.Employee_Id")
                    .groupBy('P.Project_Id')
                    .modify((queryBuilder)=>{
                        if (args.formId) {
                            queryBuilder.where('EP.Employee_Id',args.employeeId)
                            queryBuilder.where('P.Status','Open')
                        }
                    })
                } 
                else if(projectCoverage === "CUSTOMGROUP"){
                    //If the coverage is custom group then get the custom group details.
                    projectQuery = organizationDbConnection(ehrTables.projectDetails)
                    .from(ehrTables.projectDetails+" as P")
                    .innerJoin(ehrTables.customGroupAssociated+" as CGAF", "CGAF.Parent_Id","P.Project_Id")
                    .modify((queryBuilder)=>{
                        if (args.formId) {
                            queryBuilder.select('P.Project_Id as projectId','P.Project_Name as projectName', 'P.Description as description', 'P.Client_Name as clientName', 'P.Location_Id as locationId');
                        queryBuilder.innerJoin(
                            ehrTables.customGroupEmployees + " as CGE",
                            "CGE.Group_Id",
                            "CGAF.Custom_Group_Id"
                          );
                          queryBuilder.where('CGE.Employee_Id',args.employeeId);
                          queryBuilder.whereIn('CGE.Type',["AdditionalInclusion","Default"]);
                          queryBuilder.groupBy('P.Project_Id','CGE.Employee_Id');
                          queryBuilder.where('P.Status','Open')
                        }
                        else{
                        queryBuilder.select('P.Project_Id as projectId', 'P.Project_Name as projectName', 'P.Description as description', 'P.Client_Name as clientName', 'P.Location_Id as locationId',
                        'L.Location_Name as locationName','P.Manager_Id as managerId','P.Added_On as addedOn', 'P.Updated_On as updatedOn', 'P.Status as status',
                        organizationDbConnection.raw("CONCAT_WS(' ',EPI.Emp_First_Name,EPI.Emp_Middle_Name, EPI.Emp_Last_Name) as addedByName"),
                        organizationDbConnection.raw("CONCAT_WS(' ',EPI2.Emp_First_Name,EPI2.Emp_Middle_Name, EPI2.Emp_Last_Name) as updatedByName"),
                        organizationDbConnection.raw("CONCAT_WS(' ',EPI3.Emp_First_Name,EPI3.Emp_Middle_Name, EPI3.Emp_Last_Name) as managerName"),organizationDbConnection.raw('GROUP_CONCAT(DISTINCT CGAF.Custom_Group_Id) as customGroupId'),organizationDbConnection.raw('GROUP_CONCAT(CEG.Group_Name)  AS `customGroupName`') ,organizationDbConnection.raw('GROUP_CONCAT(DISTINCT EP.Employee_Id) as employeeId'),
                        organizationDbConnection.raw('GROUP_CONCAT( Ename.Emp_First_Name, " ", Ename.Emp_Last_Name) AS `employeeName`'));
                        queryBuilder.leftJoin(ehrTables.location+" as L","P.Location_Id","L.Location_Id");
                        queryBuilder.leftJoin(ehrTables.empProject+" as EP", "P.Project_Id","EP.Project_Id")
                        queryBuilder.leftJoin(ehrTables.empPersonalInfo + " as EPI", "EPI.Employee_Id", "P.Added_By");
                        queryBuilder.leftJoin(ehrTables.empPersonalInfo + " as EPI2", "EPI2.Employee_Id", "P.Updated_By");
                        queryBuilder.leftJoin(ehrTables.empPersonalInfo + " as EPI3", "EPI3.Employee_Id", "P.Manager_Id");
                        queryBuilder.leftJoin(ehrTables.cusEmpGroup+" as CEG","CEG.Group_Id","CGAF.Custom_Group_Id");
                        queryBuilder.leftJoin(ehrTables.empPersonalInfo+" as Ename", "EP.Employee_Id","Ename.Employee_Id")
                        queryBuilder.groupBy('P.Project_Id');
                        queryBuilder.where("CGAF.Form_Id", formIds.projects);
                        }
    
                    })
                    .then((customGroupResult) => {
                        return customGroupResult;
                    })
                    .catch((err)=>{
                        console.log('Error in getting the projectData .catch block.', err);
                        throw('CHR0006');
                    })
                    }
                else{
                    console.log("Invalid project coverage", projectCoverage);
                    throw('CHR0003')
                }
            } else{
                console.log("Project settings data does not exists");
                throw('CHR0002');
            }

            let projectData = await projectQuery.then((data) => {
                return data;
            })
            .catch((err)=>{
                console.log('Error in getting the projectData .catch block.', err);
                throw('CHR0005');
            })
                
                //destroy the connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;

                return { errorCode: "", message: "Project details retrieved successfully.", projectDetails: projectData, projectCoverage: args.formId?'':projectCoverage };
        }
        else {
            throw '_DB0100';
        }

    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in listProjectDetails function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'CHR0004');
        throw new ApolloError(errResult.message, errResult.code);
    }
}
