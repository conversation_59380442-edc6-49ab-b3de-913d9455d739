//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { UserInputError, ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../../../common/tablealias');
const { getAttendanceExist, getCompensatoryOffExist, getLeaveExist, getPaySlipExist, checkSameHolidayLocationExists } = require('../../../common/commonfunctions');
const { formName } = require('../../../common/appconstants');
const { validateHolidayInputs } = require('../../../common/holidayInputValidation')

module.exports.updateLocationHoliday = async (parent, args, context, info) => {
    console.log('Inside updateLocationHoliday function');
    let organizationDbConnection;
    let validationError = {};
    let loginEmployeeId = context.Employee_Id;
    let orgCode = context.Org_Code
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);

        //Form Access check for updating location holiday
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, formName.holidays, '', 'UI');
        if (Object.keys(checkRights).length > 0 && checkRights.Role_Update === 1) {
            validationError = await validateHolidayInputs(args,0,1);
            if (Object.keys(validationError).length == 0) {
                let loginEmployeeCurrentDateTime = await commonLib.func.getEmployeeTimeZone(loginEmployeeId, organizationDbConnection, 1);
                return (
                    organizationDbConnection
                        .transaction(async (trx) => {
                            let updateDetails = args
                            let locationId = args.Location_Id
                            //fetch the employeeIds with the locationId
                            return (
                                organizationDbConnection(ehrTables.empJob)
                                    .select("Employee_Id")
                                    .transacting(trx)
                                    .where('Location_Id', locationId)
                                    .then(async (data) => {
                                        //Merge the employeeId with the updateDetails
                                        let result = data.map(a => a.Employee_Id);
                                        updateDetails.Employee_Id = result
                                        updateDetails.Updated_By = loginEmployeeId
                                        updateDetails.Modified_Date = loginEmployeeCurrentDateTime

                                        //validation to check if same date is added for the location
                                        let holidayCheckResult = await checkSameHolidayLocationExists(updateDetails, organizationDbConnection, 1);
                                        if (!holidayCheckResult) {
                                            throw 'CGH0114'
                                        }
                                        if (holidayCheckResult.length && args.Old_Date !== args.Holiday_Date) {
                                            // It contains same date
                                            throw 'CGH0115'
                                        }

                                        //validation to check if date is in payslip
                                        let paySlipResult = await getPaySlipExist(updateDetails, organizationDbConnection, orgCode, 1)
                                        if (!paySlipResult) {
                                            throw 'CGH0116'
                                        }
                                        if (paySlipResult.length) {
                                            console.log('Payslip contains date')
                                            throw 'CGH0117'
                                        }

                                        //validation to check if date is in emp_attendance table
                                        let attendanceResult = await getAttendanceExist(updateDetails, organizationDbConnection, 1);
                                        if (!attendanceResult) {
                                            throw 'CGH0105'
                                        }
                                        if (attendanceResult.length) {
                                            console.log('Attendance contains the existing date')
                                            throw 'CGH0103'
                                        }

                                        //validation to check if date is in compensatory table
                                        let compensatoryResult = await getCompensatoryOffExist(updateDetails, organizationDbConnection, 1)
                                        if (!compensatoryResult) {
                                            throw 'CGH0106'
                                        }
                                        if (compensatoryResult.length) {
                                            console.log('Compensatory contains the existing date')
                                            throw 'CGH0103'
                                        }

                                        //validation to check if date is in leave table
                                        let employeeLeaveResult = await getLeaveExist(updateDetails, organizationDbConnection, 1)
                                        if (!employeeLeaveResult) {
                                            throw 'CGH0107'
                                        }
                                        if (employeeLeaveResult.length) {
                                            console.log('Leave contains the existing date')
                                            throw 'CGH0103'
                                        }
                                        
                                        let Holiday_Assign_Id = args.Holiday_Assign_Id
                                        //removing Holiday_Assign_Id, Employee_ID and Holiday_Id
                                        delete updateDetails.Holiday_Assign_Id
                                        delete updateDetails.Holiday_Id
                                        delete updateDetails.Employee_Id
                                        delete updateDetails.Old_Date
                                        return (
                                            organizationDbConnection(ehrTables.holidayAssignment)
                                                .update(updateDetails)
                                                .where('Holiday_Assign_Id', Holiday_Assign_Id)
                                                .transacting(trx)
                                                .then(() => {
                                                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                                                    return { errorCode: "", message: "Location holiday have been updated successfully." };
                                                })
                                        )
                                    })
                            )
                        })
                        .catch((catchError) => {
                            console.log('Error in updateLocationHoliday .catch() block', catchError);
                            let errResult = commonLib.func.getError(catchError, 'CGH0129');
                            //Destroy DB connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            //Return error response
                            throw new ApolloError(errResult.message, errResult.code);
                        })
                );
            }
            else {
                throw 'IVE0000';
            }
        } else {
            console.log('No rights to update location holiday');
            throw '_DB0102';
        }
    } catch (mainCatchError) {
        console.log('Error in updateLocationHoliday function main block', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        if (mainCatchError === 'IVE0000') {
            let errResult = commonLib.func.getError('', 'IVE0000');
            console.log('Validation error in updateLocationHoliday function - ', validationError);
            // return response
            throw new UserInputError(errResult.message, { validationError: validationError });
        } else {
            let errResult = commonLib.func.getError(mainCatchError, 'CGH0014');
            // return response
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
}



