module.exports.graphql = (event, context, callback) => {
    context.callbackWaitsForEmptyEventLoop = false; //to send the response immediately when callback executes
    
    var { ApolloServer, gql } = require('apollo-server-lambda');
    var { resolvers } = require('./roresolver');
    const fs = require('fs');
    const typeDefs = gql(fs.readFileSync(__dirname.concat('/roschema.graphql'), 'utf8'));
    // require common hrapp-corelib functions
    const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;

    const server = new ApolloServer({
        typeDefs,
        resolvers,
        context: async ({ event }) => {
            let contextData = await commonLib.func.getContextDataWithoutEmployeeId(event, 1, 'wo');
            contextData.Employee_Id = event.headers.Employee_Id ? event.headers.Employee_Id: 1; 
            //return header to resolver function
            return {...contextData};
        }
    });

    const handler = server.createHandler({
        cors: {
            method: 'POST',
            allowHeaders: '*'
        }
    });
    function callbackFilter(error, output) {
        // We are appending the idToken and refreshToken in the response. While running this in local this is not returning the response
        // so here checked the stagename as local or not. If it is local then we will no append the token response. 
        // Otherwise token response will be append and response will be returned
        // If any doubts check this task #3794
        if(process.env.stageName !== 'local'){
            // parse the response data
            let responseData = JSON.parse(output.body);
            output.body = JSON.stringify(responseData);
        }
        output.headers['Access-Control-Allow-Origin'] = '*';
        output.headers['Access-Control-Allow-Credentials'] = true;
        callback(error, output);
    }
    return handler(event, context, callbackFilter);
};
